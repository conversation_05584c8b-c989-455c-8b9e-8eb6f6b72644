using System;
using System.Collections.Generic;
using ORMTools;

namespace ORMTools.Test
{
    /// <summary>
    /// 测试用的示例实体类
    /// </summary>
    public class TestEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 用户邮箱地址
        /// </summary>
        public string Email { get; set; } = "";

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 可空的更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 分数数组
        /// </summary>
        public int[] Scores { get; set; } = new int[0];

        /// <summary>
        /// 可空的描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 唯一标识符
        /// </summary>
        public Guid UniqueId { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public decimal Balance { get; set; }

        /// <summary>
        /// 评分
        /// </summary>
        public double Rating { get; set; }
    }

    /// <summary>
    /// 另一个测试实体类
    /// </summary>
    public class ProductEntity
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public string ProductId { get; set; } = "";

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; } = "";

        /// <summary>
        /// 价格
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        public int Stock { get; set; }

        /// <summary>
        /// 分类列表
        /// </summary>
        public List<int> CategoryIds { get; set; } = new List<int>();

        /// <summary>
        /// 属性数组
        /// </summary>
        public string[] Attributes { get; set; } = new string[0];

        /// <summary>
        /// 是否上架
        /// </summary>
        public bool IsPublished { get; set; }
    }

    /// <summary>
    /// 测试程序主类
    /// </summary>
    public class TestProgram
    {
        /// <summary>
        /// 测试程序入口点
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("=== ORMTools CSV生成测试 ===");
            Console.WriteLine("请选择测试模式:");
            Console.WriteLine("1. 基础实体类测试");
            Console.WriteLine("2. ComfyUI实体类分析");
            Console.Write("请输入选择 (1-2): ");

            string choice = Console.ReadLine() ?? "1";

            try
            {
                switch (choice)
                {
                    case "2":
                        Console.WriteLine("\n启动ComfyUI实体类分析...");
                        ORMTools.ComfyUI.ComfyUIAnalyzer.AnalyzeComfyUIEntities();
                        break;
                    case "1":
                    default:
                        Console.WriteLine("\n启动基础实体类测试...");
                        RunBasicEntityTests();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 运行基础实体类测试
        /// </summary>
        private static void RunBasicEntityTests()
        {
            // 测试TestEntity
            Console.WriteLine("\n1. 测试TestEntity...");
            string testEntityCsv = ORMEntityAnalyzer.GenerateEntityCsv(typeof(TestEntity), ".");
            Console.WriteLine($"TestEntity CSV文件已生成: {testEntityCsv}");

            // 测试ProductEntity
            Console.WriteLine("\n2. 测试ProductEntity...");
            string productEntityCsv = ORMEntityAnalyzer.GenerateEntityCsv(typeof(ProductEntity), ".");
            Console.WriteLine($"ProductEntity CSV文件已生成: {productEntityCsv}");

            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("请检查生成的CSV文件内容。");

            // 显示CSV文件内容
            Console.WriteLine("\n=== TestEntity.csv 内容预览 ===");
            if (System.IO.File.Exists("TestEntity.csv"))
            {
                string[] lines = System.IO.File.ReadAllLines("TestEntity.csv");
                for (int i = 0; i < lines.Length && i < 10; i++)
                {
                    Console.WriteLine($"第{i+1}行: {lines[i]}");
                }
            }

            Console.WriteLine("\n=== ProductEntity.csv 内容预览 ===");
            if (System.IO.File.Exists("ProductEntity.csv"))
            {
                string[] lines = System.IO.File.ReadAllLines("ProductEntity.csv");
                for (int i = 0; i < lines.Length && i < 10; i++)
                {
                    Console.WriteLine($"第{i+1}行: {lines[i]}");
                }
            }
        }
    }
}
