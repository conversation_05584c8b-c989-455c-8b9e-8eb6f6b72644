﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.GameService
{

    class TimeTools
    {
        /// <summary>
        /// 获取本月开始时间时间秒
        /// </summary>
        /// <returns></returns>
        public static long getMonthStartSTime()
        {
            return getSTime()- getMonthSTime();
        }
        /// <summary>
        /// 获取本月时间秒
        /// </summary>
        /// <returns></returns>
        public static long getMonthSTime()
        {
            return (getNowTime().Day - 1) * 86400 + getSTimeOnDay();
        }
        /// <summary>
        /// 获取本周开始时间时间秒
        /// </summary>
        /// <returns></returns>
        public static long getWeekStartSTime()
        {
            return getSTime() - getWeekSTime();
        }
        /// <summary>
        /// 获取本周时间秒
        /// </summary>
        /// <returns></returns>
        public static long getWeekSTime()
        {
            return ((byte)getNowTime().DayOfWeek-1)*86400 + getSTimeOnDay();
        }
        /// <summary>
        /// 获取当前时间秒
        /// </summary>
        /// <returns></returns>
        public static long getSTime()
        {
            return getNowTime().Ticks / 10000000;
        }
        /// <summary>
        /// 获取当前时间毫秒
        /// </summary>
        /// <returns></returns>
        public static long getMSTime()
        {
            return getNowTime().Ticks / 10000;
        }
        /// <summary>
        /// 获取当日秒数
        /// </summary>
        /// <returns></returns>
        public static long getSTimeOnDay()
        {
            return getHMToS();
        }
        /// <summary>
        /// 获取当日开始秒数
        /// </summary>
        /// <returns></returns>
        public static long getStartSTimeOnDay()
        {
            return getNowTime().Date.Ticks / 10000000;
        }
        /// <summary>
        /// 获取当日开始毫秒数
        /// </summary>
        /// <returns></returns>
        public static long getMSTimeOnDay()
        {
            return getNowTime().Date.Ticks / 10000;
        }
        /// <summary>
        /// 获取当日时分秒时间秒
        /// </summary>
        /// <returns></returns>
        public static long getHMToS()
        {
            return getNowTime().TimeOfDay.Ticks / 10000000; 
            //return 29000;
        }
        /// <summary>
        /// 获取当日时分秒时间毫秒
        /// </summary>
        /// <returns></returns>
        public static long getHMToMS()
        {
            return getNowTime().TimeOfDay.Ticks / 10000;
            //return 29000;
        }
        /// <summary>
        /// 获取时间戳 1970年1月1日（秒）
        /// </summary>
        /// <returns></returns>
        public static long GetTimeStamp()
        {
            TimeSpan ts = getNowTime() - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds);
        }
        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public static DateTime getNowTime()
        {
            return DateTime.UtcNow.AddSeconds(timePlus);
            //return DateTime.Now.AddSeconds(timePlus);
            //return DateTime.UtcNow.AddSeconds(-16300);
        }
        public static int timePlus = 28800;

        public static ulong getRefreshTime(ulong lastTime, ulong refreshTime, out int refreshTimes)
        {
            var time = (ulong)getSTime() - lastTime;
            refreshTimes = (int)(time / refreshTime);
            return lastTime + (ulong)refreshTimes * refreshTime;
        }
    }
}
