using System;
using System.IO;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// 文件上传功能测试类
    /// </summary>
    public class FileUploadTest
    {
        /// <summary>
        /// 测试文件上传功能
        /// </summary>
        public static async Task TestFileUpload()
        {
            Console.WriteLine("=== 文件上传功能测试 ===");
            
            try
            {
                // 测试创建上传目录
                TestCreateDirectories();
                
                // 测试文件移动功能
                await TestFileMoveFunction();
                
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试创建目录功能
        /// </summary>
        private static void TestCreateDirectories()
        {
            Console.WriteLine("测试创建上传目录...");
            
            var uploadDir = Path.Combine(AppContext.BaseDirectory, "uploads");
            var tempDir = Path.Combine(AppContext.BaseDirectory, "temp");
            
            if (!Directory.Exists(uploadDir))
            {
                Directory.CreateDirectory(uploadDir);
                Console.WriteLine($"创建上传目录: {uploadDir}");
            }
            else
            {
                Console.WriteLine($"上传目录已存在: {uploadDir}");
            }
            
            if (!Directory.Exists(tempDir))
            {
                Directory.CreateDirectory(tempDir);
                Console.WriteLine($"创建临时目录: {tempDir}");
            }
            else
            {
                Console.WriteLine($"临时目录已存在: {tempDir}");
            }
        }

        /// <summary>
        /// 测试文件移动功能
        /// </summary>
        private static async Task TestFileMoveFunction()
        {
            Console.WriteLine("测试文件移动功能...");
            
            // 创建测试文件
            var testFileName = "test_upload.txt";
            var testFilePath = Path.Combine(AppContext.BaseDirectory, testFileName);
            var testContent = "这是一个测试文件，用于验证文件上传和移动功能。\n创建时间: " + DateTime.Now.ToString();
            
            await File.WriteAllTextAsync(testFilePath, testContent);
            Console.WriteLine($"创建测试文件: {testFilePath}");
            
            // 测试移动到uploads目录
            var targetPath = Path.Combine(AppContext.BaseDirectory, "uploads", testFileName);
            
            if (File.Exists(testFilePath))
            {
                if (File.Exists(targetPath))
                {
                    File.Delete(targetPath);
                }
                
                File.Move(testFilePath, targetPath);
                Console.WriteLine($"文件移动成功: {testFileName} -> uploads/{testFileName}");
                
                // 验证文件内容
                var movedContent = await File.ReadAllTextAsync(targetPath);
                if (movedContent == testContent)
                {
                    Console.WriteLine("文件内容验证成功！");
                }
                else
                {
                    Console.WriteLine("文件内容验证失败！");
                }
                
                // 清理测试文件
                File.Delete(targetPath);
                Console.WriteLine("清理测试文件完成");
            }
            else
            {
                Console.WriteLine("测试文件创建失败");
            }
        }

        /// <summary>
        /// 显示文件上传功能使用说明
        /// </summary>
        public static void ShowUsageInstructions()
        {
            Console.WriteLine("\n=== 文件上传功能使用说明 ===");
            Console.WriteLine("1. 启动服务器后，访问 http://localhost:端口/upload.html");
            Console.WriteLine("2. 支持的功能:");
            Console.WriteLine("   - 单文件上传（支持断点续传）");
            Console.WriteLine("   - 文件夹批量上传");
            Console.WriteLine("   - HTML文件移动");
            Console.WriteLine("   - 上传进度查询");
            Console.WriteLine("\n3. API端点:");
            Console.WriteLine("   - POST /api/upload - 文件上传（断点续传）");
            Console.WriteLine("   - POST /api/upload/folder - 文件夹上传");
            Console.WriteLine("   - POST /api/move - 文件移动");
            Console.WriteLine("   - GET /api/upload/progress?sessionId=xxx - 查询上传进度");
            Console.WriteLine("\n4. 文件存储位置:");
            Console.WriteLine("   - 上传文件: ./uploads/");
            Console.WriteLine("   - 临时文件: ./temp/");
            Console.WriteLine("\n5. 断点续传原理:");
            Console.WriteLine("   - 使用Content-Range头指定字节范围");
            Console.WriteLine("   - 服务器维护上传会话状态");
            Console.WriteLine("   - 支持网络中断后继续上传");
            Console.WriteLine("\n6. 安全特性:");
            Console.WriteLine("   - 文件大小验证");
            Console.WriteLine("   - 文件完整性检查");
            Console.WriteLine("   - 路径安全验证");
            Console.WriteLine("   - CORS跨域支持");
        }

        /// <summary>
        /// 生成测试文件
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="sizeInMB">文件大小（MB）</param>
        public static async Task GenerateTestFile(string fileName, int sizeInMB)
        {
            Console.WriteLine($"生成测试文件: {fileName} ({sizeInMB}MB)");
            
            var filePath = Path.Combine(AppContext.BaseDirectory, fileName);
            var chunkSize = 1024 * 1024; // 1MB chunks
            var totalBytes = sizeInMB * chunkSize;
            
            using var fileStream = new FileStream(filePath, FileMode.Create);
            var buffer = new byte[chunkSize];
            
            // 填充随机数据
            var random = new Random();
            
            for (int i = 0; i < sizeInMB; i++)
            {
                random.NextBytes(buffer);
                await fileStream.WriteAsync(buffer, 0, buffer.Length);
                
                if (i % 10 == 0) // 每10MB显示一次进度
                {
                    Console.WriteLine($"已生成: {i + 1}MB / {sizeInMB}MB");
                }
            }
            
            Console.WriteLine($"测试文件生成完成: {filePath}");
        }

        /// <summary>
        /// 清理测试文件
        /// </summary>
        public static void CleanupTestFiles()
        {
            Console.WriteLine("清理测试文件...");
            
            var uploadDir = Path.Combine(AppContext.BaseDirectory, "uploads");
            var tempDir = Path.Combine(AppContext.BaseDirectory, "temp");
            
            if (Directory.Exists(uploadDir))
            {
                var files = Directory.GetFiles(uploadDir);
                foreach (var file in files)
                {
                    File.Delete(file);
                    Console.WriteLine($"删除文件: {Path.GetFileName(file)}");
                }
            }
            
            if (Directory.Exists(tempDir))
            {
                var files = Directory.GetFiles(tempDir);
                foreach (var file in files)
                {
                    File.Delete(file);
                    Console.WriteLine($"删除临时文件: {Path.GetFileName(file)}");
                }
            }
            
            Console.WriteLine("清理完成");
        }
    }
}
