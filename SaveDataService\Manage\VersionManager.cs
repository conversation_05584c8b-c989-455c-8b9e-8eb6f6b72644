﻿using GameServer.Util;
using Newtonsoft.Json;

namespace GameServer
{
    internal class VersionInfo
    {
        public int VersionNum { get; set; } = 1;
        public int minVersionNum { get; set; } = 0;
        public int unityVersionNum { get; set; } = 1;
    }
    internal class VersionChangeInfo
    {
        public VersionInfo VersionString ;
        public bool needChangeInfo = true;
        public bool needDelLogExcel = false;
    }
    internal class VersionManager
    {

        private static VersionInfo _verInfo;
        public static VersionChangeInfo versionChangeInfo;
        private static void initInfo()
        {
            if (_verInfo != null)
            {
                return;
            }
            string configName = $"VersionInfo.appconfig.json";
            string fullPath = Path.GetFullPath($"./{configName}");

            fullPath = PatchUtil.getFilePath() + configName;
            if (!File.Exists(fullPath))
            {
                var newinfo = new VersionInfo();
                newinfo.VersionNum = 1;
                newinfo.minVersionNum = 0;
                newinfo.unityVersionNum = 1;
                var savetext = JsonConvert.SerializeObject(newinfo);
                File.WriteAllText(fullPath, savetext);
            }
            string config = File.ReadAllText(fullPath);
            _verInfo = JsonConvert.DeserializeObject<VersionInfo>(config);
        }
        public static void getNewVersion(byte changeType)
        {

            initInfo();
            //Console.WriteLine("取到的配置路径 "+ fullPath);

            VersionChangeInfo outInfo = new VersionChangeInfo();
            switch (changeType)
            {
                case 1:
                    _verInfo.unityVersionNum++;
                    break;
                case 2:
                    _verInfo.minVersionNum++;
                    _verInfo.unityVersionNum = 1;
                    outInfo.needDelLogExcel = true;
                    break;
                case 3:
                    _verInfo.VersionNum++;
                    _verInfo.unityVersionNum = 1;
                    _verInfo.minVersionNum=0;
                    outInfo.needDelLogExcel = true;
                    break;
                default:
                    outInfo.needChangeInfo = false;
                    break;
            }
            outInfo.VersionString = _verInfo;
            versionChangeInfo = outInfo;
        }
        public static void saveInfo()
        {
            if (_verInfo == null)
            {
                return;
            }
            string configName = $"VersionInfo.appconfig.json";
            string fullPath = Path.GetFullPath($"./{configName}");
            fullPath = PatchUtil.getFilePath() + configName;
            var savetext = JsonConvert.SerializeObject(_verInfo);
            File.WriteAllText(fullPath, savetext);
            string CurDir2 = AppConfig.Instance.VersionInfoExportPath;
            if (CurDir2.StartsWith("./"))
            {
                CurDir2 = CurDir2.Replace("./", (PatchUtil.ResRootPatch.Replace("\\", "/").Replace("/Res", "")) + "/").Replace("\\", "/");
            }
            //判断文件夹是否存在,不存在就创建这个文件夹
            if (!Directory.Exists(CurDir2))
            {
                Directory.CreateDirectory(CurDir2);
            }
            var verJson = CurDir2 + configName;
            File.WriteAllText(verJson, savetext);

        }
    }
}
