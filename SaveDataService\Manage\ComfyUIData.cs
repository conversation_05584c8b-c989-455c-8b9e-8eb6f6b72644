using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SaveDataService;
using GameServer.ORM;
using ExcelToData;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI数据处理类 - 处理workflow数据和任务执行情况
    /// </summary>
    public class ComfyUIData
    {
        private static ComfyUIData _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static ComfyUIData Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ComfyUIData();
                        }
                    }
                }
                return _instance;
            }
        }

        private ComfyUIData()
        {
        }

        /// <summary>
        /// 解析workflow JSON
        /// </summary>
        /// <param name="workflowJson">workflow JSON字符串</param>
        /// <returns>解析结果</returns>
        public WorkflowInfo ParseWorkflow(string workflowJson)
        {
            try
            {
                var workflowData = JsonConvert.DeserializeObject<Dictionary<string, object>>(workflowJson);
                var workflowInfo = new WorkflowInfo
                {
                    WorkflowJson = workflowJson,
                    Nodes = new List<WorkflowNode>(),
                    InputParameters = new List<WorkflowParameter>(),
                    OutputParameters = new List<WorkflowParameter>()
                };

                // 解析节点信息
                if (workflowData != null)
                {
                    foreach (var kvp in workflowData)
                    {
                        if (kvp.Value is Newtonsoft.Json.Linq.JObject nodeObj)
                        {
                            var node = new WorkflowNode
                            {
                                NodeId = kvp.Key,
                                NodeType = nodeObj["class_type"]?.ToString() ?? "",
                                NodeTitle = nodeObj["_meta"]?["title"]?.ToString() ?? kvp.Key,
                                Inputs = new Dictionary<string, object>(),
                                Outputs = new List<string>()
                            };

                            // 解析输入参数
                            if (nodeObj["inputs"] is Newtonsoft.Json.Linq.JObject inputs)
                            {
                                foreach (var input in inputs)
                                {
                                    node.Inputs[input.Key] = input.Value?.ToString() ?? "";
                                }
                            }

                            workflowInfo.Nodes.Add(node);
                        }
                    }
                }

                return workflowInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析workflow失败: {ex.Message}");
                return new WorkflowInfo { WorkflowJson = workflowJson, Error = ex.Message };
            }
        }

        /// <summary>
        /// 创建任务执行上下文
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>任务执行上下文</returns>
        public TaskExecutionContext CreateTaskContext(string taskId, string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                var workflow = db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);

                if (task == null || workflow == null)
                {
                    return null;
                }

                var workflowInfo = ParseWorkflow(workflow.workflowJson ?? "{}");

                var context = new TaskExecutionContext
                {
                    TaskId = taskId,
                    WorkflowId = workflowId,
                    Task = task,
                    Workflow = workflow,
                    WorkflowInfo = workflowInfo,
                    ExecutionLog = new List<TaskExecutionStep>(),
                    StartTime = DateTime.Now,
                    CurrentNodeIndex = 0,
                    TotalNodes = workflowInfo.Nodes.Count,
                    Status = TaskExecutionStatus.Initialized
                };

                return context;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建任务上下文失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新任务执行进度
        /// </summary>
        /// <param name="context">任务执行上下文</param>
        /// <param name="nodeId">当前节点ID</param>
        /// <param name="nodeName">当前节点名称</param>
        /// <param name="nodeInput">节点输入</param>
        /// <param name="nodeOutput">节点输出</param>
        /// <param name="progress">进度百分比</param>
        /// <returns>是否成功</returns>
        public bool UpdateTaskProgress(TaskExecutionContext context, string nodeId, string nodeName, string nodeInput = "", string nodeOutput = "", int progress = -1)
        {
            try
            {
                if (context == null) return false;

                // 计算进度
                if (progress < 0)
                {
                    progress = (int)((double)context.CurrentNodeIndex / context.TotalNodes * 100);
                }

                // 更新上下文
                context.CurrentNodeId = nodeId;
                context.CurrentNodeName = nodeName;
                context.Progress = progress;
                context.LastUpdateTime = DateTime.Now;

                // 添加执行步骤
                var step = new TaskExecutionStep
                {
                    NodeId = nodeId,
                    NodeName = nodeName,
                    Input = nodeInput,
                    Output = nodeOutput,
                    StartTime = DateTime.Now,
                    Status = "执行中"
                };
                context.ExecutionLog.Add(step);

                // 更新数据库中的任务状态
                ComfyUIManage.Instance.UpdateTaskStatus(
                    context.TaskId, 
                    1, // 运行中
                    progress, 
                    nodeId, 
                    nodeName
                );

                // 添加日志
                ComfyUIManage.Instance.AddTaskLog(
                    context.TaskId,
                    1, // Info
                    $"执行节点: {nodeName} (进度: {progress}%)",
                    nodeId,
                    nodeName,
                    JsonConvert.SerializeObject(new { Input = nodeInput, Output = nodeOutput })
                );

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新任务进度失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 完成任务执行步骤
        /// </summary>
        /// <param name="context">任务执行上下文</param>
        /// <param name="nodeId">节点ID</param>
        /// <param name="output">输出结果</param>
        /// <param name="success">是否成功</param>
        /// <returns>是否成功</returns>
        public bool CompleteTaskStep(TaskExecutionContext context, string nodeId, string output, bool success = true)
        {
            try
            {
                if (context == null) return false;

                // 查找对应的执行步骤
                var step = context.ExecutionLog.LastOrDefault(s => s.NodeId == nodeId);
                if (step != null)
                {
                    step.Output = output;
                    step.EndTime = DateTime.Now;
                    step.Status = success ? "完成" : "失败";
                    step.Duration = step.EndTime.Value - step.StartTime;
                }

                // 更新节点索引
                context.CurrentNodeIndex++;

                // 添加日志
                ComfyUIManage.Instance.AddTaskLog(
                    context.TaskId,
                    success ? 1 : 3, // Info 或 Error
                    $"节点执行{(success ? "完成" : "失败")}: {step?.NodeName}",
                    nodeId,
                    step?.NodeName ?? "",
                    output
                );

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"完成任务步骤失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 完成整个任务
        /// </summary>
        /// <param name="context">任务执行上下文</param>
        /// <param name="success">是否成功</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>是否成功</returns>
        public bool CompleteTask(TaskExecutionContext context, bool success = true, string errorMessage = "")
        {
            try
            {
                if (context == null) return false;

                context.EndTime = DateTime.Now;
                context.Status = success ? TaskExecutionStatus.Completed : TaskExecutionStatus.Failed;
                context.ErrorMessage = errorMessage;

                // 更新数据库中的任务状态
                ComfyUIManage.Instance.UpdateTaskStatus(
                    context.TaskId,
                    success ? 2 : 3, // 完成或失败
                    100,
                    "",
                    "",
                    errorMessage
                );

                // 添加完成日志
                ComfyUIManage.Instance.AddTaskLog(
                    context.TaskId,
                    success ? 1 : 3,
                    $"任务{(success ? "完成" : "失败")}",
                    "",
                    "",
                    JsonConvert.SerializeObject(new 
                    { 
                        Duration = context.EndTime - context.StartTime,
                        TotalSteps = context.ExecutionLog.Count,
                        ErrorMessage = errorMessage
                    })
                );

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"完成任务失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存任务输出文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileName">文件名</param>
        /// <param name="fileData">文件数据</param>
        /// <param name="fileType">文件类型</param>
        /// <param name="description">描述</param>
        /// <returns>文件路径</returns>
        public string SaveTaskOutputFile(string taskId, string fileName, byte[] fileData, int fileType = 1, string description = "")
        {
            try
            {
                // 创建输出目录
                var outputDir = Path.Combine("ComfyUIOutput", taskId);
                Directory.CreateDirectory(outputDir);

                // 保存文件
                var filePath = Path.Combine(outputDir, fileName);
                File.WriteAllBytes(filePath, fileData);

                // 计算MD5
                var md5 = System.Security.Cryptography.MD5.Create();
                var hash = md5.ComputeHash(fileData);
                var md5String = BitConverter.ToString(hash).Replace("-", "").ToLower();

                // 添加到数据库
                ComfyUIManage.Instance.AddTaskFile(
                    taskId,
                    fileType,
                    fileName,
                    filePath,
                    fileData.Length,
                    md5String,
                    description
                );

                Console.WriteLine($"保存任务输出文件: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存任务输出文件失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务执行报告
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>执行报告</returns>
        public TaskExecutionReport GetTaskExecutionReport(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);

                if (task == null)
                {
                    return null;
                }

                // 解析日志和文件JSON
                var logs = new List<object>();
                var files = new List<object>();
                var inputFiles = new List<object>();
                var outputFiles = new List<object>();

                if (!string.IsNullOrEmpty(task.Logs))
                {
                    try
                    {
                        logs = System.Text.Json.JsonSerializer.Deserialize<List<object>>(task.Logs) ?? new List<object>();
                    }
                    catch { }
                }

                if (!string.IsNullOrEmpty(task.Files))
                {
                    try
                    {
                        var allFiles = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(task.Files) ?? new List<System.Text.Json.JsonElement>();
                        files = allFiles.Cast<object>().ToList();
                        inputFiles = allFiles.Where(f => f.TryGetProperty("fileType", out var ft) && ft.GetInt32() == 0).Cast<object>().ToList();
                        outputFiles = allFiles.Where(f => f.TryGetProperty("fileType", out var ft) && ft.GetInt32() == 1).Cast<object>().ToList();
                    }
                    catch { }
                }

                var report = new TaskExecutionReport
                {
                    TaskId = taskId,
                    TaskName = task.taskName ?? "",
                    Status = (int)task.status,
                    Progress = (int)task.progress,
                    StartTime = task.startTime,
                    EndTime = task.endTime,
                    Duration = task.endTime.HasValue && task.startTime.HasValue
                        ? task.endTime.Value - task.startTime.Value
                        : TimeSpan.Zero,
                    ErrorMessage = task.errorMessage ?? "",
                    Logs = logs,
                    Files = files,
                    InputFiles = inputFiles,
                    OutputFiles = outputFiles
                };

                return report;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务执行报告失败: {ex.Message}");
                return null;
            }
        }
    }
}
