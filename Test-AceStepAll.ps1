# AceStepAll ComfyUI 工作流测试脚本
# PowerShell版本

param(
    [string]$Mode = "interactive"
)

Write-Host "===================================" -ForegroundColor Cyan
Write-Host "AceStepAll ComfyUI 工作流测试脚本" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# 检查.NET环境
try {
    $dotnetVersion = dotnet --version
    Write-Host "检测到 .NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到 .NET 运行时，请先安装 .NET" -ForegroundColor Red
    exit 1
}

# 检查ComfyUI服务器状态
function Test-ComfyUIServer {
    param([string]$Url = "http://127.0.0.1:8888")
    
    Write-Host "检查ComfyUI服务器状态: $Url" -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ ComfyUI服务器在线" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️ ComfyUI服务器离线或无法访问" -ForegroundColor Yellow
        Write-Host "   请确保ComfyUI在 $Url 运行" -ForegroundColor Yellow
        return $false
    }
}

# 运行测试
function Start-Test {
    param([string]$TestMode)

    Write-Host ""
    Write-Host "开始运行测试..." -ForegroundColor Green
    Write-Host "测试模式: $TestMode" -ForegroundColor Cyan
    Write-Host ""

    # 检查服务器状态
    Test-ComfyUIServer

    # 编译并运行测试
    try {
        if ($TestMode -eq "quick") {
            Write-Host "执行快速测试 (约30秒)..." -ForegroundColor Yellow
            dotnet run RunAceStepAllTest.cs quick
        } elseif ($TestMode -eq "full") {
            Write-Host "执行完整测试 (约2-5分钟)..." -ForegroundColor Yellow
            dotnet run RunAceStepAllTest.cs full
        } elseif ($TestMode -eq "simple") {
            Write-Host "执行简单测试 (约10秒)..." -ForegroundColor Yellow
            dotnet run RunAceStepAllTest.cs simple
        } else {
            Write-Host "执行简单测试 (默认)..." -ForegroundColor Yellow
            dotnet run RunAceStepAllTest.cs simple
        }
    } catch {
        Write-Host "测试执行失败: $_" -ForegroundColor Red
    }
}

# 主逻辑
switch ($Mode.ToLower()) {
    "simple" {
        Start-Test "simple"
    }
    "quick" {
        Start-Test "quick"
    }
    "full" {
        Start-Test "full"
    }
    "interactive" {
        Write-Host "请选择测试模式:" -ForegroundColor Cyan
        Write-Host "1. 简单测试 (推荐，约10秒)" -ForegroundColor White
        Write-Host "2. 快速测试 (约30秒)" -ForegroundColor White
        Write-Host "3. 完整测试 (约2-5分钟)" -ForegroundColor White
        Write-Host ""

        $choice = Read-Host "请输入选项 (1-3)"

        switch ($choice) {
            "1" { Start-Test "simple" }
            "2" { Start-Test "quick" }
            "3" { Start-Test "full" }
            default {
                Write-Host "无效选项，使用简单测试..." -ForegroundColor Yellow
                Start-Test "simple"
            }
        }
    }
    default {
        Write-Host "用法: .\Test-AceStepAll.ps1 [-Mode simple|quick|full|interactive]" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "示例:" -ForegroundColor Cyan
        Write-Host "  .\Test-AceStepAll.ps1 -Mode simple     # 简单测试 (推荐)" -ForegroundColor White
        Write-Host "  .\Test-AceStepAll.ps1 -Mode quick      # 快速测试" -ForegroundColor White
        Write-Host "  .\Test-AceStepAll.ps1 -Mode full       # 完整测试" -ForegroundColor White
        Write-Host "  .\Test-AceStepAll.ps1                  # 交互式选择" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "测试脚本执行完成！" -ForegroundColor Green
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
