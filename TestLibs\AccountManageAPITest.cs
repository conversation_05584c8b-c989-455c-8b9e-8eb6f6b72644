using System;
using SaveDataService;
using Newtonsoft.Json;

namespace SaveDataService
{
    /// <summary>
    /// RESTfulAPIGen 测试类
    /// </summary>
    public class AccountManageAPITest
    {
        /// <summary>
        /// 测试 GetHttpPostFunction 方法
        /// </summary>
        public static void TestGetHttpPostFunction()
        {
            try
        {
                Console.WriteLine("=== RESTfulAPIGen 测试开始 ===");
                Console.WriteLine();

                // 调用 GetHttpPostFunction 方法
                var apiDescription = RESTfulAPIGen.GetHttpPostFunction("AccountManage");

                Console.WriteLine("生成的 RESTful API 描述:");
                Console.WriteLine(apiDescription);
                Console.WriteLine();

                // 尝试解析 JSON 以验证格式
                var apiData = JsonConvert.DeserializeObject(apiDescription);
                Console.WriteLine("✓ JSON 格式验证通过");
                Console.WriteLine();

                Console.WriteLine("=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("开始运行 RESTfulAPIGen 测试...");
            Console.WriteLine();

            TestGetHttpPostFunction();

            Console.WriteLine();
            Console.WriteLine("所有测试完成。");
        }
    }
}
