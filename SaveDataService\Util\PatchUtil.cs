﻿

using SaveDataService;

namespace GameServer.Util
{
    class PatchUtil
    {


        //Gets the local path of shared files.
        //When debugging, take them directly from source so we can edit and reload.
        //Otherwise, take them from the deployment directory.
        public static string ResRootPatch
        {
            get
            {
                if (!String.IsNullOrEmpty(AppConfig.Instance.ResRootPatch))
                {
                    return AppConfig.Instance.ResRootPatch;
                }
                var assemblyPath = Path.GetDirectoryName(typeof(Program).Assembly.Location);

#if DEBUG
                //Console.WriteLine("PatchUtil 走的 DEBUG标签");
                string pathStr =Directory.GetParent(assemblyPath).Parent.Parent.FullName;
                return Path.Combine(pathStr, "Res").Replace("\\", "/");
#else
                //Path.Combine(Environment.CurrentDirectory, "Res");

                string pathStr = PatchUtil.getFilePath();
                string truePathStr= Path.Combine(pathStr, "Res").Replace("\\", "/"); ;
                //Console.WriteLine("PatchUtil 走的 不是 DEBUG 标签" + truePathStr);
                return truePathStr;
#endif
            }
        }
        public static string ResRootExportPatch
        {
            get
            {
                if (!String.IsNullOrEmpty(AppConfig.Instance.ResRootExportPatch))
                {
                    return AppConfig.Instance.ResRootExportPatch;
                }
                var assemblyPath = Path.GetDirectoryName(typeof(Program).Assembly.Location);

#if DEBUG
                //Console.WriteLine("PatchUtil 走的 DEBUG标签");
                string pathStr = Directory.GetParent(assemblyPath).Parent.Parent.FullName;
                return Path.Combine(pathStr, "Res").Replace("\\", "/");
#else
                //Path.Combine(Environment.CurrentDirectory, "Res");

                string pathStr = PatchUtil.getFilePath();
                string truePathStr= Path.Combine(pathStr, "Res").Replace("\\", "/"); ;
                //Console.WriteLine("PatchUtil 走的 不是 DEBUG 标签" + truePathStr);
                return truePathStr;
#endif
            }
        }

        public static string[] getFiles(string dir,string searchPattern)
        {

            if (dir.StartsWith("./"))
            {
                dir = dir.Replace("./", PatchUtil.ResRootPatch + "/").Replace("\\", "/");
            }
            return Directory.GetFiles(dir, searchPattern, SearchOption.AllDirectories);
        }

        public static string getFilePath()
        {
            string pathStr = System.AppDomain.CurrentDomain.SetupInformation.ApplicationBase;
            string path = pathStr.Replace("\\", "/");
            return path;
        }
    }
}
