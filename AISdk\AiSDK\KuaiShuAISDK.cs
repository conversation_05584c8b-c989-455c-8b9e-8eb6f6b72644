﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class KuaiShuAISDK : AISDKProvider
	{
		private string url = "https://api.deepseek.com/chat/completions";
		private string key = "Bearer sk-b76ce5226bf94b12a690b54f59936f46";
		public string Model;
		public KuaiShuAISDK(string userMode)
		{
			this.Model = userMode;
		}

		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "DeepSeek-V2.5";
			}
			var data = new KuaiShuData(Model, "user", messages, true);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
                // UnityEngine.Debug.Log(backText);
                if (!bol)
                {
                    backText = this.Model + ": " + backText;
                }
                callback(backText, bol);
			});
		}

        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "DeepSeek-V2.5";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }
        public void ChatWithAi(string prompt, Action<string, bool> callback)
        {
            List<Message> messages = new List<Message>();
            messages.Add(new Message { role = "user", content = prompt });
            if (string.IsNullOrEmpty(Model))
            {
                Model = "DeepSeek-V2.5";
            }
            var data = new KuaiShuData(Model, "user", messages, true);
            Instance.HttpsWeb(url, key, data, (backText, bol) =>
            {
                callback(backText, bol);
            });
        }
        public async Task<AiChatBase> KernelFunAsync()
        {
            var kernel = Kernel.CreateBuilder()
.AddOpenAIChatCompletion(modelId: "deepseek-chat",       // 千问模型，如 qwen-turbo、qwen-max 等
    apiKey: "sk-2b77e4bfff304444b2e7036ef6082cb1",  // DashScope API Key（需开通服务）
    endpoint: new Uri("https://api.deepseek.com/v1")
    )
.Build();
            AiChatBase data= new AiChatBase();
            data.ChatService= kernel.GetRequiredService<IChatCompletionService>();
			data.chatHistory= new ChatHistory();
			return data;
            //IChatCompletionService chatService= kernel.GetRequiredService<IChatCompletionService>();
            //ChatHistory chatHistory = new ChatHistory();
            //AIModelManager.Instance.ChatService = kernel.GetRequiredService<IChatCompletionService>();
            //AIModelManager.Instance.chatHistory = new ChatHistory();
            //AIModelManager.Instance.chatHistory.AddUserMessage("你好，介绍一下你自己");
            //Console.WriteLine("deepseek: 你好，介绍一下你自己\n");
            //await foreach (var chunk in AIModelManager.Instance.ChatService.GetStreamingChatMessageContentsAsync(AIModelManager.Instance.chatHistory))
            //{
            //    Console.Write(chunk.Content);
            //}
        }
    }
}
