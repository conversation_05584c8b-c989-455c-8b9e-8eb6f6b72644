# AceStepAll ComfyUI 工作流测试说明

## 概述

AceStepAll 是一个基于 ComfyUI 的音乐生成工作流，能够根据输入的歌词和推理步数生成音频文件。本测试套件提供了完整的测试功能来验证工作流的各个方面。

## 工作流特性

- **输入参数**:
  - `musictxt_multi_line_prompt`: 歌词文本（支持多行，包含verse、chorus、bridge等结构）
  - `step_value`: 推理步数（影响生成质量，建议20-80）

- **输出**: WAV格式音频文件

- **节点组成**:
  - MultiLineLyrics: 歌词输入处理
  - MultiLinePromptACES: 提示词处理
  - GenerationParameters: 生成参数配置
  - ACEStepGen: 音乐生成核心
  - SaveAudioMW: 音频保存

## 测试环境要求

### 1. ComfyUI 服务器
确保 ComfyUI 服务器正在运行：
```bash
# 默认地址
http://127.0.0.1:8888
```

### 2. 必需的 ComfyUI 节点
- ACE Step 扩展包
- Audio Tools 扩展包
- ComfyUI Easy Use 扩展包

### 3. .NET 环境
- .NET 6.0 或更高版本

## 快速开始

### 方法1: 使用 PowerShell 脚本（推荐）
```powershell
# 快速测试
.\Test-AceStepAll.ps1 -Mode quick

# 完整测试
.\Test-AceStepAll.ps1 -Mode full

# 交互式选择
.\Test-AceStepAll.ps1
```

### 方法2: 使用批处理文件
```cmd
# 快速测试
test_acestepall.bat quick

# 完整测试
test_acestepall.bat full

# 交互式选择
test_acestepall.bat
```

### 方法3: 直接运行 C# 程序
```bash
# 编译并运行
dotnet run TestAceStepAll.cs

# 或者使用参数
dotnet run TestAceStepAll.cs quick
dotnet run TestAceStepAll.cs full
```

## 测试模式说明

### 1. 快速测试 (推荐)
- **耗时**: 约30秒
- **功能**: 验证基本工作流运行
- **适用**: 日常开发验证

```csharp
// 使用简短测试歌词和较少步数
AceStepAll.TestWorkflow();
```

### 2. 完整测试
- **耗时**: 2-5分钟
- **功能**: 全面测试所有功能
- **包含**:
  - 环境准备和服务器连接测试
  - 不同参数组合测试
  - 任务状态监控测试
  - RESTful API 测试

### 3. 自定义参数测试
- **交互式**: 用户输入自定义歌词和步数
- **灵活性**: 可测试特定场景

## 测试用例示例

### 基本测试歌词
```
[verse]
测试歌词，节拍轻快 (test!)
音乐生成，效果不错 (yeah!)

[chorus]
这是测试，快速验证 (go!)
工作流程，运行正常 (woo!)
```

### 中等长度测试
```
[verse]
这是一个中等长度的测试歌词 (test!)
用来验证工作流的处理能力 (power!)

[chorus]
音乐生成，效果很棒 (great!)
节拍律动，让人陶醉 (wow!)
```

### 高质量测试
```
[verse]
高质量音乐生成测试 (quality!)
更多步数，更好效果 (better!)

[chorus]
精细处理，完美音质 (perfect!)
专业级别，值得期待 (amazing!)
```

## 参数建议

### 推理步数 (step_value)
- **快速测试**: 20-30 步
- **标准质量**: 40-60 步
- **高质量**: 70-80 步
- **注意**: 步数越多，生成时间越长

### 歌词格式
- 支持标准歌曲结构：`[verse]`, `[chorus]`, `[bridge]`
- 可包含英文感叹词：`(yeah!)`, `(woo!)`, `(test!)`
- 建议每行不超过50字符

## 监控和调试

### 任务状态检查
```csharp
// 获取任务状态
var status = AceStepAll.GetTaskStatus(taskId);
Console.WriteLine(status);
```

### 常见状态
- **0**: 等待中
- **1**: 运行中
- **2**: 已完成
- **3**: 失败

### 日志查看
测试过程中会输出详细日志，包括：
- 服务器连接状态
- 工作流提交结果
- 任务执行进度
- 错误信息

## 故障排除

### 1. 服务器连接失败
```
⚠️ 警告: 没有在线的ComfyUI服务器
```
**解决方案**:
- 检查 ComfyUI 是否在 `http://127.0.0.1:8888` 运行
- 确认端口没有被占用
- 检查防火墙设置

### 2. 工作流提交失败
```
❌ 工作流任务创建失败
```
**解决方案**:
- 检查 ComfyUI 是否安装了必需的扩展
- 验证工作流 JSON 格式
- 查看 ComfyUI 控制台错误信息

### 3. 任务执行超时
**解决方案**:
- 减少推理步数
- 检查 GPU 内存使用情况
- 确认模型文件完整

## API 接口

AceStepAll 继承自 RESTfulAPIBase，自动提供 HTTP API：

```
POST http://127.0.0.1:7778/api/acestepall/runWorkflow
Content-Type: application/json

{
    "musictxt_multi_line_prompt": "歌词内容",
    "step_value": 40
}
```

### 获取 API 文档
```
GET http://127.0.0.1:7778/api/acestepall
```

## 性能优化建议

1. **开发阶段**: 使用快速测试（20-30步）
2. **质量验证**: 使用标准测试（40-60步）
3. **生产环境**: 根据需求调整步数
4. **批量测试**: 适当延迟避免服务器过载

## 扩展测试

可以基于现有测试框架添加更多测试场景：

```csharp
// 添加新的测试用例
var customTestCase = new {
    Name = "特殊场景测试",
    Lyrics = "自定义歌词...",
    Steps = 50
};
```

## 联系和支持

如果遇到问题或需要帮助，请：
1. 检查 ComfyUI 控制台日志
2. 查看测试输出的详细错误信息
3. 确认环境配置正确
