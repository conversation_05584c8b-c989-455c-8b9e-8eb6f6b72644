using System;
using System.IO;
using Newtonsoft.Json;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI文件下载配置
    /// </summary>
    public class ComfyUIDownloadConfig
    {
        private static ComfyUIDownloadConfig? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// 单例实例
        /// </summary>
        public static ComfyUIDownloadConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = LoadConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 下载根目录
        /// </summary>
        public string DownloadRootPath { get; set; } = "Downloads";

        /// <summary>
        /// 是否按任务ID创建子目录
        /// </summary>
        public bool CreateTaskSubdirectory { get; set; } = true;

        /// <summary>
        /// 是否按文件类型创建子目录
        /// </summary>
        public bool CreateTypeSubdirectory { get; set; } = false;

        /// <summary>
        /// 是否按日期创建子目录
        /// </summary>
        public bool CreateDateSubdirectory { get; set; } = false;

        /// <summary>
        /// 最大并发下载数
        /// </summary>
        public int MaxConcurrentDownloads { get; set; } = 3;

        /// <summary>
        /// 下载超时时间（秒）
        /// </summary>
        public int DownloadTimeoutSeconds { get; set; } = 300;

        /// <summary>
        /// 是否自动重试下载失败的文件
        /// </summary>
        public bool AutoRetryFailedDownloads { get; set; } = true;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 重试间隔（毫秒）
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// 是否保留原始文件名
        /// </summary>
        public bool KeepOriginalFilename { get; set; } = true;

        /// <summary>
        /// 文件名前缀
        /// </summary>
        public string FilenamePrefix { get; set; } = "";

        /// <summary>
        /// 文件名后缀
        /// </summary>
        public string FilenameSuffix { get; set; } = "";

        /// <summary>
        /// 支持的文件类型过滤器（空表示下载所有类型）
        /// </summary>
        public string[] SupportedFileTypes { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 排除的文件类型
        /// </summary>
        public string[] ExcludedFileTypes { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 最大文件大小限制（MB，0表示无限制）
        /// </summary>
        public double MaxFileSizeMB { get; set; } = 0;

        /// <summary>
        /// 是否在下载完成后验证文件完整性
        /// </summary>
        public bool ValidateFileIntegrity { get; set; } = false;

        /// <summary>
        /// 配置文件路径
        /// </summary>
        private static readonly string ConfigFilePath = "comfyui_download_config.json";

        /// <summary>
        /// 加载配置
        /// </summary>
        private static ComfyUIDownloadConfig LoadConfig()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var config = JsonConvert.DeserializeObject<ComfyUIDownloadConfig>(json);
                    if (config != null)
                    {
                        return config;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载下载配置失败: {ex.Message}，使用默认配置");
            }

            // 返回默认配置
            var defaultConfig = new ComfyUIDownloadConfig();
            defaultConfig.SaveConfig();
            return defaultConfig;
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(ConfigFilePath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存下载配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取下载目录路径
        /// </summary>
        public string GetDownloadPath(string taskId, string fileType = "", string filename = "")
        {
            var path = DownloadRootPath;

            // 按日期创建子目录
            if (CreateDateSubdirectory)
            {
                path = Path.Combine(path, DateTime.Now.ToString("yyyy-MM-dd"));
            }

            // 按任务ID创建子目录
            if (CreateTaskSubdirectory)
            {
                path = Path.Combine(path, "ComfyUI", taskId);
            }

            // 按文件类型创建子目录
            if (CreateTypeSubdirectory && !string.IsNullOrEmpty(fileType))
            {
                path = Path.Combine(path, fileType);
            }

            return path;
        }

        /// <summary>
        /// 生成最终文件名
        /// </summary>
        public string GenerateFilename(string originalFilename)
        {
            if (!KeepOriginalFilename)
            {
                var extension = Path.GetExtension(originalFilename);
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                originalFilename = $"{timestamp}{extension}";
            }

            var nameWithoutExt = Path.GetFileNameWithoutExtension(originalFilename);
            var extension2 = Path.GetExtension(originalFilename);

            return $"{FilenamePrefix}{nameWithoutExt}{FilenameSuffix}{extension2}";
        }

        /// <summary>
        /// 检查文件类型是否被支持
        /// </summary>
        public bool IsFileTypeSupported(string extension)
        {
            extension = extension.ToLower();

            // 检查排除列表
            if (ExcludedFileTypes.Length > 0)
            {
                foreach (var excluded in ExcludedFileTypes)
                {
                    if (extension.Equals(excluded.ToLower()))
                    {
                        return false;
                    }
                }
            }

            // 检查支持列表（空表示支持所有）
            if (SupportedFileTypes.Length > 0)
            {
                foreach (var supported in SupportedFileTypes)
                {
                    if (extension.Equals(supported.ToLower()))
                    {
                        return true;
                    }
                }
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查文件大小是否在限制范围内
        /// </summary>
        public bool IsFileSizeAllowed(long fileSizeBytes)
        {
            if (MaxFileSizeMB <= 0)
            {
                return true; // 无限制
            }

            var fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);
            return fileSizeMB <= MaxFileSizeMB;
        }
    }
}
