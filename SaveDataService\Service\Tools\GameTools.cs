﻿

namespace GameServer.GameService
{
    class Vector3
    {
        public static Random rnd = new Random();
        public float x;
        public float y;
        public float z;
        public Vector3(float getx, float gety, float getz)
        {
            x = getx;
            y = gety;
            z = getz;
        }
        public double Lenght()
        {
            return Math.Sqrt(x * x + y * y + z * z);
        }
        public Vector3 Normalize()
        {
            Vector3 outV3 = new Vector3();
            double sqr = Math.Pow(x, 2) + Math.Pow(y, 2) + Math.Pow(z, 2);
            float len = 1 / MathF.Sqrt((float)sqr);
            outV3.x = x * len;
            outV3.y = y * len;
            outV3.z = z * len;
            return outV3;
        }
        public static float disVector3(Vector3 a, Vector3 b)
        {
            double disSqr = Math.Pow(a.x - b.x, 2) + Math.Pow(a.y - b.y, 2) + Math.Pow(a.z - b.z, 2);
            return (float)Math.Sqrt(disSqr);
        }
        public static Vector3 subVector3(Vector3 a, Vector3 b)
        {
            Vector3 outV3 = new Vector3();
            outV3.x = a.x - b.x;
            outV3.y = a.y - b.y;
            outV3.z = a.z - b.z;
            return outV3;
        }
        public static Vector3 addVector3(Vector3 a, Vector3 b)
        {
            Vector3 outV3 = new Vector3();
            outV3.x = a.x + b.x;
            outV3.y = a.y + b.y;
            outV3.z = a.z + b.z;
            return outV3;
        }
        public static Vector3 scaleVector3(Vector3 a, float b)
        {
            Vector3 outV3 = new Vector3();
            outV3.x = a.x * b;
            outV3.y = a.y * b;
            outV3.z = a.z * b;
            return outV3;
        }
        public static Vector3 randomVector3(Vector3 a, float b)
        {
            Vector3 outV3 = new Vector3();
            outV3.x = a.x + (float)(rnd.NextDouble() - 0.5f) * b;
            outV3.y = a.y;
            outV3.z = a.z + (float)(rnd.NextDouble() - 0.5f) * b;
            return outV3;
        }
        public void subVector3(Vector3 b)
        {
            x = x - b.x;
            y = y - b.y;
            z = z - b.z;
        }
        public void addVector3(Vector3 b)
        {
            x = x + b.x;
            y = y + b.y;
            z = z + b.z;
        }
        public void scaleVector3(float b)
        {
            x *= b;
            y *= b;
            z *= b;
        }
        public Vector3()
        {
            x = 0;
            y = 0;
            z = 0;
        }
        public void clone(Vector3 clone)
        {
            x = clone.x;
            y = clone.y;
            z = clone.z;
        }
        public void ClampLength(float maxLength)
        {
            float val = x * x + y * y + z * z;
            if (val > maxLength * maxLength)
            {
                Vector3 vector = Normalize();
                x = vector.x * maxLength;
                y = vector.y * maxLength;
                z = vector.z * maxLength;
            }
        }
    }
    /// <summary>
    /// 区域数据类型
    /// </summary>
    class TriangleRect
    {
        public Vector3 point1;
        public Vector3 point2;
        public Vector3 point3;

        private float _dX21 = float.PositiveInfinity;
        private float dX21
        {
            get
            {
                if (_dX21 == float.PositiveInfinity)
                {
                    _dX21 = point1.x - point2.x;
                }
                return _dX21;
            }
        }
        private float _dY12 = float.PositiveInfinity;
        private float dY12
        {
            get
            {
                if (_dY12 == float.PositiveInfinity)
                {
                    _dY12 = point2.z - point1.z;
                }
                return _dY12;
            }
        }
        private float _mrx = float.PositiveInfinity;
        private float mrx
        {
            get
            {
                if (_mrx == float.PositiveInfinity)
                {
                    _mrx = point3.x - point1.x;
                }
                return _mrx;
            }
        }
        private float _mry = float.PositiveInfinity;
        private float mry
        {
            get
            {
                if (_mry == float.PositiveInfinity)
                {
                    _mry = point3.z - point1.z;
                }
                return _mry;
            }
        }
        private float _rmy = float.PositiveInfinity;
        private float rmy
        {
            get
            {
                if (_rmy == float.PositiveInfinity)
                {
                    _rmy = point1.z - point3.z;
                }
                return _rmy;
            }
        }

        public bool isInRect(Vector3 p)
        {
            var dX = p.x - point1.x;
            var dY = p.z - point1.z;
            var D = this.dY12 * this.mrx + this.dX21 * this.mry;
            var s = this.dY12 * dX + this.dX21 * dY;
            var t = this.rmy * dX + this.mrx * dY;
            if (D < 0)
            {
                return s <= 0 && t <= 0 && s + t >= D;
            }
            return s >= 0 && t >= 0 && s + t <= D;
        }
    }
    /// <summary>
    /// 区域数据类型
    /// </summary>
    class CircleRect
    {
        public Vector3 center;
        public float radius=0;
        public bool isInRect(Vector3 pos)
        {
            float dis = Vector3.disVector3(pos, center);
            return dis <= radius;
        }
        public Vector3 getPointOnCircleByArc(double arc)
        {

            Vector3 v3 = new Vector3();
            v3.x = (float)Math.Sin(arc);
            v3.z = (float)Math.Cos(arc);
            v3.scaleVector3(radius);
            v3.addVector3(center);
            return v3;
        }
    }
    /// <summary>
    /// 区域数据类型
    /// </summary>
    class PosRect
    {
        public Vector3 min;
        public Vector3 max;
        private float Xlen
        {
            get
            {
                if (_x == float.PositiveInfinity)
                {
                    _x = Math.Abs(max.x - min.x);
                }
                return _x;
            }
        }
        private float _x = float.PositiveInfinity;
        private float Ylen
        {
            get
            {
                if (_y == float.PositiveInfinity)
                {
                    _y = Math.Abs(max.y - min.y);
                }
                return _y;
            }
        }
        private float _y = float.PositiveInfinity;
        private float Zlen
        {
            get
            {
                if (_z == float.PositiveInfinity)
                {
                    _z = Math.Abs(max.z - min.z);
                }
                return _z;
            }
        }
        private float _z = float.PositiveInfinity;
        /// <summary>
        /// 判断是否在区域内
        /// </summary>
        /// <param name="pos"></param>
        /// <returns></returns>
        public bool isInRect(Vector3 pos)
        {
            bool isInx = isIn(pos.x, max.x, min.x);
            bool isIny = isIn(pos.y, max.y, min.y);
            bool isInz = isIn(pos.z, max.z, min.z);
            return isInx && isIny && isInz;
        }
        public Vector3 randomInRect(float xper = 1, float zper = -1, bool hasY = false, float xPlus = 0, float zPlus = 0)
        {
            Vector3 outV3 = new Vector3();
            if (xper < 0)
            {
                xper = 0;
            }
            if (zper < 0)
            {
                zper = xper;
            }
            if (xper == 1 && zper == 1)
            {
                outV3.x = GameTools.lerp(max.x, min.x, GameTools.rnd.NextDouble());
                outV3.y = hasY ? GameTools.lerp(max.y, min.y, GameTools.rnd.NextDouble()) : 0;
                outV3.z = GameTools.lerp(max.z, min.z, GameTools.rnd.NextDouble());

            }
            else
            {
                outV3.x = GameTools.lerp(max.x, min.x, GameTools.rnd.NextDouble() * xper + (1 - xper) / 2 + xPlus);
                outV3.y = hasY ? GameTools.lerp(max.y, min.y, GameTools.rnd.NextDouble() * xper + (1 - xper) / 2) : 0;
                outV3.z = GameTools.lerp(max.z, min.z, GameTools.rnd.NextDouble() * zper + (1 - zper) / 2 + zPlus);
            }
            return outV3;
        }
        public Vector3 randomOutRect(float xper = 0, float zper = 0, bool hasY = false)
        {
            Vector3 outV3 = new Vector3();
            outV3.y = hasY ? GameTools.lerp(max.y, min.y, GameTools.rnd.NextDouble()) : 0;

            if (zper > 0)
            {
                outV3.z = (float)(Zlen * GameTools.rnd.NextDouble() * zper + max.z);
            }
            else if (zper < 0)
            {
                outV3.z = (float)(Zlen * GameTools.rnd.NextDouble() * zper + min.z);
            }
            else
            {
                outV3.z = GameTools.lerp(max.z, min.z, GameTools.rnd.NextDouble());
            }
            if (xper > 0)
            {
                outV3.x = (float)(Xlen * GameTools.rnd.NextDouble() * xper + max.x);
            }
            else if (xper < 0)
            {
                outV3.x = (float)(Xlen * GameTools.rnd.NextDouble() * xper + min.x);
            }
            else
            {
                outV3.x = GameTools.lerp(max.x, min.x, GameTools.rnd.NextDouble());
            }
            return outV3;
        }
        private static bool isIn(float x, float max, float min)
        {
            return x == max || x == min || x - max < 0 ^ x - min < 0;
        }
    }
    class GameTools
    {
        public static Random rnd = new Random();
        /// <summary>
        /// 插值计算
        /// </summary>
        /// <param name="from"></param>
        /// <param name="to"></param>
        /// <param name="per"></param>
        /// <returns></returns>
        public static float lerp(float from, float to, double per)
        {
            return (float)(from + (to - from) * per);
        }
    }
}
