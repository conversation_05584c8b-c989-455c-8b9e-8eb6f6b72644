using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using SaveDataService;
using GameServer.ORM;
using ExcelToData;
using GameServer.Util;
using GameServer;
using GameServer.GameService;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI管理类 - 管理多台ComfyUI服务器和任务
    /// </summary>
    public class ComfyUIManage : RESTfulAPIBase
    {
        private static ComfyUIManage _instance;
        private static readonly object _lock = new object();
        private readonly HttpClient _httpClient;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static ComfyUIManage Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ComfyUIManage();
                        }
                    }
                }
                return _instance;
            }
        }

        private ComfyUIManage()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(5);
        }

        /// <summary>
        /// 检查服务器是否已存在（基于IP和端口）
        /// </summary>
        /// <param name="serverUrl">服务器地址</param>
        /// <param name="port">端口</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        public bool IsServerExists(string serverUrl, int port)
        {
            try
            {
                var db = ORMTables.Instance;
                var existingServer = db.ComfyUIServers.FirstOrDefault(s => s.serverUrl == serverUrl && s.port == port);
                return existingServer != null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查服务器是否存在时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 初始化所有ComfyUI服务器 - 测试连通性并更新硬件信息
        /// </summary>
        /// <returns>初始化结果统计</returns>
        public async Task<object> Init()
        {
            Console.WriteLine("=== 开始初始化所有ComfyUI服务器 ===");

            try
            {
                var allServers = GetAllServers();
                Console.WriteLine($"发现 {allServers.Count} 台服务器，开始逐个测试连通性和获取硬件信息...\n");

                int onlineCount = 0;
                int offlineCount = 0;
                int errorCount = 0;
                var results = new List<object>();

                foreach (var server in allServers)
                {
                    Console.WriteLine($"正在处理服务器: {server.serverName} ({server.serverUrl}:{server.port})");

                    try
                    {
                        // 测试连通性并获取硬件信息
                        var result = await TestServerAndUpdateInfo(server);
                        results.Add(result);

                        if (result.IsOnline)
                        {
                            onlineCount++;
                            Console.WriteLine($"  ✅ 在线 - 已更新硬件信息");
                        }
                        else
                        {
                            offlineCount++;
                            Console.WriteLine($"  ❌ 离线");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Console.WriteLine($"  ⚠️  处理失败: {ex.Message}");

                        // 更新为离线状态
                        await UpdateServerStatusAndInfo(server.id, 0, $"处理失败: {ex.Message}", null);

                        results.Add(new
                        {
                            ServerId = server.id,
                            ServerName = server.serverName,
                            ServerUrl = server.serverUrl,
                            Port = server.port,
                            IsOnline = false,
                            SupportedWorkflows = new string[0],
                            Error = ex.Message,
                            UpdateTime = DateTime.Now
                        });
                    }

                    Console.WriteLine(); // 空行分隔
                }

                var summary = new
                {
                    TotalServers = allServers.Count,
                    OnlineServers = onlineCount,
                    OfflineServers = offlineCount,
                    ErrorServers = errorCount,
                    InitTime = DateTime.Now,
                    Results = results
                };

                Console.WriteLine("=== 服务器初始化完成 ===");
                Console.WriteLine($"总计: {allServers.Count} 台");
                Console.WriteLine($"在线: {onlineCount} 台");
                Console.WriteLine($"离线: {offlineCount} 台");
                Console.WriteLine($"错误: {errorCount} 台");
                Console.WriteLine($"完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            // 输出每台服务器的工作流汇总
            Console.WriteLine($"\n=== 各服务器工作流汇总 ===");
            foreach (var result in results)
            {
                var serverName = result.GetType().GetProperty("ServerName")?.GetValue(result)?.ToString() ?? "未知服务器";
                var isOnline = (bool)(result.GetType().GetProperty("IsOnline")?.GetValue(result) ?? false);
                var workflows = result.GetType().GetProperty("SupportedWorkflows")?.GetValue(result) as string[] ?? new string[0];

                Console.WriteLine($"📍 {serverName}:");
                Console.WriteLine($"   状态: {(isOnline ? "🟢 在线" : "🔴 离线")}");
                if (isOnline && workflows.Length > 0)
                {
                    Console.WriteLine($"   工作流: {workflows.Length} 个");
                    foreach (var workflow in workflows)
                    {
                        Console.WriteLine($"     - {workflow}");
                    }
                }
                else if (isOnline)
                {
                    Console.WriteLine($"   工作流: 无");
                }
                else
                {
                    Console.WriteLine($"   工作流: 无法获取（服务器离线）");
                }
                Console.WriteLine();
            }

                return summary;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 服务器初始化失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 测试单个服务器连通性并更新硬件信息
        /// </summary>
        /// <param name="server">服务器信息</param>
        /// <returns>测试结果</returns>
        private async Task<dynamic> TestServerAndUpdateInfo(ComfyUIServer server)
        {
            var result = new
            {
                ServerId = server.id,
                ServerName = server.serverName,
                ServerUrl = server.serverUrl,
                Port = server.port,
                IsOnline = false,
                ResponseTime = 0,
                SystemInfo = "",
                SupportedWorkflows = new string[0],
                Error = "",
                UpdateTime = DateTime.Now
            };

            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);

                var baseUrl = $"http://{server.serverUrl}:{server.port}";
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // 1. 测试基本连通性
                Console.WriteLine($"  🔗 测试连通性: {baseUrl}");
                var response = await httpClient.GetAsync(baseUrl);
                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"  ✅ 连接成功 (响应时间: {stopwatch.ElapsedMilliseconds}ms)");

                    // 2. 获取系统信息
                    Console.WriteLine($"  📊 获取系统信息...");
                    var systemInfo = await GetServerSystemInfo(httpClient, baseUrl);

                    // 3. 获取支持的工作流
                    Console.WriteLine($"  🔧 获取支持的工作流...");
                    var supportedWorkflows = await GetServerSupportedWorkflows(httpClient, baseUrl);

                    // 详细输出工作流信息
                    Console.WriteLine($"  📋 服务器 {server.serverName} 的工作流详情:");
                    if (supportedWorkflows != null && supportedWorkflows.Length > 0)
                    {
                        Console.WriteLine($"      ✅ 发现 {supportedWorkflows.Length} 个工作流:");
                        for (int i = 0; i < supportedWorkflows.Length; i++)
                        {
                            Console.WriteLine($"      [{i + 1}] {supportedWorkflows[i]}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"      ❌ 未发现任何工作流文件");
                    }

                    // 4. 更新数据库
                    await UpdateServerStatusAndInfo(server.id, 1, systemInfo, supportedWorkflows);

                    result = new
                    {
                        ServerId = server.id,
                        ServerName = server.serverName,
                        ServerUrl = server.serverUrl,
                        Port = server.port,
                        IsOnline = true,
                        ResponseTime = (int)stopwatch.ElapsedMilliseconds,
                        SystemInfo = systemInfo,
                        SupportedWorkflows = supportedWorkflows ?? new string[0],
                        Error = "",
                        UpdateTime = DateTime.Now
                    };
                }
                else
                {
                    Console.WriteLine($"  ❌ 连接失败: HTTP {response.StatusCode}");
                    Console.WriteLine($"  📋 服务器 {server.serverName} 的工作流详情:");
                    Console.WriteLine($"      ❌ 服务器离线，无法获取工作流信息");
                    await UpdateServerStatusAndInfo(server.id, 0, $"连接失败: HTTP {response.StatusCode}", null);

                    result = new
                    {
                        ServerId = server.id,
                        ServerName = server.serverName,
                        ServerUrl = server.serverUrl,
                        Port = server.port,
                        IsOnline = false,
                        ResponseTime = (int)stopwatch.ElapsedMilliseconds,
                        SystemInfo = "",
                        SupportedWorkflows = new string[0],
                        Error = $"HTTP {response.StatusCode}",
                        UpdateTime = DateTime.Now
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 连接异常: {ex.Message}");
                Console.WriteLine($"  📋 服务器 {server.serverName} 的工作流详情:");
                Console.WriteLine($"      ❌ 连接异常，无法获取工作流信息");
                await UpdateServerStatusAndInfo(server.id, 0, $"连接异常: {ex.Message}", null);

                result = new
                {
                    ServerId = server.id,
                    ServerName = server.serverName,
                    ServerUrl = server.serverUrl,
                    Port = server.port,
                    IsOnline = false,
                    ResponseTime = 0,
                    SystemInfo = "",
                    SupportedWorkflows = new string[0],
                    Error = ex.Message,
                    UpdateTime = DateTime.Now
                };
            }

            return result;
        }

        /// <summary>
        /// 获取服务器系统信息
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>格式化的系统信息</returns>
        private async Task<string> GetServerSystemInfo(HttpClient httpClient, string baseUrl)
        {
            try
            {
                // 尝试获取ComfyUI的系统信息
                var systemStatsUrl = $"{baseUrl}/system_stats";
                Console.WriteLine($"    📡 尝试获取系统统计: {systemStatsUrl}");

                var response = await httpClient.GetAsync(systemStatsUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"    ✅ 系统信息获取成功");

                    return FormatSystemInfo(content);
                }
                else
                {
                    Console.WriteLine($"    ⚠️  系统统计API不可用: {response.StatusCode}");

                    // 尝试获取基本信息
                    return await GetBasicServerInfo(httpClient, baseUrl);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ⚠️  获取系统信息失败: {ex.Message}");
                return await GetBasicServerInfo(httpClient, baseUrl);
            }
        }

        /// <summary>
        /// 获取基本服务器信息
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>基本信息</returns>
        private async Task<string> GetBasicServerInfo(HttpClient httpClient, string baseUrl)
        {
            try
            {
                var info = new System.Text.StringBuilder();
                info.AppendLine("=== ComfyUI 服务器信息 ===");
                info.AppendLine($"服务器地址: {baseUrl}");
                info.AppendLine($"连接状态: 正常");
                info.AppendLine($"检测时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 尝试获取队列信息
                try
                {
                    var queueUrl = $"{baseUrl}/queue";
                    var queueResponse = await httpClient.GetAsync(queueUrl);
                    if (queueResponse.IsSuccessStatusCode)
                    {
                        var queueContent = await queueResponse.Content.ReadAsStringAsync();
                        var queueData = System.Text.Json.JsonDocument.Parse(queueContent);

                        if (queueData.RootElement.TryGetProperty("queue_running", out var running) &&
                            queueData.RootElement.TryGetProperty("queue_pending", out var pending))
                        {
                            var runningCount = running.GetArrayLength();
                            var pendingCount = pending.GetArrayLength();

                            info.AppendLine($"队列状态: 运行中 {runningCount} 个，等待中 {pendingCount} 个");
                        }
                    }
                }
                catch
                {
                    info.AppendLine("队列状态: 无法获取");
                }

                // 尝试获取历史记录信息
                try
                {
                    var historyUrl = $"{baseUrl}/history";
                    var historyResponse = await httpClient.GetAsync(historyUrl);
                    if (historyResponse.IsSuccessStatusCode)
                    {
                        info.AppendLine("历史记录API: 可用");
                    }
                }
                catch
                {
                    info.AppendLine("历史记录API: 不可用");
                }

                info.AppendLine($"备注: 基本连接正常，但无法获取详细硬件信息");

                return info.ToString();
            }
            catch (Exception ex)
            {
                return $"基本信息获取失败: {ex.Message}\n检测时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            }
        }

        /// <summary>
        /// 获取服务器上的工作流列表
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>工作流文件名数组</returns>
        private async Task<string[]> GetServerSupportedWorkflows(HttpClient httpClient, string baseUrl)
        {
            try
            {
                var workflowList = new List<string>();

                // 1. 尝试获取用户工作流目录
                Console.WriteLine($"    📁 尝试获取用户工作流...");
                var userWorkflows = await GetUserWorkflows(httpClient, baseUrl);
                if (userWorkflows.Count > 0)
                {
                    workflowList.AddRange(userWorkflows);
                    Console.WriteLine($"    ✅ 发现 {userWorkflows.Count} 个用户工作流");
                }

                // 2. 尝试获取示例工作流
                Console.WriteLine($"    📚 尝试获取示例工作流...");
                var exampleWorkflows = await GetExampleWorkflows(httpClient, baseUrl);
                if (exampleWorkflows.Count > 0)
                {
                    workflowList.AddRange(exampleWorkflows);
                    Console.WriteLine($"    ✅ 发现 {exampleWorkflows.Count} 个示例工作流");
                }

                // 3. 尝试通过API获取工作流列表
                Console.WriteLine($"    🔍 尝试通过API获取工作流列表...");
                var apiWorkflows = await GetWorkflowsFromAPI(httpClient, baseUrl);
                if (apiWorkflows.Count > 0)
                {
                    workflowList.AddRange(apiWorkflows);
                    Console.WriteLine($"    ✅ 通过API发现 {apiWorkflows.Count} 个工作流");
                }

                // 4. 如果没有获取到任何工作流，返回空数组
                if (workflowList.Count == 0)
                {
                    Console.WriteLine($"    ⚠️  未发现任何工作流文件");
                    return new string[0];
                }

                // 去重并排序
                var uniqueWorkflows = workflowList.Distinct().OrderBy(w => w).ToArray();
                Console.WriteLine($"    🎯 最终发现的工作流: {string.Join(", ", uniqueWorkflows)}");

                return uniqueWorkflows;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ❌ 获取工作流列表失败: {ex.Message}");
                return new string[0];
            }
        }

        /// <summary>
        /// 获取用户工作流
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>用户工作流列表</returns>
        private async Task<List<string>> GetUserWorkflows(HttpClient httpClient, string baseUrl)
        {
            var workflows = new List<string>();
            try
            {
                // 尝试访问用户工作流目录的API
                var userWorkflowsUrls = new[]
                {
                    // ComfyUI文件系统API - 基于源码分析的正确用法
                    $"{baseUrl}/view?filename=XL模型漫画.json&type=input", // 直接尝试在input目录找目标文件
                    $"{baseUrl}/view?filename=XL模型漫画.json&type=output", // 直接尝试在output目录找目标文件
                    $"{baseUrl}/view?filename=XL模型漫画.json", // 直接尝试找目标文件（默认output）

                    // 尝试不同的目录结构
                    $"{baseUrl}/view?filename=workflows/XL模型漫画.json&type=input", // input/workflows目录
                    $"{baseUrl}/view?filename=workflows/XL模型漫画.json&type=output", // output/workflows目录
                    $"{baseUrl}/view?filename=user/XL模型漫画.json&type=input", // input/user目录
                    $"{baseUrl}/view?filename=user/XL模型漫画.json&type=output", // output/user目录

                    // 尝试子文件夹
                    $"{baseUrl}/view?filename=XL模型漫画.json&subfolder=workflows&type=input", // 使用subfolder参数
                    $"{baseUrl}/view?filename=XL模型漫画.json&subfolder=workflows&type=output", // 使用subfolder参数
                    $"{baseUrl}/view?filename=XL模型漫画.json&subfolder=user&type=input", // 用户子文件夹
                    $"{baseUrl}/view?filename=XL模型漫画.json&subfolder=user&type=output", // 用户子文件夹

                    // ComfyUI文件浏览器目录列表（不指定具体文件，看看能否获取目录列表）
                    $"{baseUrl}/view?filename=", // ComfyUI文件浏览器根目录
                    $"{baseUrl}/view?filename=workflows", // 工作流目录
                    $"{baseUrl}/view?filename=user", // 用户目录
                    $"{baseUrl}/view?filename=input", // 输入目录
                    $"{baseUrl}/view?filename=output", // 输出目录

                    // 可能的文件列表API
                    $"{baseUrl}/api/view", // API文件浏览
                    $"{baseUrl}/api/view?path=", // API文件浏览带路径
                    $"{baseUrl}/api/view?path=workflows", // API工作流目录
                    $"{baseUrl}/api/view?path=user", // API用户目录
                    $"{baseUrl}/api/view?path=user/default/workflows", // API默认工作流目录

                    // 基于搜索结果的新路径
                    $"{baseUrl}/user/default/ComfyUI-Connect/workflows", // ComfyUI-Connect扩展的工作流目录
                    $"{baseUrl}/user/default/workflows", // 默认用户工作流目录
                    $"{baseUrl}/user/workflows", // 用户工作流目录
                    $"{baseUrl}/api/workflows", // API工作流
                    $"{baseUrl}/workflows", // 工作流目录
                    $"{baseUrl}/user", // 用户目录
                    $"{baseUrl}/files/user", // 用户文件
                    $"{baseUrl}/view", // ComfyUI的文件浏览器

                    // 尝试直接访问工作流文件
                    $"{baseUrl}/XL模型漫画.json", // 直接尝试访问目标工作流
                    $"{baseUrl}/user/XL模型漫画.json", // 用户目录下的工作流
                    $"{baseUrl}/workflows/XL模型漫画.json", // 工作流目录下的文件
                    $"{baseUrl}/user/default/workflows/XL模型漫画.json", // 默认工作流目录
                    $"{baseUrl}/input/XL模型漫画.json", // 输入目录
                    $"{baseUrl}/output/XL模型漫画.json", // 输出目录

                    // 可能的静态文件服务
                    $"{baseUrl}/static/workflows/XL模型漫画.json", // 静态工作流
                    $"{baseUrl}/assets/workflows/XL模型漫画.json", // 资源工作流
                    $"{baseUrl}/public/workflows/XL模型漫画.json", // 公共工作流
                };

                foreach (var url in userWorkflowsUrls)
                {
                    try
                    {
                        Console.WriteLine($"      🔍 尝试用户工作流API: {url}");
                        var response = await httpClient.GetAsync(url);
                        Console.WriteLine($"      📡 响应状态: {response.StatusCode}");

                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            Console.WriteLine($"      📄 响应内容长度: {content.Length} 字符");
                            Console.WriteLine($"      📄 响应内容预览: {(content.Length > 200 ? content.Substring(0, 200) + "..." : content)}");

                            var foundWorkflows = ParseWorkflowList(content, "user");
                            if (foundWorkflows.Count > 0)
                            {
                                workflows.AddRange(foundWorkflows);
                                Console.WriteLine($"      ✅ 从 {url} 发现 {foundWorkflows.Count} 个用户工作流");
                                break; // 找到工作流就停止
                            }
                            else
                            {
                                Console.WriteLine($"      ⚠️  {url} 响应成功但未解析到工作流");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"      ❌ {url} 响应失败: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      ❌ {url} 请求异常: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  获取用户工作流失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 获取示例工作流
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>示例工作流列表</returns>
        private async Task<List<string>> GetExampleWorkflows(HttpClient httpClient, string baseUrl)
        {
            var workflows = new List<string>();
            try
            {
                // 尝试访问示例工作流目录
                var exampleUrls = new[]
                {
                    $"{baseUrl}/examples",
                    $"{baseUrl}/web/examples",
                    $"{baseUrl}/api/examples",
                    $"{baseUrl}/files/examples",
                    $"{baseUrl}/web/scripts/defaultGraph.js", // ComfyUI默认图
                    $"{baseUrl}/web/scripts/ui.js", // UI脚本可能包含示例
                };

                foreach (var url in exampleUrls)
                {
                    try
                    {
                        Console.WriteLine($"      🔍 尝试示例工作流API: {url}");
                        var response = await httpClient.GetAsync(url);
                        Console.WriteLine($"      📡 响应状态: {response.StatusCode}");

                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            Console.WriteLine($"      📄 响应内容长度: {content.Length} 字符");
                            Console.WriteLine($"      📄 响应内容预览: {(content.Length > 200 ? content.Substring(0, 200) + "..." : content)}");

                            var foundWorkflows = ParseWorkflowList(content, "example");
                            if (foundWorkflows.Count > 0)
                            {
                                workflows.AddRange(foundWorkflows);
                                Console.WriteLine($"      ✅ 从 {url} 发现 {foundWorkflows.Count} 个示例工作流");
                                break; // 找到工作流就停止
                            }
                            else
                            {
                                Console.WriteLine($"      ⚠️  {url} 响应成功但未解析到工作流");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"      ❌ {url} 响应失败: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      ❌ {url} 请求异常: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  获取示例工作流失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 通过API获取工作流列表
        /// </summary>
        /// <param name="httpClient">HTTP客户端</param>
        /// <param name="baseUrl">服务器基础URL</param>
        /// <returns>API工作流列表</returns>
        private async Task<List<string>> GetWorkflowsFromAPI(HttpClient httpClient, string baseUrl)
        {
            var workflows = new List<string>();
            try
            {
                // 尝试各种可能的工作流文件API端点
                var apiUrls = new[]
                {
                    // ComfyUI官方工作流模板API（重要！）
                    $"{baseUrl}/api/workflow_templates", // 官方工作流模板API

                    // ComfyUI工作流相关API
                    $"{baseUrl}/api/workflows", // 工作流API
                    $"{baseUrl}/workflows", // 工作流目录
                    $"{baseUrl}/workflow", // 单个工作流
                    $"{baseUrl}/api/workflow", // API工作流
                    $"{baseUrl}/user/workflows", // 用户工作流
                    $"{baseUrl}/saved_workflows", // 保存的工作流
                    $"{baseUrl}/api/saved_workflows", // API保存的工作流

                    // ComfyUI文件浏览相关
                    $"{baseUrl}/view", // 文件浏览器
                    $"{baseUrl}/api/view", // API文件浏览
                    $"{baseUrl}/browse", // 浏览文件
                    $"{baseUrl}/api/browse", // API浏览文件
                    $"{baseUrl}/files", // 文件列表
                    $"{baseUrl}/api/files", // API文件列表

                    // ComfyUI特定的工作流端点
                    $"{baseUrl}/workflow_api", // 工作流API
                    $"{baseUrl}/load_workflow", // 加载工作流
                    $"{baseUrl}/save_workflow", // 保存工作流
                    $"{baseUrl}/list_workflows", // 列出工作流
                    $"{baseUrl}/get_workflows", // 获取工作流

                    // 可能的JSON文件端点
                    $"{baseUrl}/workflows.json", // 工作流JSON
                    $"{baseUrl}/workflow_list.json", // 工作流列表JSON
                    $"{baseUrl}/api/workflows.json", // API工作流JSON

                    // ComfyUI模板路径（基于源码中的 FrontendManager.templates_path()）
                    $"{baseUrl}/templates", // 工作流模板目录
                    $"{baseUrl}/templates/XL模型漫画.json", // 模板目录中的目标文件

                    // 尝试其他可能的工作流存储位置
                    $"{baseUrl}/user/workflows", // 用户工作流目录
                    $"{baseUrl}/user/workflows/XL模型漫画.json", // 用户工作流中的目标文件
                    $"{baseUrl}/saved_workflows", // 保存的工作流目录
                    $"{baseUrl}/saved_workflows/XL模型漫画.json", // 保存的工作流中的目标文件

                    // 历史和队列（可能包含工作流信息）
                    $"{baseUrl}/history", // 历史记录
                    $"{baseUrl}/queue", // 队列信息
                    $"{baseUrl}/api/history", // API历史
                    $"{baseUrl}/api/queue", // API队列

                    // 根目录和其他
                    $"{baseUrl}/", // 根目录
                    $"{baseUrl}/index.html", // 主页
                    $"{baseUrl}/app.js", // 应用脚本
                };

                foreach (var url in apiUrls)
                {
                    try
                    {
                        Console.WriteLine($"      🔍 尝试API工作流: {url}");
                        var response = await httpClient.GetAsync(url);
                        Console.WriteLine($"      📡 响应状态: {response.StatusCode}");

                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            Console.WriteLine($"      📄 响应内容长度: {content.Length} 字符");

                            // 对于特别长的响应，只显示前面部分
                            var preview = content.Length > 500 ? content.Substring(0, 500) + "..." : content;
                            Console.WriteLine($"      📄 响应内容预览: {preview}");

                            var foundWorkflows = ParseWorkflowList(content, "api");
                            if (foundWorkflows.Count > 0)
                            {
                                workflows.AddRange(foundWorkflows);
                                Console.WriteLine($"      ✅ 从 {url} 发现 {foundWorkflows.Count} 个API工作流");

                                // 检查是否包含目标工作流
                                var targetWorkflow = "XL模型漫画.json";
                                if (foundWorkflows.Any(w => w.Contains("XL模型漫画") || w.Contains(targetWorkflow)))
                                {
                                    Console.WriteLine($"      🎯 找到目标工作流！包含 'XL模型漫画' 的工作流");
                                }

                                // 不要break，继续尝试其他API，可能有更多工作流
                            }
                            else
                            {
                                Console.WriteLine($"      ⚠️  {url} 响应成功但未解析到工作流");
                            }
                        }
                        else
                        {
                            Console.WriteLine($"      ❌ {url} 响应失败: {response.StatusCode}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"      ❌ {url} 请求异常: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  通过API获取工作流失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 解析工作流列表
        /// </summary>
        /// <param name="content">响应内容</param>
        /// <param name="source">来源类型</param>
        /// <returns>工作流名称列表</returns>
        private List<string> ParseWorkflowList(string content, string source)
        {
            var workflows = new List<string>();
            try
            {
                // 尝试解析JSON响应
                using var document = System.Text.Json.JsonDocument.Parse(content);
                var root = document.RootElement;

                Console.WriteLine($"      🔍 解析{source}的JSON内容，根元素类型: {root.ValueKind}");

                if (root.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    // 如果是数组，遍历每个元素
                    foreach (var item in root.EnumerateArray())
                    {
                        var workflowName = ExtractWorkflowName(item, source);
                        if (!string.IsNullOrEmpty(workflowName))
                        {
                            workflows.Add(workflowName);
                        }
                    }
                }
                else if (root.ValueKind == System.Text.Json.JsonValueKind.Object)
                {
                    // 如果是对象，尝试找到工作流相关的属性
                    Console.WriteLine($"      🔍 JSON对象有 {root.EnumerateObject().Count()} 个属性");

                    foreach (var property in root.EnumerateObject())
                    {
                        Console.WriteLine($"      🔑 属性: {property.Name}, 类型: {property.Value.ValueKind}");

                        // 特别处理不同的API响应格式
                        if (source == "api" && property.Value.ValueKind == System.Text.Json.JsonValueKind.Object)
                        {
                            // 这可能是ComfyUI的历史记录格式
                            var historyWorkflows = ParseHistoryWorkflows(property.Value, property.Name);
                            workflows.AddRange(historyWorkflows);
                        }
                        else if (source == "api" && property.Value.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            // 这可能是工作流模板列表
                            var templateWorkflows = ParseWorkflowTemplates(property.Value, property.Name);
                            workflows.AddRange(templateWorkflows);
                        }
                        else if (property.Value.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            foreach (var item in property.Value.EnumerateArray())
                            {
                                var workflowName = ExtractWorkflowName(item, source);
                                if (!string.IsNullOrEmpty(workflowName))
                                {
                                    workflows.Add(workflowName);
                                }
                            }
                        }
                        else if (property.Name.ToLower().Contains("workflow") ||
                                property.Name.ToLower().Contains("prompt") ||
                                property.Name.ToLower().Contains("name"))
                        {
                            var workflowName = ExtractWorkflowName(property.Value, source);
                            if (!string.IsNullOrEmpty(workflowName))
                            {
                                workflows.Add(workflowName);
                            }
                        }
                    }
                }
            }
            catch (System.Text.Json.JsonException)
            {
                // 如果不是JSON，尝试解析为HTML或文本
                workflows.AddRange(ParseHtmlOrTextWorkflowList(content, source));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  解析{source}工作流列表失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 解析ComfyUI历史记录中的工作流信息
        /// </summary>
        /// <param name="historyItem">历史记录项</param>
        /// <param name="historyId">历史记录ID</param>
        /// <returns>工作流名称列表</returns>
        private List<string> ParseHistoryWorkflows(System.Text.Json.JsonElement historyItem, string historyId)
        {
            var workflows = new List<string>();
            try
            {
                Console.WriteLine($"      📜 解析历史记录 {historyId}...");

                // ComfyUI历史记录格式通常包含 prompt, outputs 等
                if (historyItem.TryGetProperty("prompt", out var promptElement) &&
                    promptElement.ValueKind == System.Text.Json.JsonValueKind.Array &&
                    promptElement.GetArrayLength() >= 3)
                {
                    // prompt数组的第三个元素通常包含工作流数据
                    var workflowData = promptElement[2];
                    if (workflowData.ValueKind == System.Text.Json.JsonValueKind.Object)
                    {
                        Console.WriteLine($"      🔍 在历史记录中发现工作流数据，包含 {workflowData.EnumerateObject().Count()} 个节点");

                        // 查找可能的工作流名称或标题
                        foreach (var node in workflowData.EnumerateObject())
                        {
                            if (node.Value.ValueKind == System.Text.Json.JsonValueKind.Object)
                            {
                                // 检查节点的_meta信息
                                if (node.Value.TryGetProperty("_meta", out var metaElement) &&
                                    metaElement.ValueKind == System.Text.Json.JsonValueKind.Object)
                                {
                                    if (metaElement.TryGetProperty("title", out var titleElement) &&
                                        titleElement.ValueKind == System.Text.Json.JsonValueKind.String)
                                    {
                                        var title = titleElement.GetString();
                                        if (!string.IsNullOrEmpty(title) && !workflows.Contains(title))
                                        {
                                            workflows.Add(title);
                                            Console.WriteLine($"      ✅ 从历史记录发现工作流节点: {title}");

                                            // 检查是否包含目标工作流
                                            if (title.Contains("XL模型漫画") || title.Contains("XL") && title.Contains("模型") && title.Contains("漫画"))
                                            {
                                                Console.WriteLine($"      🎯🎯🎯 在历史记录中找到目标工作流节点！！！ {title}");
                                            }
                                        }
                                    }
                                }

                                // 检查class_type，可能包含工作流信息
                                if (node.Value.TryGetProperty("class_type", out var classTypeElement) &&
                                    classTypeElement.ValueKind == System.Text.Json.JsonValueKind.String)
                                {
                                    var classType = classTypeElement.GetString();
                                    if (!string.IsNullOrEmpty(classType) &&
                                        (classType.Contains("workflow") || classType.Contains("Workflow") ||
                                         classType.Contains("XL") || classType.Contains("模型") || classType.Contains("漫画")))
                                    {
                                        if (!workflows.Contains(classType))
                                        {
                                            workflows.Add(classType);
                                            Console.WriteLine($"      ✅ 从历史记录发现工作流类型: {classType}");
                                        }
                                    }
                                }
                            }
                        }

                        // 如果没有找到具体的工作流名称，但有工作流数据，记录一个通用名称
                        if (workflows.Count == 0)
                        {
                            var workflowName = $"历史工作流_{historyId.Substring(0, 8)}";
                            workflows.Add(workflowName);
                            Console.WriteLine($"      ✅ 记录历史工作流: {workflowName}");
                        }
                    }
                }

                // 检查是否有outputs，可能包含生成的文件信息
                if (historyItem.TryGetProperty("outputs", out var outputsElement) &&
                    outputsElement.ValueKind == System.Text.Json.JsonValueKind.Object)
                {
                    Console.WriteLine($"      📤 历史记录包含输出信息");
                    // 这里可以进一步解析输出信息，但主要关注工作流名称
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  解析历史记录工作流失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 从JSON元素中提取工作流名称
        /// </summary>
        /// <param name="element">JSON元素</param>
        /// <param name="source">来源类型</param>
        /// <returns>工作流名称</returns>
        private string ExtractWorkflowName(System.Text.Json.JsonElement element, string source)
        {
            try
            {
                if (element.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    var value = element.GetString();
                    if (!string.IsNullOrEmpty(value) && (value.EndsWith(".json") || value.Contains("workflow")))
                    {
                        return value;
                    }
                }
                else if (element.ValueKind == System.Text.Json.JsonValueKind.Object)
                {
                    // 尝试找到名称相关的属性
                    var nameProperties = new[] { "name", "filename", "title", "workflow_name", "prompt_id" };

                    foreach (var prop in nameProperties)
                    {
                        if (element.TryGetProperty(prop, out var nameElement) &&
                            nameElement.ValueKind == System.Text.Json.JsonValueKind.String)
                        {
                            var name = nameElement.GetString();
                            if (!string.IsNullOrEmpty(name))
                            {
                                return name;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  提取{source}工作流名称失败: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 解析HTML或文本格式的工作流列表
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="source">来源类型</param>
        /// <returns>工作流名称列表</returns>
        private List<string> ParseHtmlOrTextWorkflowList(string content, string source)
        {
            var workflows = new List<string>();
            try
            {
                Console.WriteLine($"      🔍 尝试解析{source}的HTML/文本内容...");

                // 使用正则表达式查找可能的工作流文件名，特别关注中文工作流
                var patterns = new[]
                {
                    // 专门匹配中文工作流文件
                    @"[\u4e00-\u9fa5]+[\w\-_\u4e00-\u9fa5]*\.json",  // 以中文开头的.json文件
                    @"[\w\-_]*[\u4e00-\u9fa5]+[\w\-_\u4e00-\u9fa5]*\.json",  // 包含中文的.json文件

                    // 通用.json文件匹配
                    @"[\w\-_\u4e00-\u9fa5]+\.json",  // .json文件（包括中文）
                    @"[^""'\s,]+\.json",  // 更宽泛的.json文件匹配

                    // HTML和JSON中的文件引用
                    @"href=[""']([^""']*\.json)[""']", // HTML链接中的.json文件
                    @">([^<]*\.json)<",  // HTML标签中的.json文件
                    @"""([^""]*\.json)""", // 引号中的.json文件
                    @"'([^']*\.json)'", // 单引号中的.json文件

                    // 属性中的文件名
                    @"filename[""':\s]*([^""',\s]+\.json)", // filename属性
                    @"name[""':\s]*([^""',\s]+\.json)", // name属性
                    @"title[""':\s]*([^""',\s]+\.json)", // title属性
                    @"path[""':\s]*([^""',\s]+\.json)", // path属性

                    // 特殊模式：寻找XL模型漫画相关
                    @"XL[^""'\s,]*\.json", // 以XL开头的json文件
                    @"[^""'\s,]*模型[^""'\s,]*\.json", // 包含"模型"的json文件
                    @"[^""'\s,]*漫画[^""'\s,]*\.json", // 包含"漫画"的json文件

                    // JSON对象中的工作流相关内容
                    @"\{[^}]*""workflow""[^}]*\}", // JSON对象中包含workflow的
                    @"\{[^}]*""prompt""[^}]*\}", // JSON对象中包含prompt的
                };

                foreach (var pattern in patterns)
                {
                    try
                    {
                        var matches = System.Text.RegularExpressions.Regex.Matches(content, pattern,
                            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                        Console.WriteLine($"      🔍 模式 '{pattern}' 找到 {matches.Count} 个匹配");

                        foreach (System.Text.RegularExpressions.Match match in matches)
                        {
                            var workflowName = match.Groups.Count > 1 ? match.Groups[1].Value : match.Value;
                            if (!string.IsNullOrEmpty(workflowName) && !workflows.Contains(workflowName))
                            {
                                // 清理工作流名称
                                workflowName = workflowName.Trim().Trim('"').Trim('\'');
                                if (!string.IsNullOrEmpty(workflowName))
                                {
                                    workflows.Add(workflowName);
                                    Console.WriteLine($"      ✅ 发现工作流: {workflowName}");

                                    // 特别检查目标工作流
                                    if (workflowName.Contains("XL模型漫画") || workflowName.Contains("XL") && workflowName.Contains("模型") && workflowName.Contains("漫画"))
                                    {
                                        Console.WriteLine($"      🎯🎯🎯 找到目标工作流！！！ {workflowName}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception patternEx)
                    {
                        Console.WriteLine($"      ⚠️  模式 '{pattern}' 解析失败: {patternEx.Message}");
                    }
                }

                // 如果是HTML内容，尝试查找文件列表
                if (content.Contains("<html") || content.Contains("<!DOCTYPE"))
                {
                    Console.WriteLine($"      🌐 检测到HTML内容，尝试解析文件列表...");
                    // 查找可能的文件链接
                    var linkPattern = @"<a[^>]*href=[""']([^""']*)[""'][^>]*>([^<]*)</a>";
                    var linkMatches = System.Text.RegularExpressions.Regex.Matches(content, linkPattern,
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    Console.WriteLine($"      🔗 找到 {linkMatches.Count} 个链接");
                    foreach (System.Text.RegularExpressions.Match linkMatch in linkMatches)
                    {
                        var href = linkMatch.Groups[1].Value;
                        var text = linkMatch.Groups[2].Value;
                        Console.WriteLine($"      🔗 链接: {href} -> {text}");

                        if (href.EndsWith(".json") || text.EndsWith(".json") ||
                            href.Contains("workflow") || text.Contains("workflow"))
                        {
                            var workflowName = href.EndsWith(".json") ? href : text;
                            if (!workflows.Contains(workflowName))
                            {
                                workflows.Add(workflowName);
                                Console.WriteLine($"      ✅ 从链接发现工作流: {workflowName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  解析{source}HTML/文本工作流列表失败: {ex.Message}");
            }

            Console.WriteLine($"      📊 {source}解析结果: 共发现 {workflows.Count} 个工作流");
            return workflows;
        }

        /// <summary>
        /// 解析ComfyUI工作流模板
        /// </summary>
        /// <param name="templatesArray">工作流模板数组</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>工作流名称列表</returns>
        private List<string> ParseWorkflowTemplates(System.Text.Json.JsonElement templatesArray, string propertyName)
        {
            var workflows = new List<string>();
            try
            {
                Console.WriteLine($"      📋 解析工作流模板数组 {propertyName}，包含 {templatesArray.GetArrayLength()} 个模板");

                foreach (var template in templatesArray.EnumerateArray())
                {
                    if (template.ValueKind == System.Text.Json.JsonValueKind.Object)
                    {
                        // 尝试获取工作流文件名
                        var workflowName = ExtractWorkflowNameFromTemplate(template);
                        if (!string.IsNullOrEmpty(workflowName))
                        {
                            workflows.Add(workflowName);
                            Console.WriteLine($"      ✅ 发现工作流模板: {workflowName}");

                            // 检查是否包含目标工作流
                            if (workflowName.Contains("XL模型漫画") ||
                                (workflowName.Contains("XL") && workflowName.Contains("模型") && workflowName.Contains("漫画")))
                            {
                                Console.WriteLine($"      🎯🎯🎯 找到目标工作流模板！！！ {workflowName}");
                            }
                        }
                    }
                    else if (template.ValueKind == System.Text.Json.JsonValueKind.String)
                    {
                        var templateName = template.GetString();
                        if (!string.IsNullOrEmpty(templateName))
                        {
                            workflows.Add(templateName);
                            Console.WriteLine($"      ✅ 发现工作流模板: {templateName}");

                            // 检查是否包含目标工作流
                            if (templateName.Contains("XL模型漫画") ||
                                (templateName.Contains("XL") && templateName.Contains("模型") && templateName.Contains("漫画")))
                            {
                                Console.WriteLine($"      🎯🎯🎯 找到目标工作流模板！！！ {templateName}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  解析工作流模板失败: {ex.Message}");
            }
            return workflows;
        }

        /// <summary>
        /// 从工作流模板对象中提取工作流名称
        /// </summary>
        /// <param name="template">工作流模板对象</param>
        /// <returns>工作流名称</returns>
        private string ExtractWorkflowNameFromTemplate(System.Text.Json.JsonElement template)
        {
            try
            {
                // 尝试各种可能的属性名
                var nameProperties = new[] { "name", "filename", "title", "workflow_name", "file", "path", "id" };

                foreach (var prop in nameProperties)
                {
                    if (template.TryGetProperty(prop, out var nameElement) &&
                        nameElement.ValueKind == System.Text.Json.JsonValueKind.String)
                    {
                        var name = nameElement.GetString();
                        if (!string.IsNullOrEmpty(name))
                        {
                            return name;
                        }
                    }
                }

                // 如果没有找到标准属性，尝试查找包含.json的属性
                foreach (var property in template.EnumerateObject())
                {
                    if (property.Value.ValueKind == System.Text.Json.JsonValueKind.String)
                    {
                        var value = property.Value.GetString();
                        if (!string.IsNullOrEmpty(value) && value.EndsWith(".json"))
                        {
                            return value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"      ⚠️  提取工作流模板名称失败: {ex.Message}");
            }
            return null;
        }



        /// <summary>
        /// 格式化系统信息为可读格式
        /// </summary>
        /// <param name="jsonContent">JSON格式的系统信息</param>
        /// <returns>格式化后的系统信息</returns>
        private string FormatSystemInfo(string jsonContent)
        {
            try
            {
                using var document = System.Text.Json.JsonDocument.Parse(jsonContent);
                var root = document.RootElement;

                var formattedInfo = new System.Text.StringBuilder();
                formattedInfo.AppendLine("=== ComfyUI 系统信息 ===");

                // 解析系统信息
                if (root.TryGetProperty("system", out var system))
                {
                    formattedInfo.AppendLine("【系统信息】");

                    if (system.TryGetProperty("os", out var os))
                        formattedInfo.AppendLine($"操作系统: {os.GetString()}");

                    if (system.TryGetProperty("comfyui_version", out var version))
                        formattedInfo.AppendLine($"ComfyUI版本: {version.GetString()}");

                    if (system.TryGetProperty("python_version", out var pythonVersion))
                        formattedInfo.AppendLine($"Python版本: {pythonVersion.GetString()}");

                    if (system.TryGetProperty("pytorch_version", out var pytorchVersion))
                        formattedInfo.AppendLine($"PyTorch版本: {pytorchVersion.GetString()}");

                    if (system.TryGetProperty("ram_total", out var ramTotal))
                    {
                        var ramTotalGB = ramTotal.GetInt64() / (1024.0 * 1024.0 * 1024.0);
                        formattedInfo.AppendLine($"总内存: {ramTotalGB:F1} GB");
                    }

                    if (system.TryGetProperty("ram_free", out var ramFree))
                    {
                        var ramFreeGB = ramFree.GetInt64() / (1024.0 * 1024.0 * 1024.0);
                        formattedInfo.AppendLine($"可用内存: {ramFreeGB:F1} GB");
                    }
                }

                // 解析设备信息
                if (root.TryGetProperty("devices", out var devices) && devices.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    formattedInfo.AppendLine("\n【GPU设备】");

                    foreach (var device in devices.EnumerateArray())
                    {
                        if (device.TryGetProperty("name", out var name))
                            formattedInfo.AppendLine($"设备名称: {name.GetString()}");

                        if (device.TryGetProperty("type", out var type))
                            formattedInfo.AppendLine($"设备类型: {type.GetString()}");

                        if (device.TryGetProperty("vram_total", out var vramTotal))
                        {
                            var vramTotalGB = vramTotal.GetInt64() / (1024.0 * 1024.0 * 1024.0);
                            formattedInfo.AppendLine($"显存总量: {vramTotalGB:F1} GB");
                        }

                        if (device.TryGetProperty("vram_free", out var vramFree))
                        {
                            var vramFreeGB = vramFree.GetInt64() / (1024.0 * 1024.0 * 1024.0);
                            formattedInfo.AppendLine($"显存可用: {vramFreeGB:F1} GB");
                        }

                        formattedInfo.AppendLine(); // 空行分隔多个设备
                    }
                }

                formattedInfo.AppendLine($"更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                return formattedInfo.ToString();
            }
            catch (Exception ex)
            {
                return $"系统信息解析失败: {ex.Message}\n原始数据: {jsonContent}\n更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            }
        }

        /// <summary>
        /// 更新服务器状态和系统信息
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="status">状态 (0:离线, 1:在线, 2:忙碌, 3:维护)</param>
        /// <param name="systemInfo">系统信息</param>
        /// <param name="supportedWorkflows">支持的工作流数组</param>
        /// <returns>是否成功</returns>
        private async Task<bool> UpdateServerStatusAndInfo(string serverId, int status, string systemInfo, string[] supportedWorkflows)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    server.status = status;
                    server.description = systemInfo;
                    server.lastHeartbeat = TimeTools.GetTimeStamp();
                    server.UpdateTime = DateTime.Now;

                    // 处理支持的工作流 - 将string[]序列化为JSON字符串存储
                    if (supportedWorkflows != null && supportedWorkflows.Length > 0)
                    {
                        // 配置JSON序列化选项，确保中文字符不被转义
                        var jsonOptions = new System.Text.Json.JsonSerializerOptions
                        {
                            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                            WriteIndented = false
                        };
                        server.supportedWorkflows = System.Text.Json.JsonSerializer.Serialize(supportedWorkflows, jsonOptions);
                        Console.WriteLine($"    🔧 工作流信息已更新: {supportedWorkflows.Length} 种类型");
                    }
                    else
                    {
                        server.supportedWorkflows = "[]"; // 空数组的JSON表示
                        Console.WriteLine($"    🔧 工作流信息已清空");
                    }

                    await Task.Run(() => db.SaveChanges());

                    Console.WriteLine($"    💾 数据库更新成功 - 状态: {GetServerStatusName(status)}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"    ❌ 服务器不存在: {serverId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    ❌ 更新数据库失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取服务器状态名称
        /// </summary>
        /// <param name="status">状态码</param>
        /// <returns>状态名称</returns>
        private string GetServerStatusName(int status)
        {
            return status switch
            {
                0 => "离线",
                1 => "在线",
                2 => "忙碌",
                3 => "维护",
                _ => "未知"
            };
        }

        /// <summary>
        /// 添加ComfyUI服务器
        /// </summary>
        /// <param name="serverName">服务器名称</param>
        /// <param name="serverUrl">服务器地址</param>
        /// <param name="port">端口</param>
        /// <param name="maxConcurrentTasks">最大并发任务数</param>
        /// <param name="description">描述</param>
        /// <returns>服务器ID，如果添加失败返回空字符串</returns>
        public string AddServer(string serverName, string serverUrl, int port, int maxConcurrentTasks = 5, string description = "")
        {
            try
            {
                // 检查服务器是否已存在
                if (IsServerExists(serverUrl, port))
                {
                    var errorMessage = $"服务器已存在: {serverUrl}:{port}，不能重复添加相同IP和端口的服务器";
                    Console.WriteLine($"❌ {errorMessage}");
                    throw new InvalidOperationException(errorMessage);
                }

                var serverId = Guid.NewGuid().ToString();
                var server = new ComfyUIServer
                {
                    id = serverId,
                    serverName = serverName,
                    serverUrl = serverUrl,
                    port = port,
                    status = 0, // 离线
                    maxConcurrentTasks = maxConcurrentTasks,
                    currentTasks = 0,
                    supportedWorkflows = "[]",
                    description = description,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var db = ORMTables.Instance;
                db.ComfyUIServers.Add(server);
                db.SaveChanges();

                Console.WriteLine($"✅ 成功添加ComfyUI服务器: {serverName} ({serverUrl}:{port})");
                return serverId;
            }
            catch (InvalidOperationException)
            {
                // 重新抛出业务逻辑异常，不修改消息
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 添加服务器失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 删除ComfyUI服务器
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>是否成功</returns>
        public bool RemoveServer(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    db.ComfyUIServers.Remove(server);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除服务器: {serverId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除服务器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有服务器列表
        /// </summary>
        /// <returns>服务器列表</returns>
        public List<ComfyUIServer> GetAllServers()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取服务器列表失败: {ex.Message}");
                return new List<ComfyUIServer>();
            }
        }

        /// <summary>
        /// 获取在线服务器列表
        /// </summary>
        /// <returns>在线服务器列表</returns>
        public List<ComfyUIServer> GetOnlineServers()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.Where(s => s.status == 1).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取在线服务器列表失败: {ex.Message}");
                return new List<ComfyUIServer>();
            }
        }

        /// <summary>
        /// 更新服务器信息
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="serverName">服务器名称</param>
        /// <param name="host">主机地址</param>
        /// <param name="port">端口</param>
        /// <param name="maxConcurrentTasks">最大并发任务数</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        public bool UpdateServer(string serverId, string serverName, string host, int port, int maxConcurrentTasks, string description = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server == null)
                {
                    Console.WriteLine($"❌ 服务器不存在: {serverId}");
                    return false;
                }

                // 检查是否有其他服务器使用相同的IP和端口（排除当前服务器）
                var existingServer = db.ComfyUIServers.FirstOrDefault(s => s.serverUrl == host && s.port == port && s.id != serverId);
                if (existingServer != null)
                {
                    var errorMessage = $"更新失败：服务器 {host}:{port} 已被其他服务器使用 (服务器ID: {existingServer.id}, 名称: {existingServer.serverName})";
                    Console.WriteLine($"❌ {errorMessage}");
                    throw new InvalidOperationException(errorMessage);
                }

                server.serverName = serverName;
                server.serverUrl = host;
                server.port = port;
                server.maxConcurrentTasks = maxConcurrentTasks;
                server.description = description;
                server.UpdateTime = DateTime.Now;

                db.SaveChanges();
                Console.WriteLine($"✅ 成功更新服务器: {serverName} ({host}:{port})");
                return true;
            }
            catch (InvalidOperationException)
            {
                // 重新抛出业务逻辑异常，不修改消息
                throw;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 更新服务器失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 更新服务器状态
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="status">状态 (0:离线, 1:在线, 2:忙碌, 3:维护)</param>
        /// <param name="currentTasks">当前任务数</param>
        /// <returns>是否成功</returns>
        public bool UpdateServerStatus(string serverId, int status, int currentTasks = -1)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server != null)
                {
                    server.status = status;
                    if (currentTasks >= 0)
                    {
                        server.currentTasks = currentTasks;
                    }
                    server.lastHeartbeat = TimeTools.GetTimeStamp();
                    server.UpdateTime = DateTime.Now;
                    db.SaveChanges();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新服务器状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 添加工作流配置
        /// </summary>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="workflowJson">工作流JSON配置</param>
        /// <param name="workflowType">工作流类型</param>
        /// <param name="description">描述</param>
        /// <param name="creator">创建者</param>
        /// <returns>工作流ID</returns>
        public string AddWorkflow(string workflowName, string workflowJson, string workflowType, string description = "", string creator = "")
        {
            try
            {
                var workflowId = Guid.NewGuid().ToString();
                var workflow = new ComfyUIWorkflow
                {
                    id = workflowId,
                    workflowName = workflowName,
                    workflowJson = workflowJson,
                    workflowType = workflowType,
                    description = description,
                    workflowVersion = "1.0",
                    creator = creator,
                    isEnabled = true,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var db = ORMTables.Instance;
                db.ComfyUIWorkflows.Add(workflow);
                db.SaveChanges();

                Console.WriteLine($"成功添加工作流: {workflowName}");
                return workflowId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加工作流失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 更新工作流信息
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="workflowJson">工作流JSON配置</param>
        /// <param name="creator">创建者</param>
        /// <param name="workflowType">工作流类型</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        public bool UpdateWorkflow(string workflowId, string workflowName, string workflowJson, string creator, string workflowType, string description = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var workflow = db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
                if (workflow != null)
                {
                    workflow.workflowName = workflowName;
                    workflow.workflowJson = workflowJson;
                    workflow.workflowType = workflowType;
                    workflow.description = description;
                    workflow.creator = creator;
                    workflow.UpdateTime = DateTime.Now;

                    db.SaveChanges();
                    Console.WriteLine($"成功更新工作流: {workflowName}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"工作流不存在: {workflowId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新工作流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有工作流
        /// </summary>
        /// <returns>工作流列表</returns>
        public List<ComfyUIWorkflow> GetAllWorkflows()
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.Where(w => w.isEnabled).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流列表失败: {ex.Message}");
                return new List<ComfyUIWorkflow>();
            }
        }

        /// <summary>
        /// 生成数据库中所有工作流对应的C#调用文件和测试文件到ComfyuiGate文件夹
        /// 一个工作流对应一个类和一个测试类
        /// </summary>
        /// <returns>生成结果信息</returns>
        public string GenerateWorkflowClasses()
        {
            try
            {
                var workflows = GetAllWorkflows();
                if (workflows.Count == 0)
                {
                    return "数据库中没有找到启用的工作流";
                }

                // 确保ComfyuiGate目录存在
                string gateDirectory = Path.Combine(PatchUtil.ResRootPatch, "..", "ComfyuiGate");
                if (!Directory.Exists(gateDirectory))
                {
                    Directory.CreateDirectory(gateDirectory);
                }

                var results = new List<string>();
                int successCount = 0;
                int failCount = 0;

                foreach (var workflow in workflows)
                {
                    try
                    {
                        // 生成工作流调用类
                        var workflowClassResult = GenerateWorkflowClass(workflow, gateDirectory);
                        results.Add(workflowClassResult);

                        // 生成工作流测试类
                        var testClassResult = GenerateWorkflowTestClass(workflow, gateDirectory);
                        results.Add(testClassResult);

                        successCount++;
                        Console.WriteLine($"成功生成工作流类: {workflow.workflowName}");
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        var errorMsg = $"❌ 生成失败: {workflow.workflowName} - {ex.Message}";
                        results.Add(errorMsg);
                        Console.WriteLine(errorMsg);
                    }
                }

                var summary = $"工作流类生成完成！成功: {successCount}, 失败: {failCount}";
                results.Insert(0, summary);
                Console.WriteLine(summary);

                return string.Join("\n", results);
            }
            catch (Exception ex)
            {
                var errorMsg = $"生成工作流类失败: {ex.Message}";
                Console.WriteLine(errorMsg);
                return errorMsg;
            }
        }

        /// <summary>
        /// 生成单个工作流的调用类
        /// </summary>
        /// <param name="workflow">工作流信息</param>
        /// <param name="gateDirectory">输出目录</param>
        /// <returns>生成结果</returns>
        private string GenerateWorkflowClass(ComfyUIWorkflow workflow, string gateDirectory)
        {
            try
            {
                // 解析工作流
                var workflowInfo = ComfyUIData.Instance.ParseWorkflow(workflow.workflowJson ?? "{}");

                // 生成类名（清理特殊字符）
                var className = CleanClassName(workflow.workflowName);

                // 生成类文件内容
                var classContent = GenerateWorkflowClassContent(workflow, workflowInfo, className);

                // 写入文件
                var filePath = Path.Combine(gateDirectory, $"{className}.cs");
                File.WriteAllText(filePath, classContent, System.Text.Encoding.UTF8);

                return $"✅ 成功生成工作流类: {className}.cs";
            }
            catch (Exception ex)
            {
                return $"❌ 生成工作流类失败: {workflow.workflowName} - {ex.Message}";
            }
        }

        /// <summary>
        /// 生成单个工作流的测试类
        /// </summary>
        /// <param name="workflow">工作流信息</param>
        /// <param name="gateDirectory">输出目录</param>
        /// <returns>生成结果</returns>
        private string GenerateWorkflowTestClass(ComfyUIWorkflow workflow, string gateDirectory)
        {
            try
            {
                // 解析工作流
                var workflowInfo = ComfyUIData.Instance.ParseWorkflow(workflow.workflowJson ?? "{}");

                // 生成类名（清理特殊字符）
                var className = CleanClassName(workflow.workflowName);
                var testClassName = $"{className}Test";

                // 生成测试类文件内容
                var testContent = GenerateWorkflowTestClassContent(workflow, workflowInfo, className, testClassName);

                // 写入文件
                var filePath = Path.Combine(gateDirectory, $"{testClassName}.cs");
                File.WriteAllText(filePath, testContent, System.Text.Encoding.UTF8);

                return $"✅ 成功生成测试类: {testClassName}.cs";
            }
            catch (Exception ex)
            {
                return $"❌ 生成测试类失败: {workflow.workflowName} - {ex.Message}";
            }
        }

        /// <summary>
        /// 清理类名，移除特殊字符
        /// </summary>
        /// <param name="name">原始名称</param>
        /// <returns>清理后的类名</returns>
        private string CleanClassName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return "UnknownWorkflow";

            // 移除特殊字符，只保留字母、数字和下划线
            var cleanName = System.Text.RegularExpressions.Regex.Replace(name, @"[^\w\u4e00-\u9fa5]", "_");

            // 确保以字母开头
            if (char.IsDigit(cleanName[0]))
                cleanName = "Workflow_" + cleanName;

            return cleanName;
        }

        /// <summary>
        /// 生成工作流类的内容
        /// </summary>
        /// <param name="workflow">工作流信息</param>
        /// <param name="workflowInfo">解析后的工作流信息</param>
        /// <param name="className">类名</param>
        /// <returns>类文件内容</returns>
        private string GenerateWorkflowClassContent(ComfyUIWorkflow workflow, WorkflowInfo workflowInfo, string className)
        {
            var sb = new System.Text.StringBuilder();

            // 文件头部
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using SaveDataService;");
            sb.AppendLine("using SaveDataService.Manage;");
            sb.AppendLine();
            sb.AppendLine("namespace ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {workflow.workflowName} - 工作流调用类");
            sb.AppendLine($"    /// 工作流类型: {workflow.workflowType}");
            sb.AppendLine($"    /// 描述: {workflow.description}");
            sb.AppendLine($"    /// 创建者: {workflow.creator}");
            sb.AppendLine($"    /// 版本: {workflow.workflowVersion}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className}");
            sb.AppendLine("    {");

            // 工作流ID常量
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 工作流ID");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine($"        public const string WORKFLOW_ID = \"{workflow.id}\";");
            sb.AppendLine();

            // 工作流信息属性
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 工作流基本信息");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static class WorkflowInfo");
            sb.AppendLine("        {");
            sb.AppendLine($"            public const string Name = \"{workflow.workflowName}\";");
            sb.AppendLine($"            public const string Type = \"{workflow.workflowType}\";");
            sb.AppendLine($"            public const string Description = \"{workflow.description}\";");
            sb.AppendLine($"            public const string Version = \"{workflow.workflowVersion}\";");
            sb.AppendLine($"            public const string Creator = \"{workflow.creator}\";");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 节点信息展示
            if (workflowInfo.Nodes.Count > 0)
            {
                sb.AppendLine("        /// <summary>");
                sb.AppendLine("        /// 工作流节点信息");
                sb.AppendLine("        /// </summary>");
                sb.AppendLine("        public static class Nodes");
                sb.AppendLine("        {");

                foreach (var node in workflowInfo.Nodes)
                {
                    sb.AppendLine($"            /// <summary>");
                    sb.AppendLine($"            /// 节点: {node.NodeId} - {node.NodeType}");
                    if (!string.IsNullOrEmpty(node.NodeTitle))
                        sb.AppendLine($"            /// 标题: {node.NodeTitle}");
                    sb.AppendLine($"            /// </summary>");
                    sb.AppendLine($"            public const string Node_{node.NodeId} = \"{node.NodeId}\";");
                }

                sb.AppendLine("        }");
                sb.AppendLine();
            }

            // 执行方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 执行工作流");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskName\">任务名称</param>");
            sb.AppendLine("        /// <param name=\"creator\">创建者</param>");
            sb.AppendLine("        /// <param name=\"priority\">优先级</param>");
            sb.AppendLine("        /// <returns>任务ID</returns>");
            sb.AppendLine("        public static string Execute(string taskName, string creator = \"system\", int priority = 5)");
            sb.AppendLine("        {");
            sb.AppendLine("            return ComfyUIManage.Instance.CreateTask(WORKFLOW_ID, taskName, creator, priority);");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 获取任务状态方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取任务状态");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskId\">任务ID</param>");
            sb.AppendLine("        /// <returns>任务信息</returns>");
            sb.AppendLine("        public static ComfyUITask GetTaskStatus(string taskId)");
            sb.AppendLine("        {");
            sb.AppendLine("            return ComfyUIManage.Instance.GetTaskById(taskId);");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 获取任务日志方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取任务执行日志");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskId\">任务ID</param>");
            sb.AppendLine("        /// <returns>日志JSON字符串</returns>");
            sb.AppendLine("        public static string GetTaskLogs(string taskId)");
            sb.AppendLine("        {");
            sb.AppendLine("            return ComfyUIManage.Instance.GetTaskLogs(taskId);");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 获取任务文件方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取任务相关文件");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskId\">任务ID</param>");
            sb.AppendLine("        /// <param name=\"fileType\">文件类型过滤 (-1:全部, 0:输入图片, 1:输出图片, 2:输出视频, 3:其他)</param>");
            sb.AppendLine("        /// <returns>文件JSON字符串</returns>");
            sb.AppendLine("        public static string GetTaskFiles(string taskId, int fileType = -1)");
            sb.AppendLine("        {");
            sb.AppendLine("            return ComfyUIManage.Instance.GetTaskFiles(taskId, fileType);");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 类结束
            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 生成工作流测试类的内容
        /// </summary>
        /// <param name="workflow">工作流信息</param>
        /// <param name="workflowInfo">解析后的工作流信息</param>
        /// <param name="className">工作流类名</param>
        /// <param name="testClassName">测试类名</param>
        /// <returns>测试类文件内容</returns>
        private string GenerateWorkflowTestClassContent(ComfyUIWorkflow workflow, WorkflowInfo workflowInfo, string className, string testClassName)
        {
            var sb = new System.Text.StringBuilder();

            // 文件头部
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using SaveDataService;");
            sb.AppendLine("using SaveDataService.Manage;");
            sb.AppendLine();
            sb.AppendLine("namespace ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {workflow.workflowName} - 工作流测试类");
            sb.AppendLine($"    /// 测试工作流: {className}");
            sb.AppendLine($"    /// 工作流类型: {workflow.workflowType}");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {testClassName}");
            sb.AppendLine("    {");

            // 基础测试方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 测试工作流执行");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static void TestExecute()");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine(\"开始测试工作流: {workflow.workflowName}\");");
            sb.AppendLine();
            sb.AppendLine("                // 执行工作流");
            sb.AppendLine($"                var taskId = {className}.Execute(\"测试任务_{DateTime.Now:yyyyMMdd_HHmmss}\", \"测试用户\", 5);");
            sb.AppendLine("                Console.WriteLine($\"任务已创建，ID: {taskId}\");");
            sb.AppendLine();
            sb.AppendLine("                if (!string.IsNullOrEmpty(taskId))");
            sb.AppendLine("                {");
            sb.AppendLine("                    // 获取任务状态");
            sb.AppendLine($"                    var task = {className}.GetTaskStatus(taskId);");
            sb.AppendLine("                    if (task != null)");
            sb.AppendLine("                    {");
            sb.AppendLine("                        Console.WriteLine($\"任务状态: {task.status}, 进度: {task.progress}%\");");
            sb.AppendLine("                        Console.WriteLine($\"当前节点: {task.currentNode} - {task.currentNodeName}\");");
            sb.AppendLine("                    }");
            sb.AppendLine("                    else");
            sb.AppendLine("                    {");
            sb.AppendLine("                        Console.WriteLine(\"获取任务状态失败\");");
            sb.AppendLine("                    }");
            sb.AppendLine("                }");
            sb.AppendLine("                else");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine(\"任务创建失败\");");
            sb.AppendLine("                }");
            sb.AppendLine();
            sb.AppendLine($"                Console.WriteLine(\"工作流测试完成: {workflow.workflowName}\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"测试执行失败: {ex.Message}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 详细测试方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 测试工作流详细功能");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static void TestDetailed()");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine(\"开始详细测试工作流: {workflow.workflowName}\");");
            sb.AppendLine();
            sb.AppendLine("                // 执行工作流");
            sb.AppendLine($"                var taskId = {className}.Execute(\"详细测试任务_{DateTime.Now:yyyyMMdd_HHmmss}\", \"测试用户\", 3);");
            sb.AppendLine("                Console.WriteLine($\"任务已创建，ID: {taskId}\");");
            sb.AppendLine();
            sb.AppendLine("                if (!string.IsNullOrEmpty(taskId))");
            sb.AppendLine("                {");
            sb.AppendLine("                    // 获取任务状态");
            sb.AppendLine($"                    var task = {className}.GetTaskStatus(taskId);");
            sb.AppendLine("                    if (task != null)");
            sb.AppendLine("                    {");
            sb.AppendLine("                        Console.WriteLine($\"任务详情:\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 任务ID: {task.id}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 工作流ID: {task.workflowId}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 任务名称: {task.taskName}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 状态: {task.status}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 进度: {task.progress}%\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 当前节点: {task.currentNode}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 节点名称: {task.currentNodeName}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 创建时间: {task.CreateTime}\");");
            sb.AppendLine("                        Console.WriteLine($\"  - 更新时间: {task.UpdateTime}\");");
            sb.AppendLine();
            sb.AppendLine("                        // 获取任务日志");
            sb.AppendLine($"                        var logs = {className}.GetTaskLogs(taskId);");
            sb.AppendLine("                        Console.WriteLine($\"任务日志: {logs}\");");
            sb.AppendLine();
            sb.AppendLine("                        // 获取任务文件");
            sb.AppendLine($"                        var files = {className}.GetTaskFiles(taskId);");
            sb.AppendLine("                        Console.WriteLine($\"任务文件: {files}\");");
            sb.AppendLine("                    }");
            sb.AppendLine("                }");
            sb.AppendLine();
            sb.AppendLine($"                Console.WriteLine(\"详细测试完成: {workflow.workflowName}\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"详细测试失败: {ex.Message}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 工作流信息展示方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 显示工作流信息");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        public static void ShowWorkflowInfo()");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine($"                Console.WriteLine(\"=== {workflow.workflowName} 工作流信息 ===\");");
            sb.AppendLine($"                Console.WriteLine($\"工作流ID: {{WORKFLOW_ID}}\");");
            sb.AppendLine($"                Console.WriteLine($\"工作流名称: {{{className}.WorkflowInfo.Name}}\");");
            sb.AppendLine($"                Console.WriteLine($\"工作流类型: {{{className}.WorkflowInfo.Type}}\");");
            sb.AppendLine($"                Console.WriteLine($\"描述: {{{className}.WorkflowInfo.Description}}\");");
            sb.AppendLine($"                Console.WriteLine($\"版本: {{{className}.WorkflowInfo.Version}}\");");
            sb.AppendLine($"                Console.WriteLine($\"创建者: {{{className}.WorkflowInfo.Creator}}\");");

            // 如果有节点信息，显示节点列表
            if (workflowInfo.Nodes.Count > 0)
            {
                sb.AppendLine("                Console.WriteLine(\"\\n节点列表:\");");
                foreach (var node in workflowInfo.Nodes)
                {
                    sb.AppendLine($"                Console.WriteLine($\"  - 节点{node.NodeId}: {node.NodeType} ({node.NodeTitle})\");");
                }
            }

            sb.AppendLine("                Console.WriteLine(\"===========================================\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"显示工作流信息失败: {ex.Message}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 类结束
            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }

        /// <summary>
        /// 根据类型获取工作流
        /// </summary>
        /// <param name="workflowType">工作流类型</param>
        /// <returns>工作流列表</returns>
        public List<ComfyUIWorkflow> GetWorkflowsByType(string workflowType)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.Where(w => w.isEnabled && w.workflowType == workflowType).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流列表失败: {ex.Message}");
                return new List<ComfyUIWorkflow>();
            }
        }

        /// <summary>
        /// 创建新任务
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="creator">创建者</param>
        /// <param name="priority">优先级</param>
        /// <returns>任务ID</returns>
        public string CreateTask(string workflowId, string taskName, string creator = "", int priority = 5)
        {
            try
            {
                // 首先检查是否有服务器
                var allServers = GetAllServers();
                Console.WriteLine($"数据库中共有 {allServers.Count} 个服务器");

                // 选择最佳服务器
                var serverId = SelectBestServer();
                if (string.IsNullOrEmpty(serverId))
                {
                    // 如果没有在线服务器，使用第一个服务器
                    if (allServers.Count > 0)
                    {
                        serverId = allServers[0].id;
                        Console.WriteLine($"没有在线服务器，使用第一个服务器: {serverId}");
                    }
                    else
                    {
                        Console.WriteLine("没有可用的服务器");
                        return "";
                    }
                }

                var taskId = Guid.NewGuid().ToString();
                var task = new ComfyUITask
                {
                    id = taskId,
                    serverId = serverId,
                    workflowId = workflowId,
                    taskName = taskName,
                    status = 0, // 等待
                    progress = 0,
                    queuePosition = GetNextQueuePosition(),
                    priority = priority,
                    creator = creator,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };
                try
                {

                    var db = ORMTables.Instance;
                    db.ComfyUITasks.Add(task);
                    db.SaveChanges();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"存储任务失败: {taskName} (ID: {taskId})");
                    Console.WriteLine($"存储任务失败: {ex.ToString()} ");

                }

                Console.WriteLine($"成功创建任务: {taskName} (ID: {taskId})");
                return taskId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建任务失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return "";
            }
        }

        /// <summary>
        /// 选择最佳服务器
        /// </summary>
        /// <returns>服务器ID</returns>
        private string SelectBestServer()
        {
            try
            {
                var db = ORMTables.Instance;
                var availableServers = db.ComfyUIServers
                    .Where(s => s.status == 1 && s.currentTasks < s.maxConcurrentTasks)
                    .OrderBy(s => s.currentTasks)
                    .ToList();

                return availableServers.FirstOrDefault()?.id ?? "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"选择服务器失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取下一个队列位置
        /// </summary>
        /// <returns>队列位置</returns>
        private int GetNextQueuePosition()
        {
            try
            {
                var db = ORMTables.Instance;
                var maxPosition = db.ComfyUITasks
                    .Where(t => t.status == 0) // 等待状态
                    .Max(t => (int?)t.queuePosition) ?? 0;
                return maxPosition + 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取队列位置失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="status">状态过滤 (-1:全部, 0:等待, 1:运行中, 2:完成, 3:失败, 4:取消)</param>
        /// <param name="serverId">服务器ID过滤</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasks(int status = -1, string serverId = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var query = db.ComfyUITasks.AsQueryable();

                if (status >= 0)
                {
                    query = query.Where(t => t.status == status);
                }

                if (!string.IsNullOrEmpty(serverId))
                {
                    query = query.Where(t => t.serverId == serverId);
                }

                return query.OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务列表失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }

        /// <summary>
        /// 根据ID获取服务器
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>服务器信息</returns>
        public ComfyUIServer GetServerById(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取服务器失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>工作流信息</returns>
        public ComfyUIWorkflow GetWorkflowById(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取工作流失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据ID获取任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务信息</returns>
        public ComfyUITask GetTaskById(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据状态获取任务列表
        /// </summary>
        /// <param name="status">任务状态</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasksByStatus(int status)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.Where(t => t.status == status).OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据状态获取任务失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }



        /// <summary>
        /// 根据工作流ID获取任务列表
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>任务列表</returns>
        public List<ComfyUITask> GetTasksByWorkflowId(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                return db.ComfyUITasks.Where(t => t.workflowId == workflowId).OrderBy(t => t.CreateTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"根据工作流ID获取任务失败: {ex.Message}");
                return new List<ComfyUITask>();
            }
        }

        /// <summary>
        /// 获取队列中的任务
        /// </summary>
        /// <returns>队列任务列表</returns>
        public List<ComfyUITask> GetQueueTasks()
        {
            return GetTasks(0).OrderBy(t => t.queuePosition).ToList();
        }

        /// <summary>
        /// 获取正在运行的任务
        /// </summary>
        /// <returns>运行中任务列表</returns>
        public List<ComfyUITask> GetRunningTasks()
        {
            return GetTasks(1);
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="status">新状态</param>
        /// <param name="progress">进度</param>
        /// <param name="currentNode">当前节点</param>
        /// <param name="currentNodeName">当前节点名称</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>是否成功</returns>
        public bool UpdateTaskStatus(string taskId, int status, int progress = -1, string currentNode = "", string currentNodeName = "", string errorMessage = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task != null)
                {
                    task.status = status;
                    if (progress >= 0)
                    {
                        task.progress = progress;
                    }
                    if (!string.IsNullOrEmpty(currentNode))
                    {
                        task.currentNode = currentNode;
                    }
                    if (!string.IsNullOrEmpty(currentNodeName))
                    {
                        task.currentNodeName = currentNodeName;
                    }
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        task.errorMessage = errorMessage;
                    }

                    // 设置开始和结束时间
                    if (status == 1 && task.startTime == null) // 开始运行
                    {
                        task.startTime = TimeTools.GetTimeStamp();
                    }
                    else if ((status == 2 || status == 3 || status == 4) && task.endTime == null) // 完成/失败/取消
                    {
                        task.endTime = TimeTools.GetTimeStamp();
                    }

                    task.UpdateTime = DateTime.Now;
                    db.SaveChanges();

                    // 记录日志
                    AddTaskLog(taskId, 1, $"任务状态更新: {GetStatusName(status)}", currentNode, currentNodeName);

                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新任务状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 添加任务日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="logLevel">日志级别</param>
        /// <param name="message">消息</param>
        /// <param name="nodeId">节点ID</param>
        /// <param name="nodeName">节点名称</param>
        /// <param name="details">详细信息</param>
        /// <returns>是否成功</returns>
        public bool AddTaskLog(string taskId, int logLevel, string message, string nodeId = "", string nodeName = "", string details = "")
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null)
                {
                    Console.WriteLine($"任务不存在: {taskId}");
                    return false;
                }

                var logEntry = new
                {
                    id = Guid.NewGuid().ToString(),
                    logLevel = logLevel,
                    message = message,
                    nodeId = nodeId,
                    nodeName = nodeName,
                    details = details,
                    createTime = TimeTools.GetTimeStamp()
                };

                var logs = new List<object>();
                if (!string.IsNullOrEmpty(task.Logs))
                {
                    try
                    {
                        logs = System.Text.Json.JsonSerializer.Deserialize<List<object>>(task.Logs) ?? new List<object>();
                    }
                    catch
                    {
                        logs = new List<object>();
                    }
                }

                logs.Add(logEntry);
                task.Logs = System.Text.Json.JsonSerializer.Serialize(logs);
                task.UpdateTime = DateTime.Now;

                db.SaveChanges();

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加任务日志失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取任务日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>日志JSON字符串</returns>
        public string GetTaskLogs(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                return task?.Logs ?? "[]";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务日志失败: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 添加任务文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileType">文件类型</param>
        /// <param name="fileName">文件名</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="fileSize">文件大小</param>
        /// <param name="fileMd5">文件MD5</param>
        /// <param name="description">描述</param>
        /// <returns>文件ID</returns>
        public string AddTaskFile(string taskId, int fileType, string fileName, string filePath, long fileSize = 0, string fileMd5 = "", string description = "")
        {
            try
            {
                var fileId = Guid.NewGuid().ToString();

                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null)
                {
                    Console.WriteLine($"任务不存在: {taskId}");
                    return "";
                }

                var fileEntry = new
                {
                    id = fileId,
                    fileType = fileType,
                    fileName = fileName,
                    filePath = filePath,
                    fileSize = fileSize,
                    fileMd5 = fileMd5,
                    description = description,
                    createTime = TimeTools.GetTimeStamp()
                };

                var files = new List<object>();
                if (!string.IsNullOrEmpty(task.Files))
                {
                    try
                    {
                        files = System.Text.Json.JsonSerializer.Deserialize<List<object>>(task.Files) ?? new List<object>();
                    }
                    catch
                    {
                        files = new List<object>();
                    }
                }

                files.Add(fileEntry);
                task.Files = System.Text.Json.JsonSerializer.Serialize(files);
                task.UpdateTime = DateTime.Now;

                db.SaveChanges();

                Console.WriteLine($"成功添加任务文件: {fileName}");
                return fileId;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加任务文件失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取任务文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileType">文件类型过滤 (-1:全部)</param>
        /// <returns>文件JSON字符串</returns>
        public string GetTaskFiles(string taskId, int fileType = -1)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task == null || string.IsNullOrEmpty(task.Files))
                {
                    return "[]";
                }

                if (fileType >= 0)
                {
                    try
                    {
                        var allFiles = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(task.Files);
                        var filteredFiles = allFiles?.Where(f =>
                            f.TryGetProperty("fileType", out var ft) && ft.GetInt32() == fileType).ToList();
                        return System.Text.Json.JsonSerializer.Serialize(filteredFiles ?? new List<System.Text.Json.JsonElement>());
                    }
                    catch
                    {
                        return "[]";
                    }
                }

                return task.Files;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取任务文件失败: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="status">状态码</param>
        /// <returns>状态名称</returns>
        private string GetStatusName(int status)
        {
            return status switch
            {
                0 => "等待",
                1 => "运行中",
                2 => "完成",
                3 => "失败",
                4 => "取消",
                _ => "未知"
            };
        }

        /// <summary>
        /// 取消任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否成功</returns>
        public bool CancelTask(string taskId)
        {
            return UpdateTaskStatus(taskId, 4, errorMessage: "用户取消任务");
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteTask(string taskId)
        {
            try
            {
                var db = ORMTables.Instance;
                var task = db.ComfyUITasks.FirstOrDefault(t => t.id == taskId);
                if (task != null)
                {
                    db.ComfyUITasks.Remove(task);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除任务: {taskId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除任务失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除工作流
        /// </summary>
        /// <param name="workflowId">工作流ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteWorkflow(string workflowId)
        {
            try
            {
                var db = ORMTables.Instance;
                var workflow = db.ComfyUIWorkflows.FirstOrDefault(w => w.id == workflowId);
                if (workflow != null)
                {
                    db.ComfyUIWorkflows.Remove(workflow);
                    db.SaveChanges();
                    Console.WriteLine($"成功删除工作流: {workflowId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除工作流失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取服务器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public object GetServerStatistics()
        {
            try
            {
                var db = ORMTables.Instance;
                var servers = db.ComfyUIServers.ToList();
                var tasks = db.ComfyUITasks.ToList();

                return new
                {
                    TotalServers = servers.Count,
                    OnlineServers = servers.Count(s => s.status == 1),
                    BusyServers = servers.Count(s => s.status == 2),
                    OfflineServers = servers.Count(s => s.status == 0),
                    TotalTasks = tasks.Count,
                    WaitingTasks = tasks.Count(t => t.status == 0),
                    RunningTasks = tasks.Count(t => t.status == 1),
                    CompletedTasks = tasks.Count(t => t.status == 2),
                    FailedTasks = tasks.Count(t => t.status == 3),
                    CancelledTasks = tasks.Count(t => t.status == 4)
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取统计信息失败: {ex.Message}");
                return new { Error = ex.Message };
            }
        }

        /// <summary>
        /// 测试服务器连接
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <returns>是否连接成功</returns>
        public async Task<bool> TestServerConnection(string serverId)
        {
            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server == null)
                {
                    Console.WriteLine($"服务器不存在: {serverId}");
                    return false;
                }

                var url = $"http://{server.serverUrl}:{server.port}/";
                Console.WriteLine($"测试连接到: {url}");

                try
                {
                    var response = await _httpClient.GetAsync(url);
                    var isSuccess = response.IsSuccessStatusCode;

                    // 更新服务器状态
                    UpdateServerStatus(serverId, isSuccess ? 1 : 0);

                    Console.WriteLine($"服务器连接测试 {(isSuccess ? "成功" : "失败")}: {server.serverName}");
                    return isSuccess;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"连接服务器失败: {ex.Message}");
                    UpdateServerStatus(serverId, 0); // 离线
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试服务器连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试所有服务器连接
        /// </summary>
        /// <returns>在线服务器数量</returns>
        public async Task<int> TestAllServerConnections()
        {
            try
            {
                var servers = GetAllServers();
                int onlineCount = 0;

                Console.WriteLine($"开始测试 {servers.Count} 台服务器的连接...");

                foreach (var server in servers)
                {
                    var isOnline = await TestServerConnection(server.id);
                    if (isOnline)
                    {
                        onlineCount++;
                    }
                }

                Console.WriteLine($"连接测试完成，{onlineCount}/{servers.Count} 台服务器在线");
                return onlineCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试所有服务器连接失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 向ComfyUI服务器发送工作流（带详细日志监控）
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="inputParameters">输入参数</param>
        /// <returns>任务ID和提交结果</returns>
        public async Task<(string taskId, string result)> SubmitWorkflowToServerWithMonitoring(string serverId, string workflowJson,
            string workflowId = "", string workflowName = "", object inputParameters = null)
        {
            var logger = ComfyUILogger.Instance;
            var monitor = ComfyUIMonitor.Instance;
            var taskId = Guid.NewGuid().ToString();

            try
            {
                var db = ORMTables.Instance;
                var server = db.ComfyUIServers.FirstOrDefault(s => s.id == serverId);
                if (server == null)
                {
                    logger.LogError($"服务器不存在: {serverId}");
                    return (taskId, "服务器不存在");
                }

                var serverUrl = $"http://{server.serverUrl}:{server.port}";
                var url = $"{serverUrl}/prompt";

                // 记录工作流开始
                logger.LogWorkflowStart(taskId, workflowJson, serverId);

                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                logger.LogInfo($"向服务器提交工作流: {server.serverName} ({serverUrl})");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                // 记录提交结果
                logger.LogWorkflowSubmission(taskId, response.IsSuccessStatusCode, responseText);

                if (response.IsSuccessStatusCode)
                {
                    // 解析响应获取prompt_id并启动监控
                    try
                    {
                        var responseObj = JsonConvert.DeserializeObject<dynamic>(responseText);
                        if (responseObj.prompt_id != null)
                        {
                            var promptId = responseObj.prompt_id.ToString();
                            logger.LogInfo($"获得Prompt ID: {promptId}，启动详细监控...");

                            // 启动监控（不等待完成）
                            _ = Task.Run(() => monitor.StartMonitoring(taskId, promptId, serverUrl, workflowJson,
                                serverId, workflowId, workflowName, inputParameters));
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning($"启动监控失败: {ex.Message}");
                    }

                    return (taskId, responseText);
                }
                else
                {
                    logger.LogError($"工作流提交失败: {response.StatusCode} - {responseText}");
                    return (taskId, $"提交失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"提交工作流到服务器失败: {ex.Message}");
                return (taskId, $"提交失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 向ComfyUI服务器发送工作流（原有方法，保持兼容性）
        /// </summary>
        /// <param name="serverId">服务器ID</param>
        /// <param name="workflowJson">工作流JSON</param>
        /// <returns>是否成功</returns>
        public async Task<string> SubmitWorkflowToServer(string serverId, string workflowJson)
        {
            var result = await SubmitWorkflowToServerWithMonitoring(serverId, workflowJson);
            return result.result;
        }

        /// <summary>
        /// 自动扫描并导入工作流文件
        /// 扫描项目下的 aitestserver\SaveDataService\Res\comfyuiWorkFlows 目录的所有 JSON 文件
        /// 清理数据库中 comfyuiworkflows 所有内容，然后导入新的工作流
        /// </summary>
        /// <returns>导入结果信息</returns>
        public string AddWorkflows()
        {
            try
            {







                // 获取所有 JSON 文件


                string workflowDirectory = PatchUtil.ResRootPatch + AppConfig.Instance.workflowsPath;
                string[] jsonFiles = Directory.GetFiles(workflowDirectory, "*.json", SearchOption.AllDirectories);




               // var jsonFiles = Directory.GetFiles(workflowDirectory, "*.json", SearchOption.AllDirectories);
                Console.WriteLine($"找到 {jsonFiles.Length} 个 JSON 文件");

                if (jsonFiles.Length == 0)
                {
                    var msg = "未找到任何 JSON 工作流文件";
                    Console.WriteLine(msg);
                    return msg;
                }

                // 清理数据库中的所有工作流
                var db = ORMTables.Instance;
                var existingWorkflows = db.ComfyUIWorkflows.ToList();
                if (existingWorkflows.Count > 0)
                {
                    db.ComfyUIWorkflows.RemoveRange(existingWorkflows);
                    db.SaveChanges();
                    Console.WriteLine($"已清理数据库中的 {existingWorkflows.Count} 个现有工作流");
                }

                int successCount = 0;
                int failCount = 0;
                var results = new List<string>();

                foreach (var filePath in jsonFiles)
                {
                    try
                    {
                        // 读取文件内容
                        var jsonContent = File.ReadAllText(filePath);

                        // 验证 JSON 格式
                        JsonConvert.DeserializeObject(jsonContent);

                        // 计算文件的 SHA1 值作为 ID
                        var fileId = CalculateFileSHA1(filePath);

                        // 获取相对于 comfyuiWorkFlows 的路径作为名称和描述
                        var relativePath = GetRelativePathFromComfyUIWorkFlows(filePath, workflowDirectory);
                        var workflowName = relativePath;
                        var description = $"从文件导入: {relativePath}";

                        // 从文件路径推断工作流类型
                        var workflowType = InferWorkflowType(relativePath);

                        // 创建工作流记录
                        var workflow = new ComfyUIWorkflow
                        {
                            id = fileId,
                            workflowName = workflowName,
                            workflowJson = jsonContent,
                            workflowType = workflowType,
                            description = description,
                            workflowVersion = "1.0",
                            creator = "system",
                            isEnabled = true,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now
                        };

                        db.ComfyUIWorkflows.Add(workflow);
                        successCount++;
                        results.Add($"✅ 成功导入: {relativePath} (ID: {fileId})");
                        Console.WriteLine($"成功导入工作流: {workflowName}");
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        var errorMsg = $"❌ 导入失败: {Path.GetFileName(filePath)} - {ex.Message}";
                        results.Add(errorMsg);
                        Console.WriteLine(errorMsg);
                    }
                }

                // 保存到数据库
                if (successCount > 0)
                {
                    db.SaveChanges();
                    Console.WriteLine($"已保存 {successCount} 个工作流到数据库");
                }

                var summary = $"工作流导入完成: 成功 {successCount} 个，失败 {failCount} 个";
                results.Insert(0, summary);
                Console.WriteLine(summary);

                return string.Join("\n", results);
            }
            catch (Exception ex)
            {
                var errorMsg = $"导入工作流时发生错误: {ex.Message}";
                Console.WriteLine(errorMsg);
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return errorMsg;
            }
        }

        /// <summary>
        /// 计算文件的 SHA1 值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>SHA1 哈希值</returns>
        private string CalculateFileSHA1(string filePath)
        {
            using (var sha1 = SHA1.Create())
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var hash = sha1.ComputeHash(stream);
                    return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
                }
            }
        }

        /// <summary>
        /// 获取相对于 comfyuiWorkFlows 目录的路径
        /// </summary>
        /// <param name="filePath">完整文件路径</param>
        /// <param name="workflowDirectory">工作流根目录</param>
        /// <returns>相对路径</returns>
        private string GetRelativePathFromComfyUIWorkFlows(string filePath, string workflowDirectory)
        {
            var relativePath = Path.GetRelativePath(workflowDirectory, filePath);
            return relativePath.Replace("\\", "/"); // 统一使用正斜杠
        }

        /// <summary>
        /// 从文件路径推断工作流类型
        /// </summary>
        /// <param name="relativePath">相对路径</param>
        /// <returns>工作流类型</returns>
        private string InferWorkflowType(string relativePath)
        {
            var lowerPath = relativePath.ToLower();

            if (lowerPath.Contains("text2img") || lowerPath.Contains("txt2img"))
                return "text2img";
            else if (lowerPath.Contains("img2img"))
                return "img2img";
            else if (lowerPath.Contains("inpaint"))
                return "inpaint";
            else if (lowerPath.Contains("upscale"))
                return "upscale";
            else if (lowerPath.Contains("controlnet"))
                return "controlnet";
            else if (lowerPath.Contains("video"))
                return "video";
            else if (lowerPath.Contains("animation"))
                return "animation";
            else
                return "general";
        }
    }
}
