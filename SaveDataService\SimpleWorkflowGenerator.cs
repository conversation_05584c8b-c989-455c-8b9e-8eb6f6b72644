using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService.Manage;
using GameServer.Util;
using GameServer;

namespace SaveDataService
{
    /// <summary>
    /// 简单工作流生成器
    /// 扫描ComfyUI工作流JSON文件，分析input-前缀节点，生成对应的C#调用类
    /// </summary>
    public class SimpleWorkflowGenerator
    {
        /// <summary>
        /// 生成所有工作流的C#类
        /// </summary>
        /// <returns></returns>
        public static async Task<string> GenerateAllWorkflowsAsync()
        {
            try
            {
                Console.WriteLine("🚀🚀🚀 SimpleWorkflowGenerator.GenerateAllWorkflowsAsync() 开始执行！🚀🚀🚀");
                Console.WriteLine("🚀 开始生成所有工作流的C#类...");

                // 获取工作流目录
                string workflowsPath = AppConfig.Instance.workflowsPath.TrimStart('/');
                string workflowDirectory = Path.Combine(PatchUtil.ResRootPatch, workflowsPath);
                Console.WriteLine($"扫描目录: {workflowDirectory}");

                if (!Directory.Exists(workflowDirectory))
                {
                    var errorMsg = $"工作流目录不存在: {workflowDirectory}";
                    Console.WriteLine($"❌ {errorMsg}");
                    return errorMsg;
                }

                // 递归获取所有JSON文件
                string[] jsonFiles = Directory.GetFiles(workflowDirectory, "*.json", SearchOption.AllDirectories);
                Console.WriteLine($"找到 {jsonFiles.Length} 个JSON文件");

                if (jsonFiles.Length == 0)
                {
                    var errorMsg = "没有找到任何JSON工作流文件";
                    Console.WriteLine($"❌ {errorMsg}");
                    return errorMsg;
                }

                // 确保输出目录存在
                string outputDirectory = Path.Combine(PatchUtil.ResRootPatch, "ComfyuiGate");
                if (!Directory.Exists(outputDirectory))
                {
                    Directory.CreateDirectory(outputDirectory);
                    Console.WriteLine($"创建输出目录: {outputDirectory}");
                }

                var results = new List<string>();
                int successCount = 0;
                int failCount = 0;

                // 处理每个JSON文件
                foreach (var jsonFile in jsonFiles)
                {
                    try
                    {
                        var result = await GenerateWorkflowAsync(jsonFile);
                        results.Add(result);
                        successCount++;
                        Console.WriteLine($"✅ 成功处理: {Path.GetFileName(jsonFile)}");
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        var errorMsg = $"❌ 处理失败: {Path.GetFileName(jsonFile)} - {ex.Message}";
                        results.Add(errorMsg);
                        Console.WriteLine(errorMsg);
                    }
                }

                var summary = $"工作流类生成完成！成功: {successCount}, 失败: {failCount}";
                results.Insert(0, summary);
                Console.WriteLine($"\n🎉 {summary}");

                return string.Join("\n", results);
            }
            catch (Exception ex)
            {
                var errorMsg = $"生成工作流类时发生错误: {ex.Message}";
                Console.WriteLine($"❌ {errorMsg}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
                return errorMsg;
            }
        }
        
        /// <summary>
        /// 生成单个工作流的C#类
        /// </summary>
        /// <param name="jsonFilePath">JSON文件路径</param>
        /// <returns>生成结果</returns>
        public static async Task<string> GenerateWorkflowAsync(string jsonFilePath)
        {
            try
            {
                if (!File.Exists(jsonFilePath))
                {
                    throw new FileNotFoundException($"JSON文件不存在: {jsonFilePath}");
                }
                
                // 读取JSON内容
                string jsonContent = await File.ReadAllTextAsync(jsonFilePath);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    throw new InvalidOperationException("JSON文件内容为空");
                }
                
                // 解析JSON
                var workflowData = JsonConvert.DeserializeObject<JObject>(jsonContent);
                if (workflowData == null)
                {
                    throw new InvalidOperationException("无法解析JSON内容");
                }
                
                // 生成类名（基于文件名）
                string fileName = Path.GetFileNameWithoutExtension(jsonFilePath);
                string className = GenerateClassName(fileName);
                
                // 分析工作流，提取input参数
                var inputParameters = AnalyzeWorkflowInputs(workflowData);
                
                // 生成C#类内容
                string classContent = GenerateClassContent(className, fileName, jsonContent, inputParameters);
                
                // 写入文件
                string outputDirectory = Path.Combine(PatchUtil.ResRootPatch, "ComfyuiGate");
                string outputFilePath = Path.Combine(outputDirectory, $"{className}.cs");
                await File.WriteAllTextAsync(outputFilePath, classContent, System.Text.Encoding.UTF8);
                
                return $"✅ 成功生成: {className}.cs (参数: {inputParameters.Count})";
            }
            catch (Exception ex)
            {
                throw new Exception($"生成工作流类失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 工作流输入参数信息
        /// </summary>
        public class WorkflowInputParameter
        {
            /// <summary>
            /// 参数名称（从title中提取）
            /// </summary>
            public string ParameterName { get; set; } = "";

            /// <summary>
            /// 参数类型（C#类型）
            /// </summary>
            public string ParameterType { get; set; } = "";

            /// <summary>
            /// 默认值
            /// </summary>
            public object DefaultValue { get; set; } = null;

            /// <summary>
            /// 原始输入名称（JSON中的key）
            /// </summary>
            public string OriginalInputName { get; set; } = "";

            /// <summary>
            /// 节点ID
            /// </summary>
            public string NodeId { get; set; } = "";

            /// <summary>
            /// 节点标题
            /// </summary>
            public string NodeTitle { get; set; } = "";
        }

        /// <summary>
        /// 分析工作流输入参数
        /// 查找所有title包含"input-"前缀的节点，提取参数信息
        /// 工作流文件已经是ComfyUI API格式（旧格式），直接以节点ID为键
        /// </summary>
        /// <param name="workflowData">工作流JSON数据</param>
        /// <returns>输入参数列表</returns>
        private static List<WorkflowInputParameter> AnalyzeWorkflowInputs(JObject workflowData)
        {
            var inputParameters = new List<WorkflowInputParameter>();

            try
            {
                Console.WriteLine($"开始分析工作流（API格式），找到 {workflowData.Count} 个节点");

                // 遍历所有节点
                foreach (var nodeEntry in workflowData)
                {
                    string nodeId = nodeEntry.Key;
                    var nodeData = nodeEntry.Value as JObject;

                    if (nodeData == null) continue;

                    // 检查是否有_meta.title属性，并且以input-开头
                    var metaTitle = nodeData["_meta"]?["title"]?.ToString();
                    if (string.IsNullOrEmpty(metaTitle) || !metaTitle.StartsWith("input-"))
                    {
                        continue;
                    }

                    Console.WriteLine($"找到input节点: {nodeId} - {metaTitle}");

                    // 解析title，提取参数名称
                    string parameterName = ExtractParameterName(metaTitle);
                    if (string.IsNullOrEmpty(parameterName))
                    {
                        Console.WriteLine($"无法从title提取参数名: {metaTitle}");
                        continue;
                    }

                    // 获取节点的inputs部分
                    var inputs = nodeData["inputs"] as JObject;
                    if (inputs == null)
                    {
                        Console.WriteLine($"节点 {nodeId} 没有inputs部分");
                        continue;
                    }

                    // 分析inputs中的每个属性
                    foreach (var inputEntry in inputs)
                    {
                        string inputName = inputEntry.Key;
                        var inputValue = inputEntry.Value;

                        // 跳过数组类型的输入（这些通常是连接到其他节点的）
                        if (inputValue is JArray)
                        {
                            continue;
                        }

                        // 确定参数类型和默认值
                        var (paramType, defaultValue) = DetermineParameterType(inputValue);

                        var parameter = new WorkflowInputParameter
                        {
                            ParameterName = $"{parameterName}_{inputName}",
                            ParameterType = paramType,
                            DefaultValue = defaultValue,
                            OriginalInputName = inputName,
                            NodeId = nodeId,
                            NodeTitle = metaTitle
                        };

                        inputParameters.Add(parameter);
                        Console.WriteLine($"  添加参数: {parameter.ParameterName} ({parameter.ParameterType}) = {parameter.DefaultValue}");
                    }
                }

                Console.WriteLine($"总共找到 {inputParameters.Count} 个输入参数");
                return inputParameters;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析工作流输入参数时发生错误: {ex.Message}");
                return inputParameters;
            }
        }



        /// <summary>
        /// 从title中提取参数名称
        /// 例如: "input-promt-提示词" -> "promt"
        /// </summary>
        /// <param name="title">节点标题</param>
        /// <returns>参数名称</returns>
        private static string ExtractParameterName(string title)
        {
            try
            {
                if (string.IsNullOrEmpty(title) || !title.StartsWith("input-"))
                {
                    return "";
                }

                // 移除"input-"前缀
                string remaining = title.Substring(6);

                // 按"-"分割，取第一部分作为参数名
                string[] parts = remaining.Split('-');
                if (parts.Length > 0 && !string.IsNullOrEmpty(parts[0]))
                {
                    return parts[0];
                }

                return "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提取参数名称时发生错误: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 确定参数类型和默认值
        /// </summary>
        /// <param name="value">JSON值</param>
        /// <returns>C#类型和默认值</returns>
        private static (string type, object defaultValue) DetermineParameterType(JToken value)
        {
            try
            {
                if (value == null)
                {
                    return ("string", "\"\"");
                }

                switch (value.Type)
                {
                    case JTokenType.String:
                        var strValue = value.ToString();
                        var escapedValue = EscapeStringForCSharp(strValue);
                        return ("string", escapedValue);

                    case JTokenType.Integer:
                        var intValue = value.Value<long>();
                        return ("int", intValue.ToString());

                    case JTokenType.Float:
                        var floatValue = value.Value<double>();
                        return ("double", floatValue.ToString("F1"));

                    case JTokenType.Boolean:
                        var boolValue = value.Value<bool>();
                        return ("bool", boolValue.ToString().ToLower());

                    default:
                        var defaultStrValue = value.ToString();
                        var escapedDefaultValue = EscapeStringForCSharp(defaultStrValue);
                        return ("string", escapedDefaultValue);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"确定参数类型时发生错误: {ex.Message}");
                return ("string", "\"\"");
            }
        }

        /// <summary>
        /// 转义字符串以便在C#代码中安全使用
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>转义后的C#字符串字面量</returns>
        private static string EscapeStringForCSharp(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return "\"\"";
            }

            try
            {
                // 检查是否包含特殊字符，如果包含则使用逐字字符串字面量(@"...")
                bool hasSpecialChars = input.Contains('\n') || input.Contains('\r') ||
                                      input.Contains('\t') || input.Contains('\\') ||
                                      input.Contains('"') || input.Contains('\'');

                if (hasSpecialChars)
                {
                    // 使用逐字字符串字面量，只需要转义双引号
                    var escaped = input.Replace("\"", "\"\"");
                    return $"@\"{escaped}\"";
                }
                else
                {
                    // 普通字符串，只需要转义双引号和反斜杠
                    var escaped = input.Replace("\\", "\\\\").Replace("\"", "\\\"");
                    return $"\"{escaped}\"";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转义字符串时发生错误: {ex.Message}");
                return "\"\"";
            }
        }

        /// <summary>
        /// 生成类名（清理特殊字符）
        /// 例如: "cafelabs-test" -> "CafelabsTest"
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>类名</returns>
        private static string GenerateClassName(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    return "UnknownWorkflow";
                }

                // 移除特殊字符，只保留字母数字
                string cleaned = "";
                bool capitalizeNext = true;

                foreach (char c in fileName)
                {
                    if (char.IsLetterOrDigit(c))
                    {
                        if (capitalizeNext)
                        {
                            cleaned += char.ToUpper(c);
                            capitalizeNext = false;
                        }
                        else
                        {
                            cleaned += c;
                        }
                    }
                    else
                    {
                        capitalizeNext = true;
                    }
                }

                // 确保类名以字母开头
                if (string.IsNullOrEmpty(cleaned) || !char.IsLetter(cleaned[0]))
                {
                    cleaned = "Workflow" + cleaned;
                }

                return cleaned;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成类名时发生错误: {ex.Message}");
                return "UnknownWorkflow";
            }
        }

        /// <summary>
        /// 生成C#类内容
        /// </summary>
        /// <param name="className">类名</param>
        /// <param name="fileName">原始文件名</param>
        /// <param name="jsonContent">JSON内容</param>
        /// <param name="inputParameters">输入参数列表</param>
        /// <returns>C#类内容</returns>
        private static string GenerateClassContent(string className, string fileName, string jsonContent, List<WorkflowInputParameter> inputParameters)
        {
            var sb = new System.Text.StringBuilder();

            // 文件头部
            sb.AppendLine("using System;");
            sb.AppendLine("using System.Collections.Generic;");
            sb.AppendLine("using System.Threading.Tasks;");
            sb.AppendLine("using Newtonsoft.Json;");
            sb.AppendLine("using Newtonsoft.Json.Linq;");
            sb.AppendLine("using SaveDataService;");
            sb.AppendLine("using SaveDataService.Manage;");
            sb.AppendLine();
            sb.AppendLine("namespace ComfyuiGate");
            sb.AppendLine("{");

            // 类注释
            sb.AppendLine("    /// <summary>");
            sb.AppendLine($"    /// {className} - ComfyUI工作流调用类");
            sb.AppendLine($"    /// 基于文件: {fileName}.json");
            sb.AppendLine($"    /// 自动生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine("    /// 继承RESTfulAPIBase，自动提供RESTful API功能");
            sb.AppendLine("    /// </summary>");
            sb.AppendLine($"    public class {className} : RESTfulAPIBase");
            sb.AppendLine("    {");

            // 工作流JSON常量
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 工作流JSON定义（ComfyUI API格式）");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        private const string WORKFLOW_JSON = @\"" + jsonContent.Replace("\"", "\"\"") + "\";");
            sb.AppendLine();

            // runWorkflow方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 运行工作流");
            sb.AppendLine("        /// </summary>");

            // 生成方法参数
            var methodParams = new List<string>();
            var usedParameterNames = new HashSet<string>();

            foreach (var param in inputParameters)
            {
                // 确保参数名唯一
                string uniqueParameterName = param.ParameterName;
                int counter = 1;
                while (usedParameterNames.Contains(uniqueParameterName))
                {
                    uniqueParameterName = $"{param.ParameterName}{counter}";
                    counter++;
                }
                usedParameterNames.Add(uniqueParameterName);

                sb.AppendLine($"        /// <param name=\"{uniqueParameterName}\">{param.NodeTitle} - {param.OriginalInputName}</param>");
                methodParams.Add($"{param.ParameterType} {uniqueParameterName} = {param.DefaultValue}");

                // 更新参数对象以便后续使用
                param.ParameterName = uniqueParameterName;
            }

            sb.AppendLine("        /// <returns>任务ID</returns>");
            sb.Append("        public static async Task<string> runWorkflow(");
            if (methodParams.Count > 0)
            {
                sb.Append(string.Join(", ", methodParams));
            }
            sb.AppendLine(")");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                // 解析工作流JSON（已经是ComfyUI API格式）");
            sb.AppendLine("                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);");
            sb.AppendLine("                if (workflow == null)");
            sb.AppendLine("                {");
            sb.AppendLine("                    throw new Exception(\"无法解析工作流JSON\");");
            sb.AppendLine("                }");
            sb.AppendLine();

            // 更新参数值
            if (inputParameters.Count > 0)
            {
                sb.AppendLine("                // 更新输入参数（工作流已经是API格式）");
                foreach (var param in inputParameters)
                {
                    sb.AppendLine($"                // 更新节点 {param.NodeId} 的 {param.OriginalInputName} 参数");
                    sb.AppendLine($"                if (workflow[\"{param.NodeId}\"]?[\"inputs\"]?[\"{param.OriginalInputName}\"] != null)");
                    sb.AppendLine("                {");
                    sb.AppendLine($"                    workflow[\"{param.NodeId}\"]![\"inputs\"]![\"{param.OriginalInputName}\"] = JToken.FromObject({param.ParameterName});");
                    sb.AppendLine("                }");
                    sb.AppendLine();
                }
            }

            sb.AppendLine("                // 提交工作流到ComfyUI（已经是API格式，无需转换）");
            sb.AppendLine("                string workflowJson = JsonConvert.SerializeObject(workflow);");
            sb.AppendLine("                var comfyUIManage = ComfyUIManage.Instance;");
            sb.AppendLine();
            sb.AppendLine("                // 首先将工作流保存到数据库（如果不存在）");
            sb.AppendLine($"                string workflowId = EnsureWorkflowExists(workflowJson, \"{className}\");");
            sb.AppendLine("                if (string.IsNullOrEmpty(workflowId))");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine(\"无法保存工作流到数据库\");");
            sb.AppendLine("                    return \"\";");
            sb.AppendLine("                }");
            sb.AppendLine();
            sb.AppendLine("                // 获取可用的服务器");
            sb.AppendLine("                var onlineServers = comfyUIManage.GetOnlineServers();");
            sb.AppendLine("                if (onlineServers.Count == 0)");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine(\"❌ 没有在线的ComfyUI服务器可用\");");
            sb.AppendLine("                    return \"\";");
            sb.AppendLine("                }");
            sb.AppendLine();
            sb.AppendLine("                // 使用第一个在线服务器");
            sb.AppendLine("                var selectedServer = onlineServers[0];");
            sb.AppendLine("                Console.WriteLine($\"🚀 使用服务器: {selectedServer.serverName} ({selectedServer.serverUrl}:{selectedServer.port})\");");
            sb.AppendLine();
            sb.AppendLine("                // 准备输入参数用于日志记录");
            sb.AppendLine("                var inputParams = new {");

            // 添加所有输入参数到日志记录对象
            for (int i = 0; i < inputParameters.Count; i++)
            {
                var param = inputParameters[i];
                var comma = i < inputParameters.Count - 1 ? "," : "";
                sb.AppendLine($"                    {param.ParameterName} = {param.ParameterName}{comma}");
            }

            sb.AppendLine("                };");
            sb.AppendLine();
            sb.AppendLine("                // 直接提交工作流到ComfyUI服务器执行（API格式）");
            sb.AppendLine($"                var result = await comfyUIManage.SubmitWorkflowToServerWithMonitoring(selectedServer.id, workflowJson, workflowId, \"{className}\", inputParams);");
            sb.AppendLine("                string taskId = result.taskId;");
            sb.AppendLine("                string submitResult = result.result;");
            sb.AppendLine();
            sb.AppendLine("                if (string.IsNullOrEmpty(taskId))");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine($\"工作流提交失败: {submitResult}\");");
            sb.AppendLine("                    return \"\";");
            sb.AppendLine("                }");
            sb.AppendLine();
            sb.AppendLine("                // 检查提交结果");
            sb.AppendLine("                if (submitResult.Contains(\"prompt_id\") || submitResult.Contains(\"成功\"))");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine($\"✅ 工作流已成功提交到ComfyUI服务器，任务ID: {taskId}\");");
            sb.AppendLine("                    Console.WriteLine($\"服务器响应: {submitResult}\");");
            sb.AppendLine("                    ");
            sb.AppendLine("                    // 等待一段时间后检查是否有生成的文件");
            sb.AppendLine("                    await Task.Delay(5000); // 等待5秒");
            sb.AppendLine("                    await CheckGeneratedFiles(taskId);");
            sb.AppendLine("                    ");
            sb.AppendLine("                    return taskId;");
            sb.AppendLine("                }");
            sb.AppendLine("                else");
            sb.AppendLine("                {");
            sb.AppendLine("                    Console.WriteLine($\"❌ 工作流提交失败: {submitResult}\");");
            sb.AppendLine("                    return \"\";");
            sb.AppendLine("                }");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"运行工作流失败: {ex.Message}\");");
            sb.AppendLine("                return \"\";");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();



            // 辅助方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 确保工作流存在于数据库中");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"workflowJson\">工作流JSON</param>");
            sb.AppendLine("        /// <param name=\"workflowName\">工作流名称</param>");
            sb.AppendLine("        /// <returns>工作流ID</returns>");
            sb.AppendLine("        private static string EnsureWorkflowExists(string workflowJson, string workflowName)");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                var comfyUIManage = ComfyUIManage.Instance;");
            sb.AppendLine("                return comfyUIManage.AddWorkflow(workflowName, workflowJson, \"generated\", $\"自动生成的工作流: {workflowName}\", \"system\");");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"保存工作流到数据库失败: {ex.Message}\");");
            sb.AppendLine("                return \"\";");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 检查任务生成的文件");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskId\">任务ID</param>");
            sb.AppendLine("        private static async Task CheckGeneratedFiles(string taskId)");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                var comfyUIManage = ComfyUIManage.Instance;");
            sb.AppendLine("                var task = comfyUIManage.GetTaskById(taskId);");
            sb.AppendLine("                if (task != null)");
            sb.AppendLine("                {");
            sb.AppendLine("                    // 检查任务文件");
            sb.AppendLine("                    var filesJson = comfyUIManage.GetTaskFiles(taskId);");
            sb.AppendLine("                    if (!string.IsNullOrEmpty(filesJson) && filesJson != \"[]\")");
            sb.AppendLine("                    {");
            sb.AppendLine("                        Console.WriteLine($\"📁 任务 {taskId} 生成的文件: {filesJson}\");");
            sb.AppendLine("                        ");
            sb.AppendLine("                        // 解析文件信息并检查文件大小");
            sb.AppendLine("                        var files = JsonConvert.DeserializeObject<List<dynamic>>(filesJson);");
            sb.AppendLine("                        if (files != null)");
            sb.AppendLine("                        {");
            sb.AppendLine("                            foreach (var file in files)");
            sb.AppendLine("                            {");
            sb.AppendLine("                                string filePath = file.filePath?.ToString() ?? \"\";");
            sb.AppendLine("                                string fileName = file.fileName?.ToString() ?? \"\";");
            sb.AppendLine("                                long fileSize = file.fileSize ?? 0;");
            sb.AppendLine("                                ");
            sb.AppendLine("                                if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))");
            sb.AppendLine("                                {");
            sb.AppendLine("                                    var actualSize = new System.IO.FileInfo(filePath).Length;");
            sb.AppendLine("                                    Console.WriteLine($\"✅ 文件存在: {fileName} (大小: {actualSize} 字节)\");");
            sb.AppendLine("                                }");
            sb.AppendLine("                                else");
            sb.AppendLine("                                {");
            sb.AppendLine("                                    Console.WriteLine($\"❌ 文件不存在: {fileName} (路径: {filePath})\");");
            sb.AppendLine("                                }");
            sb.AppendLine("                            }");
            sb.AppendLine("                        }");
            sb.AppendLine("                    }");
            sb.AppendLine("                    else");
            sb.AppendLine("                    {");
            sb.AppendLine("                        Console.WriteLine($\"⚠️ 任务 {taskId} 暂未生成文件，可能仍在处理中\");");
            sb.AppendLine("                    }");
            sb.AppendLine("                }");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                Console.WriteLine($\"检查生成文件时发生错误: {ex.Message}\");");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();

            // 获取任务状态方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取任务状态");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <param name=\"taskId\">任务ID</param>");
            sb.AppendLine("        /// <returns>任务状态信息</returns>");
            sb.AppendLine("        public static string GetTaskStatus(string taskId)");
            sb.AppendLine("        {");
            sb.AppendLine("            try");
            sb.AppendLine("            {");
            sb.AppendLine("                var comfyUIManage = ComfyUIManage.Instance;");
            sb.AppendLine("                var task = comfyUIManage.GetTaskById(taskId);");
            sb.AppendLine("                if (task != null)");
            sb.AppendLine("                {");
            sb.AppendLine("                    return JsonConvert.SerializeObject(task, Formatting.Indented);");
            sb.AppendLine("                }");
            sb.AppendLine("                return \"任务不存在\";");
            sb.AppendLine("            }");
            sb.AppendLine("            catch (Exception ex)");
            sb.AppendLine("            {");
            sb.AppendLine("                return $\"获取任务状态失败: {ex.Message}\";");
            sb.AppendLine("            }");
            sb.AppendLine("        }");
            sb.AppendLine();



            // GetApiDescription方法
            sb.AppendLine("        /// <summary>");
            sb.AppendLine("        /// 获取API描述信息");
            sb.AppendLine("        /// </summary>");
            sb.AppendLine("        /// <returns>API描述信息</returns>");
            sb.AppendLine("        public static string GetApiDescription()");
            sb.AppendLine("        {");
            sb.AppendLine($"            return \"工作流: {className}\\n\" +");
            sb.AppendLine($"                   \"描述: {className}工作流\\n\" +");
            sb.AppendLine("                   \"参数:\\n\" +");

            foreach (var param in inputParameters)
            {
                sb.AppendLine($"                   \"  - {param.ParameterName}: {param.OriginalInputName}\\n\" +");
            }

            sb.AppendLine("                   \"\";");
            sb.AppendLine("        }");
            sb.AppendLine();



            // 类结束
            sb.AppendLine("    }");
            sb.AppendLine("}");

            return sb.ToString();
        }
    }
}
