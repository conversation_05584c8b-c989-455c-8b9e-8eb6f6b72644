﻿using GameServer.Util;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.GameService.Tools
{
    internal class CmdTools
    {
        public enum Platform
        {
            Windows,
            Linux,
            Mac
        }
        public static Platform platform = Platform.Windows;
        //获取当前系统
        public static Platform RunningPlatform()
        {
            switch (Environment.OSVersion.Platform)
            {
                case PlatformID.Unix:
                    if (Directory.Exists("/Applications")
                        & Directory.Exists("/System")
                        & Directory.Exists("/Users")
                        & Directory.Exists("/Volumes"))
                        return Platform.Mac;
                    else
                        return Platform.Linux;

                case PlatformID.MacOSX:
                    return Platform.Mac;

                default:
                    return Platform.Windows;
            }
        }
        public static void CreatDLL()
        {
            ////////BufferFactory
            string DllCurDir = AppConfig.Instance.ExcelDllPath;
            var path=System.IO.Path.GetFullPath(DllCurDir);
            runCmd(path, "dotnet build");

        }
        public static void runCmd(string path, string order, string fileName = null, bool isOut = true, bool noWait = false)
        {

            Process p = new Process();
            //根据当前系统启动命令行 设置格式
            string cdString = "cd\\";
            switch (platform)
            {
                case Platform.Windows:
                    p.StartInfo.FileName = "cmd.exe";
                    break;
                case Platform.Linux:
                    cdString = "cd";
                    p.StartInfo.FileName = "sh";
                    break;
                case Platform.Mac:
                    cdString = "cd";
                    ProcessStartInfo startInfoMac = new ProcessStartInfo("/bin/bash")
                    {
                        WorkingDirectory = "/Applications/Utilities/",
                        UseShellExecute = false,
                        RedirectStandardInput = true,
                        RedirectStandardOutput = true
                    };
                    p = new Process
                    {
                        StartInfo = startInfoMac
                    };
                    break;
                default:
                    break;
            }
            //启动命令行
            p.StartInfo.UseShellExecute = false;
            p.StartInfo.RedirectStandardInput = true;
            p.StartInfo.RedirectStandardOutput = true;
            p.StartInfo.StandardOutputEncoding = Encoding.UTF8;// 指定编码
            p.StartInfo.CreateNoWindow = true;
            p.Start();
            //根据cpData调整路径
            if (!string.IsNullOrEmpty(path))
            {
                string[] orderList = path.Split(":/");
                if (orderList.Length > 1)
                {
                    p.StandardInput.WriteLine(orderList[0] + ":");
                    p.StandardInput.WriteLine(cdString);
                    p.StandardInput.WriteLine("cd " + orderList[1]);
                }
                else
                {
                    p.StandardInput.WriteLine(cdString);
                    p.StandardInput.WriteLine("cd " + path);
                }
            }
            //输入命令
            p.StandardInput.WriteLine(order);
            p.StandardInput.WriteLine("exit"); //需要有这句，不然程序会挂机
            //等待完成
            if (!noWait)
            {
                p.WaitForExit();
            }
            //string output = p.StandardOutput.ReadToEnd();
            //if (isOut)
            //{
            //    Console.WriteLine(output);
            //}
            p.Close();
        }
    }
}
