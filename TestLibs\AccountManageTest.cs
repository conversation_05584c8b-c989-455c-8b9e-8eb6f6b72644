using System;
using SaveDataService;

namespace SaveDataService
{
    /// <summary>
    /// AccountManage 测试类
    /// </summary>
    public class AccountManageTest
    {
        /// <summary>
        /// 运行基本功能测试
        /// </summary>
        public static void RunBasicTests()
        {
            Console.WriteLine("开始 AccountManage 基本功能测试...");
            Console.WriteLine("=====================================");
            
            try
            {
                // 测试注册功能
                TestRegisterFunctions();
                
                // 测试登录功能
                TestLoginFunctions();
                
                // 测试密码重置功能
                TestPasswordResetFunctions();
                
                // 测试账号管理功能
                TestAccountManagementFunctions();
                
                Console.WriteLine("=====================================");
                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 测试注册功能
        /// </summary>
        private static void TestRegisterFunctions()
        {
            Console.WriteLine("\n--- 测试注册功能 ---");
            
            // 测试用户名注册
            var result1 = AccountManage.RegisterByUsername("testuser001", "password123", "<EMAIL>", "***********");
            Console.WriteLine($"用户名注册测试: {(result1.Success ? "成功" : "失败")} - {result1.Message}");
            
            // 测试邮箱注册
            var result2 = AccountManage.RegisterByEmail("<EMAIL>", "password456");
            Console.WriteLine($"邮箱注册测试: {(result2.Success ? "成功" : "失败")} - {result2.Message}");
            
            // 测试手机号注册
            var result3 = AccountManage.RegisterByMobile("***********", "password789");
            Console.WriteLine($"手机号注册测试: {(result3.Success ? "成功" : "失败")} - {result3.Message}");
            
            // 测试重复注册（应该失败）
            var result4 = AccountManage.RegisterByUsername("testuser001", "password123");
            Console.WriteLine($"重复用户名注册测试: {(result4.Success ? "意外成功" : "正确失败")} - {result4.Message}");
            
            // 测试密码强度验证
            var result5 = AccountManage.RegisterByUsername("weakuser", "123");
            Console.WriteLine($"弱密码注册测试: {(result5.Success ? "意外成功" : "正确失败")} - {result5.Message}");
        }
        
        /// <summary>
        /// 测试登录功能
        /// </summary>
        private static void TestLoginFunctions()
        {
            Console.WriteLine("\n--- 测试登录功能 ---");
            
            // 测试用户名登录
            var result1 = AccountManage.LoginByUsername("testuser001", "password123", "192.168.1.100");
            Console.WriteLine($"用户名登录测试: {(result1.Success ? "成功" : "失败")} - {result1.Message}");
            
            // 测试邮箱登录
            var result2 = AccountManage.LoginByEmail("<EMAIL>", "password123", "192.168.1.101");
            Console.WriteLine($"邮箱登录测试: {(result2.Success ? "成功" : "失败")} - {result2.Message}");
            
            // 测试手机号登录
            var result3 = AccountManage.LoginByMobile("***********", "password123", "192.168.1.102");
            Console.WriteLine($"手机号登录测试: {(result3.Success ? "成功" : "失败")} - {result3.Message}");
            
            // 测试错误密码登录（应该失败）
            var result4 = AccountManage.LoginByUsername("testuser001", "wrongpassword");
            Console.WriteLine($"错误密码登录测试: {(result4.Success ? "意外成功" : "正确失败")} - {result4.Message}");
            
            // 测试令牌登录
            if (result1.Success && !string.IsNullOrEmpty(result1.AccessToken))
            {
                var tokenResult = AccountManage.LoginByToken(result1.AccessToken);
                Console.WriteLine($"令牌登录测试: {(tokenResult.Success ? "成功" : "失败")} - {tokenResult.Message}");
            }
        }
        
        /// <summary>
        /// 测试密码重置功能
        /// </summary>
        private static void TestPasswordResetFunctions()
        {
            Console.WriteLine("\n--- 测试密码重置功能 ---");
            
            // 测试邮箱验证码重置密码（使用模拟验证码）
            var result1 = AccountManage.ResetPasswordByEmail("<EMAIL>", "123456", "newpassword123");
            Console.WriteLine($"邮箱验证码重置密码测试: {(result1.Success ? "成功" : "失败")} - {result1.Message}");

            // 测试手机验证码重置密码（使用模拟验证码）
            var result2 = AccountManage.ResetPasswordByMobile("***********", "123456", "newpassword456");
            Console.WriteLine($"手机验证码重置密码测试: {(result2.Success ? "成功" : "失败")} - {result2.Message}");

            // 验证新密码是否生效
            var loginResult = AccountManage.LoginByUsername("testuser001", "newpassword456");
            Console.WriteLine($"新密码登录验证: {(loginResult.Success ? "成功" : "失败")} - {loginResult.Message}");

            // 测试不存在的邮箱重置（应该失败）
            var result3 = AccountManage.ResetPasswordByEmail("<EMAIL>", "123456", "password123");
            Console.WriteLine($"不存在邮箱重置测试: {(result3.Success ? "意外成功" : "正确失败")} - {result3.Message}");
        }
        
        /// <summary>
        /// 测试账号管理功能
        /// </summary>
        private static void TestAccountManagementFunctions()
        {
            Console.WriteLine("\n--- 测试账号管理功能 ---");
            
            // 先登录获取账号信息
            var loginResult = AccountManage.LoginByUsername("testuser001", "newpassword456");
            if (!loginResult.Success || loginResult.Account == null)
            {
                Console.WriteLine("无法登录，跳过账号管理测试");
                return;
            }

            var accountId = loginResult.Account.id;
            
            // 测试修改密码
            var changeResult = AccountManage.ChangePassword(accountId, "newpassword456", "finalpassword123");
            Console.WriteLine($"修改密码测试: {(changeResult.Success ? "成功" : "失败")} - {changeResult.Message}");
            
            // 测试设置安全问题
            var questions = new string[] { "您出生的城市是？", "您的小学老师姓名？", "您最喜欢的颜色？" };
            var answers = new string[] { "北京", "张老师", "蓝色" };
            var securityResult = AccountManage.SetSecurityQuestions(accountId, questions, answers);
            Console.WriteLine($"设置安全问题测试: {(securityResult.Success ? "成功" : "失败")} - {securityResult.Message}");
            
            // 测试启用二次验证
            var twoFactorResult = AccountManage.SetTwoFactorAuth(accountId, true);
            Console.WriteLine($"启用二次验证测试: {(twoFactorResult.Success ? "成功" : "失败")} - {twoFactorResult.Message}");
            
            // 测试更新账号信息
            var updateResult = AccountManage.UpdateAccountInfo(accountId, "测试昵称", "avatar_test.jpg", "张三");
            Console.WriteLine($"更新账号信息测试: {(updateResult.Success ? "成功" : "失败")} - {updateResult.Message}");
            
            // 测试通过安全问题重置密码
            var securityResetResult = AccountManage.ResetPasswordBySecurityQuestion("testuser001", answers, "securitypassword123");
            Console.WriteLine($"安全问题重置密码测试: {(securityResetResult.Success ? "成功" : "失败")} - {securityResetResult.Message}");
        }
        
        /// <summary>
        /// 测试边界情况和错误处理
        /// </summary>
        public static void TestEdgeCases()
        {
            Console.WriteLine("\n--- 测试边界情况 ---");
            
            // 测试空参数
            var result1 = AccountManage.RegisterByUsername("", "password123");
            Console.WriteLine($"空用户名注册: {(result1.Success ? "意外成功" : "正确失败")} - {result1.Message}");
            
            var result2 = AccountManage.LoginByEmail("", "password123");
            Console.WriteLine($"空邮箱登录: {(result2.Success ? "意外成功" : "正确失败")} - {result2.Message}");
            
            // 测试无效格式
            var result3 = AccountManage.RegisterByEmail("invalid-email", "password123");
            Console.WriteLine($"无效邮箱格式注册: {(result3.Success ? "意外成功" : "正确失败")} - {result3.Message}");
            
            var result4 = AccountManage.RegisterByMobile("123", "password123");
            Console.WriteLine($"无效手机号格式注册: {(result4.Success ? "意外成功" : "正确失败")} - {result4.Message}");
            
            // 测试无效令牌
            var result5 = AccountManage.LoginByToken("invalid-token");
            Console.WriteLine($"无效令牌登录: {(result5.Success ? "意外成功" : "正确失败")} - {result5.Message}");
        }
    }
}
