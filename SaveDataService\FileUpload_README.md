# 文件上传功能说明

## 功能概述

本系统在WebServerHandle中实现了完整的文件上传功能，支持：

1. **单文件上传（支持断点续传）** - 大文件可以分块上传，网络中断后可以继续上传
2. **文件夹批量上传** - 一次性上传整个文件夹中的所有文件
3. **HTML文件移动** - 将上传的HTML文件移动到指定位置
4. **上传进度查询** - 实时查询上传进度

## 技术特性

### 断点续传实现
- 使用HTTP Range请求头实现分块上传
- 服务器维护上传会话状态
- 支持1MB大小的数据块传输
- 自动重试机制，网络中断后继续上传

### 安全特性
- 文件大小验证
- 路径安全检查，防止目录遍历攻击
- CORS跨域支持
- 文件完整性验证

### 性能优化
- 异步文件操作
- 内存高效的流式处理
- 并发上传会话管理

## API接口

### 1. 文件上传（断点续传）
```
POST /api/upload
Headers:
  X-File-Name: 文件名
  X-File-Size: 文件总大小
  X-File-Hash: 文件哈希值
  Content-Range: bytes 开始字节-结束字节/总大小
Body: 文件数据块
```

### 2. 文件夹批量上传
```
POST /api/upload/folder
Headers:
  X-Folder-Path: 目标文件夹路径
Content-Type: multipart/form-data
Body: 多个文件
```

### 3. 文件移动
```
POST /api/move
Content-Type: application/json
Body: {
  "sourcePath": "源文件路径",
  "targetPath": "目标文件路径"
}
```

### 4. 查询上传进度
```
GET /api/upload/progress?sessionId=会话ID
```

## 使用方法

### 1. 启动服务器
确保服务器正在运行，默认端口根据配置而定。

### 2. 访问上传页面
打开浏览器访问：`http://localhost:端口/upload.html`

### 3. 使用上传功能

#### 单文件上传
1. 点击"选择文件"按钮或拖拽文件到上传区域
2. 系统自动开始上传，显示进度条
3. 支持大文件断点续传，网络中断后会自动重试

#### 文件夹上传
1. 点击"选择文件夹"按钮
2. 选择要上传的文件夹
3. 系统会批量上传文件夹中的所有文件

#### HTML文件移动
1. 在"HTML文件移动"区域填写源文件路径和目标路径
2. 点击"移动文件"按钮
3. 系统会将文件从源位置移动到目标位置

## 文件存储结构

```
SaveDataService/
├── uploads/          # 最终上传文件存储目录
│   ├── file1.txt
│   ├── uploaded_folder/
│   │   ├── subfolder/
│   │   └── files...
│   └── ...
├── temp/            # 临时文件存储目录（断点续传）
│   ├── session1.tmp
│   └── ...
└── upload.html      # 上传界面
```

## 代码结构

### 核心类
- `WebServerHandle` - 主要的文件处理类
- `FileUploadInfo` - 上传文件信息
- `FileUploadResponse` - 上传响应
- `MoveFileRequest` - 文件移动请求

### 主要方法
- `HandleFileUpload()` - 处理文件上传（断点续传）
- `HandleFolderUpload()` - 处理文件夹批量上传
- `HandleMoveHtmlFile()` - 处理HTML文件移动
- `HandleGetUploadProgress()` - 查询上传进度

## 配置说明

### 文件大小限制
默认支持大文件上传，可以通过修改`CHUNK_SIZE`常量调整分块大小。

### 存储路径配置
- 上传目录：`AppContext.BaseDirectory + "uploads"`
- 临时目录：`AppContext.BaseDirectory + "temp"`

### CORS配置
已配置支持跨域请求，允许从任何域名访问API。

## 测试功能

使用`FileUploadTest`类进行功能测试：

```csharp
// 运行基本测试
await FileUploadTest.TestFileUpload();

// 显示使用说明
FileUploadTest.ShowUsageInstructions();

// 生成测试文件
await FileUploadTest.GenerateTestFile("test.dat", 100); // 生成100MB测试文件

// 清理测试文件
FileUploadTest.CleanupTestFiles();
```

## 故障排除

### 常见问题

1. **上传失败**
   - 检查文件大小是否超出限制
   - 确认目标目录有写入权限
   - 检查网络连接

2. **断点续传不工作**
   - 确认浏览器支持Range请求
   - 检查服务器端会话是否正常维护

3. **文件移动失败**
   - 确认源文件存在
   - 检查目标目录权限
   - 验证路径格式正确

### 日志查看
服务器会在控制台输出上传进度和错误信息，可以通过日志排查问题。

## 扩展功能

### 可以添加的功能
1. 文件类型限制
2. 用户权限验证
3. 上传速度限制
4. 文件压缩
5. 病毒扫描
6. 文件预览
7. 批量下载

### 性能优化建议
1. 使用Redis存储上传会话
2. 实现文件去重
3. 添加CDN支持
4. 实现分布式存储

## 安全注意事项

1. 在生产环境中应该添加身份验证
2. 限制上传文件类型和大小
3. 定期清理临时文件
4. 监控磁盘空间使用
5. 实现文件扫描和过滤

## 许可证

本功能作为SaveDataService项目的一部分，遵循项目的许可证协议。
