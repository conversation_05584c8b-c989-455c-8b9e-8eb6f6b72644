﻿using System;
using System.IO;
using System.Text;
using Microsoft.VisualStudio.TestTools.UnitTesting;

[TestClass]
public class BinaryTypeHelperTests
{
    private MemoryStream memoryStream;
    private BinaryWriter writer;
    private BinaryReader reader;

    [TestInitialize]
    public void Setup()
    {
        memoryStream = new MemoryStream();
        writer = new BinaryWriter(memoryStream);
        reader = new BinaryReader(memoryStream);
    }

    [TestCleanup]
    public void TearDown()
    {
        writer?.Dispose();
        reader?.Dispose();
        memoryStream?.Dispose();
    }

    #region Basic Type Tests

    [TestMethod]
    public void TestString()
    {
        string testValue = "Hello, 世界!";
        BinaryTypeHelper.TypeToValue("string", testValue, writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("string", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestInt()
    {
        int testValue = 123456;
        BinaryTypeHelper.TypeToValue("int", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("int", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestUInt()
    {
        uint testValue = 4294967295;
        BinaryTypeHelper.TypeToValue("uint", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("uint", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestShort()
    {
        short testValue = -32768;
        BinaryTypeHelper.TypeToValue("short", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("short", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestUShort()
    {
        ushort testValue = 65535;
        BinaryTypeHelper.TypeToValue("ushort", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("ushort", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestLong()
    {
        long testValue = -9223372036854775808;
        BinaryTypeHelper.TypeToValue("long", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("long", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestULong()
    {
        ulong testValue = 18446744073709551615;
        BinaryTypeHelper.TypeToValue("ulong", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("ulong", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestByte()
    {
        byte testValue = 255;
        BinaryTypeHelper.TypeToValue("byte", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("byte", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestSByte()
    {
        sbyte testValue = -128;
        BinaryTypeHelper.TypeToValue("sbyte", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("sbyte", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestFloat()
    {
        float testValue = 3.1415926f;
        BinaryTypeHelper.TypeToValue("float", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("float", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestDouble()
    {
        double testValue = 3.141592653589793;
        BinaryTypeHelper.TypeToValue("double", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("double", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestDecimal()
    {
        decimal testValue = 79228162514264337593543950335m;
        BinaryTypeHelper.TypeToValue("decimal", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("decimal", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestBool()
    {
        bool testValue = true;
        BinaryTypeHelper.TypeToValue("bool", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("bool", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestChar()
    {
        char testValue = '中';
        BinaryTypeHelper.TypeToValue("char", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("char", reader);
        Assert.AreEqual(testValue, result);
    }

    #endregion

    #region Special Type Tests
    [TestMethod]
    public void TestISO8601DateTime()
    {
        DateTime testValue = DateTime.UtcNow;
        BinaryTypeHelper.WriteDateTime(testValue.ToString("o"), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(testValue.ToString("o"), result.ToString("o"));
    }

    [TestMethod]
    public void TestISO8601DateOnly()
    {
        DateTime testValue = DateTime.Today;
        BinaryTypeHelper.WriteDateTime(testValue.ToString("yyyy-MM-dd"), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(testValue.Date, result.Date);
    }

    [TestMethod]
    public void TestUnixTimeSeconds()
    {
        long unixTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        BinaryTypeHelper.WriteDateTime(unixTime.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(unixTime, DateTimeOffset.UtcNow.ToUnixTimeSeconds());
    }

    [TestMethod]
    public void TestUnixTimeMilliseconds()
    {
        long unixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        BinaryTypeHelper.WriteDateTime(unixTimeMs.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(unixTimeMs, DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(), 1000); // Allow 1 second difference
    }

    [TestMethod]
    public void TestDotNetTicks()
    {
        long ticks = DateTime.UtcNow.Ticks;
        BinaryTypeHelper.WriteDateTime(ticks.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(ticks, result.Ticks, 10000000); // Allow 1 second difference
    }

    [TestMethod]
    public void TestWindowsFileTime()
    {
        long fileTime = DateTime.UtcNow.ToFileTimeUtc();
        BinaryTypeHelper.WriteDateTime(fileTime.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(fileTime, result.ToFileTimeUtc());
    }

    [TestMethod]
    public void TestExcelSerialDate()
    {
        double excelDate = DateTime.UtcNow.ToOADate();
        BinaryTypeHelper.WriteDateTime(excelDate.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(excelDate, result.ToOADate(), 0.00001); // Small tolerance for floating point
    }

    [TestMethod]
    public void TestRFC1123()
    {
        string rfc1123 = DateTime.UtcNow.ToString("r");
        BinaryTypeHelper.WriteDateTime(rfc1123, writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader);
        Assert.AreEqual(rfc1123, result.ToString("r"));
    }

    [TestMethod]
    public void TestCustomFormat()
    {
        string customFormat = "dd/MM/yyyy HH:mm:ss";
        DateTime testValue = DateTime.UtcNow;
        BinaryTypeHelper.WriteDateTime(testValue.ToString(customFormat), writer, customFormat);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadDateTime(reader, customFormat);
        Assert.AreEqual(testValue.ToString(customFormat), result.ToString(customFormat));
    }

    [TestMethod]
    [ExpectedException(typeof(InvalidDataException))]
    public void TestInvalidDateTimeThrowsException()
    {
        writer.Write((int)DateFormat.Custom); // Write custom format enum
        writer.Write("invalid-date"); // Write invalid date string
        memoryStream.Position = 0;
        BinaryTypeHelper.ReadDateTime(reader);
    }

    [TestMethod]
    public void TestGuid()
    {
        Guid testValue = Guid.NewGuid();
        BinaryTypeHelper.TypeToValue("guid", testValue.ToString(), writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("guid", reader);
        Assert.AreEqual(testValue, result);
    }

    #endregion

    #region Array Type Tests

    [TestMethod]
    public void TestStringArray()
    {
        string[] testValue = { "Hello", "世界", "Test", "数组" };
        BinaryTypeHelper.TypeToValue("string[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (string[])BinaryTypeHelper.ReadValue("string[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestIntArray()
    {
        int[] testValue = { 1, -2, 3, -4, 5 };
        BinaryTypeHelper.TypeToValue("int[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (int[])BinaryTypeHelper.ReadValue("int[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestFloatArray()
    {
        float[] testValue = { 1.1f, -2.2f, 3.3f, -4.4f, 5.5f };
        BinaryTypeHelper.TypeToValue("float[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (float[])BinaryTypeHelper.ReadValue("float[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestDateTimeArray()
    {
        BinaryTypeHelper.SetDateFormat(DateFormat.ISO8601);
        DateTime[] testValue = { DateTime.Now, DateTime.Today, DateTime.UtcNow };
        BinaryTypeHelper.TypeToValue("datetime[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (DateTime[])BinaryTypeHelper.ReadValue("datetime[]", reader);
        for (int i = 0; i < testValue.Length; i++)
        {
            Assert.AreEqual(testValue[i].ToString("o"), result[i].ToString("o"));
        }
    }

    [TestMethod]
    public void TestGuidArray()
    {
        Guid[] testValue = { Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid() };
        BinaryTypeHelper.TypeToValue("guid[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (Guid[])BinaryTypeHelper.ReadValue("guid[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestBoolArray()
    {
        bool[] testValue = { true, false, true, false };
        BinaryTypeHelper.TypeToValue("bool[]", string.Join(",", testValue), writer);
        memoryStream.Position = 0;
        var result = (bool[])BinaryTypeHelper.ReadValue("bool[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestByteArray()
    {
        byte[] testValue = { 0x01, 0xFF, 0x7F, 0x00 };
        // For byte arrays, write the bytes directly
        writer.Write((ushort)testValue.Length);
        writer.Write(testValue);
        memoryStream.Position = 0;
        var result = (byte[])BinaryTypeHelper.ReadValue("byte[]", reader);
        CollectionAssert.AreEqual(testValue, result);
    }

    #endregion

    #region Boundary and Exception Tests

    [TestMethod]
    public void TestEmptyString()
    {
        string testValue = "";
        BinaryTypeHelper.TypeToValue("string", testValue, writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("string", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    public void TestNullString()
    {
        string testValue = null;
        BinaryTypeHelper.TypeToValue("string", testValue, writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("string", reader);
        Assert.AreEqual("", result); // According to implementation, null should be converted to empty string
    }

    [TestMethod]
    public void TestMaxLengthString()
    {
        string testValue = new string('a', ushort.MaxValue);
        BinaryTypeHelper.TypeToValue("string", testValue, writer);
        memoryStream.Position = 0;
        var result = BinaryTypeHelper.ReadValue("string", reader);
        Assert.AreEqual(testValue, result);
    }

    [TestMethod]
    [ExpectedException(typeof(Exception))]
    public void TestStringExceedsMaxLength()
    {
        string testValue = new string('a', ushort.MaxValue + 1);
        BinaryTypeHelper.TypeToValue("string", testValue, writer);
    }

    [TestMethod]
    [ExpectedException(typeof(NotSupportedException))]
    public void TestInvalidType()
    {
        BinaryTypeHelper.TypeToValue("invalidtype", "value", writer);
    }

    [TestMethod]
    [ExpectedException(typeof(NotSupportedException))]
    public void TestInvalidArrayType()
    {
        BinaryTypeHelper.TypeToValue("invalidtype[]", "1,2,3", writer);
    }

    [TestMethod]
    [ExpectedException(typeof(FormatException))]
    public void TestInvalidDateTimeFormat()
    {
        BinaryTypeHelper.SetDateFormat(DateFormat.Custom, "invalid format");
        BinaryTypeHelper.TypeToValue("datetime", DateTime.Now.ToString(), writer);
    }

    #endregion
}