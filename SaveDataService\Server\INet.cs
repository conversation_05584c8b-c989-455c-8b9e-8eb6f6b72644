﻿
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace NetService
{
    public enum NetEnum
    {
        Init,
        /// <summary>
        /// 链接成功
        /// </summary>
        Connected,
        /// <summary>
        /// 链接断开
        /// </summary>
        Disconnetc,
        /// <summary>
        /// 链接失败，并且超过重连次数，或者创建本地socke bind失败之类的
        /// </summary>
        Error,
        /// <summary>
        /// 正在链接中
        /// </summary>
        Cnnecting,
        /// <summary>
        /// 链接失败重连当中
        /// </summary>
        ReConnecting
    }

    public abstract class INet
    {
        public bool IsAccept = false;

        public abstract NetEnum NetState();

        public abstract Task<bool> DisConnect();

        public abstract Task<bool> Connect(string ip, int port);

        public abstract Task<bool> ReConnect();

        public abstract Task<bool> SendMessage(byte[] bytes);

        public abstract event Action OnClose;
        public abstract event Action<byte[]> OnRecv;
    }


}
