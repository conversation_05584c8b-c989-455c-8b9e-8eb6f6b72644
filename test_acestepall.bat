@echo off
echo ===================================
echo AceStepAll ComfyUI 工作流测试脚本
echo ===================================
echo.

REM 检查是否有参数
if "%1"=="quick" (
    echo 运行快速测试模式...
    dotnet run --project . TestAceStepAll.cs quick
    goto end
)

if "%1"=="full" (
    echo 运行完整测试模式...
    dotnet run --project . TestAceStepAll.cs full
    goto end
)

REM 显示选项菜单
echo 请选择测试模式:
echo 1. 快速测试 (推荐)
echo 2. 完整测试
echo 3. 交互式测试
echo.
set /p choice=请输入选项 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 开始快速测试...
    dotnet run --project . TestAceStepAll.cs quick
) else if "%choice%"=="2" (
    echo.
    echo 开始完整测试...
    dotnet run --project . TestAceStepAll.cs full
) else if "%choice%"=="3" (
    echo.
    echo 开始交互式测试...
    dotnet run --project . TestAceStepAll.cs
) else (
    echo 无效选项，使用默认快速测试...
    dotnet run --project . TestAceStepAll.cs quick
)

:end
echo.
echo 测试完成！
pause
