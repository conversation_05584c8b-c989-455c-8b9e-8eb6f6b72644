﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.IO.MemoryMappedFiles;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.GameService.Tools
{
    class MemoryInfo
    {
        public MemoryInfo(MemoryMappedViewAccessor setMemoryMap,long setStartPos)
        {
            memoryMap = setMemoryMap;
            startPos = setStartPos;
            setPos = 0;
        }
        private MemoryMappedViewAccessor memoryMap;
        public long setPos
        {
            get
            {
                return _setPos - startPos;
            }
            set
            {
                _setPos = value + startPos;
            }
        }
        private long _setPos = 0;
        public long startPos = 0;
        public void addPos(int data)
        {
            _setPos += data;
        }
        public string ReadString()
        {
            var len = ReadUInt16();
            List<byte> outString = new List<byte>();
            for (int i = 0; i < len; i++)
            {
                outString.Add(memoryMap.ReadByte(_setPos));
                _setPos++;
            }
            var outData = Encoding.UTF8.GetString(outString.ToArray());
            return outData;
        }
        public bool ReadBoolean()
        {
            var outData = memoryMap.ReadBoolean(_setPos);
            addPos(1);
            return outData;
        }
        public byte ReadByte()
        {
            var outData = memoryMap.ReadByte(_setPos);
            addPos(1);
            return outData;
        }
        public char ReadChar()
        {
            var outData = memoryMap.ReadChar(_setPos);
            addPos(1);
            return outData;
        }

        public double ReadDouble()
        {
            var outData = memoryMap.ReadDouble(_setPos);
            addPos(8);
            return outData;
        }
        public short ReadInt16()
        {
            var outData = memoryMap.ReadInt16(_setPos);
            addPos(2);
            return outData;
        }
        public int ReadInt32()
        {
            var outData = memoryMap.ReadInt32(_setPos);
            addPos(4);
            return outData;
        }
        public long ReadInt64()
        {
            var outData = memoryMap.ReadInt64(_setPos);
            addPos(8);
            return outData;
        }
        public sbyte ReadSByte()
        {
            var outData = memoryMap.ReadSByte(_setPos);
            addPos(1);
            return outData;
        }
        public float ReadSingle()
        {
            var outData = memoryMap.ReadSingle(_setPos);
            addPos(4);
            return outData;
        }
        public ushort ReadUInt16()
        {
            var outData = memoryMap.ReadUInt16(_setPos);
            addPos(2);
            return outData;
        }
        public uint ReadUInt32()
        {
            var outData = memoryMap.ReadUInt32(_setPos);
            addPos(4);
            return outData;
        }
        public ulong ReadUInt64()
        {
            var outData = memoryMap.ReadUInt64(_setPos);
            addPos(8);
            return outData;
        }
        public void write(string data)
        {
            byte[] chars = MemoryMapTools.GetStringBytes(data);
            if (chars.Length > 65535)
                throw new Exception("字符串长度超出限制 65535");
            write((ushort)chars.Length);
            memoryMap.WriteArray(_setPos, chars, 0, chars.Length);
            _setPos += Encoding.Default.GetBytes(data).Length;
        }
        public void write(bool data)
        {
            memoryMap.Write(_setPos, data);
            addPos(1);
        }
        public void write(ulong data)
        {
            memoryMap.Write(_setPos, data);
            addPos(8);
        }
        public void write(uint data)
        {
            memoryMap.Write(_setPos, data);
            addPos(4);
        }
        public void write(byte data)
        {
            memoryMap.Write(_setPos, data);
            addPos(1);
        }
        public void write(float data)
        {
            memoryMap.Write(_setPos, data);
            addPos(4);
        }
        public void write(long data)
        {
            memoryMap.Write(_setPos, data);
            addPos(8);
        }
        public void write(int data)
        {
            memoryMap.Write(_setPos, data);
            addPos(4);
        }
        public void write(ushort data)
        {
            memoryMap.Write(_setPos, data);
            addPos(2);
        }
        public void write(short data)
        {
            memoryMap.Write(_setPos, data);
            addPos(2);
        }
        public void write(double data)
        {
            memoryMap.Write(_setPos, data);
            addPos(8);
        }
        public void write(sbyte data)
        {
            memoryMap.Write(_setPos, data);
            addPos(1);
        }
        public void write(char data)
        {
            memoryMap.Write(_setPos, data);
            addPos(1);
        }
    }
    class MemoryMapTools
    {
        private static ConcurrentDictionary<string, MemoryInfo> memoryInfos = new ConcurrentDictionary<string, MemoryInfo>();
        private static MemoryMappedViewAccessor _memoryMap = null;
        /// <summary>
        /// 获取内存映射信息
        /// </summary>
        /// <param name="memroyName"></param>
        /// <param name="startPos"></param>
        /// <returns></returns>
        public static MemoryInfo GetMemoryMap(string memroyName,long startPos=0)
        {
            return null;
            long size = 0x40000000;
            if (!File.Exists(AppConfig.Instance.MemoryMapPath))
            {
                File.WriteAllBytes(AppConfig.Instance.MemoryMapPath, new byte[size]);
            }
            if (_memoryMap == null)
            {
                var mmf = MemoryMappedFile.CreateFromFile(AppConfig.Instance.MemoryMapPath, FileMode.Open, "maData");
                _memoryMap = mmf.CreateViewAccessor(0, size);
            }
            MemoryInfo newMap;
            if (!memoryInfos.ContainsKey(memroyName))
            {
                newMap = new MemoryInfo(_memoryMap, startPos);
                memoryInfos[memroyName] = newMap;
            }
            else
            {
                newMap = memoryInfos[memroyName];
            }
            return newMap;
        }
        public static byte[] GetStringBytes(string str)
        {
            string strs = (str == null ? "" : str);
            return Encoding.UTF8.GetBytes(strs);
        }

        public static string getStringInLength(string orgString, int getLength)
        {
            string newString = orgString;
            if (string.IsNullOrEmpty(newString))
            {
                newString = "";
            }
            int len = Encoding.Default.GetBytes(newString).Length;
            if (len < getLength)
            {
                for (int i = len; i < getLength; i++)
                {
                    newString += " ";
                }
            }
            else
            {
                //之后想
            }
            return newString;
        }
    }
}
