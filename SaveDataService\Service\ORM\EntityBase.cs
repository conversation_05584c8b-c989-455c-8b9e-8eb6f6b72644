﻿using GameServer.GameService.Tools;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System;
using System.IO;
using Microsoft.EntityFrameworkCore;

namespace GameServer.ExcelData
{
        //[Index(nameof(tableID))]
    public class EntityBase
    {
        //[Key]
        public UInt64 tableID { get; set; }
        [JsonIgnore]
        public bool IsDelete { get; set; }
        [JsonIgnore]
        public DateTime? CreateTime { get; set; }
        [JsonIgnore]
        public DateTime? UpdateTime { get; set; }
        [JsonIgnore]
        public float? version { get; set; }
        //public virtual string id { get; set; } = "";
        public virtual EntityBase getDataByIdInstance(string id)
        {
            return null;
        }
        public virtual EntityBase getInstance()
        {
            return null;
        }
        public virtual void Init()
        {

        }
        public virtual List<string> getSqlCode()
        {
            return new List<string>();
        }
        public virtual string getExcelString()
        {
            return "";
        }        
        public virtual string getMySqlString()
        {
            return "";
        }
        public virtual void setParams(List<string>  paramList)
        {

        }
        protected static byte[] GetUTFBytes(object str)
        {
            string strs = str == null ? "" : str.ToString();
            return Encoding.UTF8.GetBytes(strs);
        }
        protected static void writeWriteString(string value, BinaryWriter file)
        {
            byte[] bytes = GetUTFBytes(value);
            if (bytes.Length > 65535)
                throw new Exception("字符串长度超出限制 65535");
            file.Write((ushort)bytes.Length);
            file.Write(bytes);
        }
        public virtual string getName()
        {
            return "";
        }
        public virtual byte[] toBytes()
        {
            return null;
        }
        public virtual void addBuffer(BinaryWriter br)
        {
        }

    }
}
