﻿
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.WebSockets;

namespace GameServer.GameService
{

    class ClientData
    {
        public WebSocket websocket;
        public HttpContext httpContext;
        public string httpContextString;
        public byte[] websocketBytes = null;
        public UInt64 clientID { get => _clientID; set => SetClientID(value); }
        public string playerToken { get; private set; }
        public string ip;

        private UInt64 _clientID;
        public ClientData()
        {

        }

        // 设置 clientID , 并更新 playerToken
        private void SetClientID(UInt64 id)
        {
            _clientID = id;
            //if (LoginManager.userTokens.TryGetValue(id, out string token))
            //{
            //    playerToken = token;
            //}
            //else
            //{
            //    playerToken = null;
            //}
        }

        /// <summary>
        /// 进行游戏的房间
        /// </summary>
        public static ConcurrentDictionary<UInt64, uint> clientMsgList = new ConcurrentDictionary<UInt64, uint>();

        public void sendLog(string context)
        {
            sendMessage("[LOG]" + context);
        }


        public static byte[] addBytes(byte[] data1, byte[] data2)
        {
            byte[] data3 = new byte[data1.Length + data2.Length];
            data1.CopyTo(data3, 0);
            data2.CopyTo(data3, data1.Length);
            return data3;
        }
        private static long bytecount = 0;
        public async void sendMessage(string context)
        {
            if (websocket != null)
            {
                if (websocket.State == WebSocketState.Open)
                {
                    try
                    {
                        byte[] by = System.Text.Encoding.UTF8.GetBytes(context);
                        bytecount+=by.Length;

                        await websocket.SendAsync(by, WebSocketMessageType.Text, true, System.Threading.CancellationToken.None);
                    }
                    catch (Exception ex)
                    {

                        Console.WriteLine(ex);
                    }
                }
            }
            else
            {
                if (httpContext != null)
                {
                    try
                    {
                        await httpContext.Response.WriteAsync(context);
                    }
                    catch (Exception ex)
                    {

                        Console.WriteLine(ex);
                    }

                }
            }
        }

        public void sendMessage(byte[] bytes)
        {
            if (websocket != null)
            {
                if (websocket.State == WebSocketState.Open)
                {
                    try
                    {
                        //bytecount += bytes.Length;
                        websocket.SendAsync(bytes, WebSocketMessageType.Binary, true, System.Threading.CancellationToken.None);
                    }
                    catch (Exception ex)
                    {

                        Console.WriteLine(ex);
                    }
                }
            }
            else
            {
                if (httpContext != null)
                {
                    //try
                    //{
                    //    await httpContext.Response.WriteAsync(bytes);
                    //}
                    //catch (Exception ex)
                    //{

                    //    Console.WriteLine(ex);
                    //}

                }
            }
        }

        /**
         * 发送数据改变的消息, 前端通过 WebsocketTool 类监听数据的变化
         * @param className 类名
         * @param fieldName MessageCode 类型或者字段名
         * @param datas 发送的数据
         * @param argsType 数据处理类型
         */
        public void sendData(string className, string fieldName, object datas, string argsType = null)
        {
            //var message = new MessageClass();
            //message.className = className;
            //message.functionName = fieldName;
            //message.args.Add(datas);
            //if (message.argsType != null)
            //{
            //    message.argsType = argsType;
            //}
            //sendMessage(message);
        }

        //public void sendData(string className, string fieldName, BuildingListBase datas, string argsType = null)
        //{
        //    sendMessage(Tools.MessageTools.getSendMsg(className, fieldName, datas, 0, argsType));
        //}
        //public void sendData(string className, string fieldName, BuildingListBase[] datas, string argsType = null)
        //{
        //    sendMessage(Tools.MessageTools.getSendMsg(className, fieldName, datas.ToList(), 0, argsType));
        //}
        //public void sendData(string className, string fieldName, Dictionary<string, BuildingListBase> datas, string argsType = null)
        //{
        //    sendMessage(Tools.MessageTools.getSendMsg(className, fieldName, datas, 0, argsType));
        //}
        //public void sendData(string className, string fieldName, object  datas, string argsType = null)
        //{
        //    sendMessage(Tools.MessageTools.getSendMsg(className, fieldName, datas, 0, argsType));
        //}



        /**
         * 发送提示数据给 WsDataManager 中指定的类
         * @param className 类名
         * @param data 包含的数据
         * @param type 提示类型
         */
        public void sendTipData(string className, object data, int type)
        {
            //sendData(className, MessageCode.TipData, new
            //{
            //    data,
            //    type
            //});
        }

        /// <summary>
        /// 从httpContext中读取文字内容
        /// </summary>
        public void getHttpStringData()
        {
            var syncIOFeature = httpContext.Features.Get<IHttpBodyControlFeature>();

            if (syncIOFeature != null)
            {
                syncIOFeature.AllowSynchronousIO = true;
            }

            //将前端服务器传递过来的数据转换成rpc类
            StreamReader reader = new StreamReader(httpContext.Request.Body);
            httpContextString = reader.ReadToEnd();
            reader.Close();
            reader.Dispose();
        }
    }
}
