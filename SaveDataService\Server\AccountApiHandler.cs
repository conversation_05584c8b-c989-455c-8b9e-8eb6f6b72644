using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// 账号API处理器 - 处理所有与账号管理相关的HTTP请求
    /// </summary>
    public class AccountApiHandler
    {
        /// <summary>
        /// 处理账号管理 API 请求
        /// </summary>
        public static async Task HandleAccountApi(HttpContext context)
        {
            try
            {
                // 设置 CORS 头
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

                // 处理 OPTIONS 预检请求
                if (context.Request.Method == "OPTIONS")
                {
                    context.Response.StatusCode = 200;
                    return;
                }

                var path = context.Request.Path.Value ?? "";
                var methodName = path.Split('/').LastOrDefault()?.ToLower() ?? "";

                // 读取请求体
                string requestBody;
                using (var reader = new StreamReader(context.Request.Body))
                {
                    requestBody = await reader.ReadToEndAsync();
                }

                // 解析请求参数
                var requestData = string.IsNullOrEmpty(requestBody)
                    ? new Dictionary<string, object>()
                    : JsonConvert.DeserializeObject<Dictionary<string, object>>(requestBody) ?? new Dictionary<string, object>();

                // 调用对应的 AccountManage 方法
                var result = await CallAccountManageMethod(methodName, requestData);

                context.Response.ContentType = "application/json; charset=utf-8";
                var jsonResponse = JsonConvert.SerializeObject(result, Formatting.Indented);
                await context.Response.WriteAsync(jsonResponse);
            }
            catch (Exception ex)
            {
                context.Response.StatusCode = 500;
                context.Response.ContentType = "application/json; charset=utf-8";
                var errorResponse = JsonConvert.SerializeObject(new { error = ex.Message, stackTrace = ex.StackTrace });
                await context.Response.WriteAsync(errorResponse);
            }
        }

        /// <summary>
        /// 调用 AccountManage 的方法
        /// </summary>
        private static async Task<object> CallAccountManageMethod(string methodName, Dictionary<string, object> requestData)
        {
            try
            {
                var accountManageType = typeof(AccountManage);

                // 查找匹配的方法（忽略大小写）
                var method = accountManageType.GetMethods(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                    .FirstOrDefault(m => m.Name.ToLower() == methodName && m.DeclaringType == accountManageType);

                if (method == null)
                {
                    return new { error = $"Method '{methodName}' not found" };
                }

                // 获取方法参数
                var parameters = method.GetParameters();
                var args = new object[parameters.Length];

                // 填充参数值
                for (int i = 0; i < parameters.Length; i++)
                {
                    var param = parameters[i];
                    var paramName = param.Name ?? "";

                    if (requestData.ContainsKey(paramName))
                    {
                        var value = requestData[paramName];
                        args[i] = ConvertParameter(value, param.ParameterType);
                    }
                    else if (param.HasDefaultValue)
                    {
                        args[i] = param.DefaultValue;
                    }
                    else
                    {
                        // 必需参数但未提供值
                        return new { error = $"Required parameter '{paramName}' is missing" };
                    }
                }

                // 调用方法
                var result = method.Invoke(null, args);

                // 如果是异步方法，等待结果
                if (result is Task task)
                {
                    await task;
                    var resultProperty = task.GetType().GetProperty("Result");
                    if (resultProperty != null)
                    {
                        result = resultProperty.GetValue(task);
                    }
                }

                return result ?? new { message = "Method executed successfully" };
            }
            catch (Exception ex)
            {
                return new { error = ex.Message, stackTrace = ex.StackTrace };
            }
        }

        /// <summary>
        /// 转换参数类型
        /// </summary>
        private static object? ConvertParameter(object? value, Type targetType)
        {
            if (value == null)
                return null;

            // 处理可空类型
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                targetType = targetType.GetGenericArguments()[0];
            }

            // 如果类型已经匹配，直接返回
            if (targetType.IsAssignableFrom(value.GetType()))
                return value;

            // 处理字符串类型
            if (targetType == typeof(string))
                return value.ToString();

            // 处理数值类型
            if (targetType == typeof(int))
                return Convert.ToInt32(value);
            if (targetType == typeof(long))
                return Convert.ToInt64(value);
            if (targetType == typeof(bool))
                return Convert.ToBoolean(value);
            if (targetType == typeof(double))
                return Convert.ToDouble(value);
            if (targetType == typeof(float))
                return Convert.ToSingle(value);
            if (targetType == typeof(decimal))
                return Convert.ToDecimal(value);

            // 处理数组类型
            if (targetType.IsArray)
            {
                var elementType = targetType.GetElementType()!;
                if (value is Newtonsoft.Json.Linq.JArray jArray)
                {
                    var array = Array.CreateInstance(elementType, jArray.Count);
                    for (int i = 0; i < jArray.Count; i++)
                    {
                        var convertedElement = ConvertParameter(jArray[i], elementType);
                        array.SetValue(convertedElement, i);
                    }
                    return array;
                }
            }

            // 尝试使用 Convert.ChangeType
            try
            {
                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                // 如果转换失败，返回原值
                return value;
            }
        }
    }
}
