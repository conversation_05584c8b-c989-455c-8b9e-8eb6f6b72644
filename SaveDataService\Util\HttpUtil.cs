﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.Util
{
    class HttpUtil
    {
        public static JObject getPostData(HttpContext context)
        {
            if (context.Request.Method == "POST")
            {
                var syncIOFeature = context.Features.Get<IHttpBodyControlFeature>();

                if (syncIOFeature != null)
                {
                    syncIOFeature.AllowSynchronousIO = true;
                }
                Stream reqStream = context.Request.Body;
                string text = "";
                try
                {
                    using (StreamReader reader = new StreamReader(reqStream))
                    {
                        text = reader.ReadToEnd();
                    }
                    return JObject.Parse(text);
                }
                catch (Exception e)
                {
                    Console.WriteLine("000001   "+e.Message);
                }
            }
            return null;
        }
    }
}
