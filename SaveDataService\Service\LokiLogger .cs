﻿using Serilog;
using Serilog.Events;
using Serilog.Sinks.Loki;
using System;

namespace SaveDataService.Service
{
    class Program
    {
        public static void demo()
        {

            //// 配置Serilog日志
            //Log.Logger = new LoggerConfiguration()
            //    .MinimumLevel.Debug()
            //    .Enrich.FromLogContext()
            //    .WriteTo.Console()
            //      .WriteTo.Loki(new LokiSinkConfigurations()
            //      {
            //          Credentials = new LokiCredentials("< login here >", "< password here >"),
            //          Url = new Uri("http://192.168.1.99:3100"),
            //          HandleLogLevelAsLabel = true, // adds Serilog.Events.LogEvent.Level as label (default is true)
            //          PropertiesAsLabels = ["userId"], // adds Serilog.Events.LogEvent.Properties as labels (default is empty)
            //          Labels = //global labels, will be added to each loki log message (default is empty)
            //          [
            //              new LokiLabel("app", "demo-app"),
            //              new LokiLabel("environment", "development"),
            //          ]
            //      },
            //      batchSizeLimit: 1000, //The maximum number of events to include in a single batch (default is 1000)
            //      period: TimeSpan.FromMilliseconds(2000), // period between sending batches to loki (default is 2000ms)
            //      queueLimit: 100000, //Maximum number of events to hold in the sink's internal queue, or null for an unbounded queue
            //      httpClient: null) // custom HttpClient instance, (can be used to set proxy, compression etc)
            //      .CreateLogger();
            Serilog.Debugging.SelfLog.Enable(msg => Console.WriteLine($"Serilog内部错误: {msg}"));
            try
            {
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Debug()
                    .Enrich.FromLogContext()
                    .WriteTo.Console()
                    .WriteTo.Loki(new LokiSinkConfigurations()
                    {
                        Credentials = new LokiCredentials("< login here >", "< password here >"),
                        //Url = new Uri("http://180.159.25.196:3100"),
                        Url = new Uri("http://ai2.kingzet.cn:63100"),
                        HandleLogLevelAsLabel = true,
                        PropertiesAsLabels = ["userId"],
                        Labels =
                        [
                            new LokiLabel("app", "demo-app"),
                            new LokiLabel("environment", "development"),
                        ]
                    })
                    .CreateLogger();

                Log.Information("Testing Loki connection...");
                // Force an immediate flush
                //Log.CloseAndFlush();

                Console.WriteLine("Logs should have been sent to Loki. Check Grafana now.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize logger: {ex}");
            }
            //finally
            //{
            //    // Force an immediate flush
            //    Log.CloseAndFlush();
            //}


            try
            {
                Log.Information("Application starting up...");

                // 模拟一些不同类型的日志
                for (int i = 0; i < 10; i++)
                {
                    if (i % 3 == 0)
                    {
                        Log.Error("This is an error message {ErrorNumber}", i);
                    }
                    else if (i % 2 == 0)
                    {
                        Log.Warning("This is a warning message {WarningNumber}", i);
                    }
                    else
                    {
                        Log.Information("This is an info message {InfoNumber}", i);
                    }

                    // 模拟一些异常日志
                    try
                    {
                        if (i == 5)
                        {
                            // throw new InvalidOperationException("Simulated exception for testing");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "An exception occurred while processing iteration {Iteration}", i);
                    }
                }

                Log.Information("Application completed successfully");
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

    }
}