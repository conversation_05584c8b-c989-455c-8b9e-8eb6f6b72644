﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace AISdk.Manage
{
    public class MessgeManager
    {
        // 私有静态实例变量
        private static MessgeManager _instance;
        // 线程安全锁对象
        private static readonly object _lock = new object();
        // 私有构造函数，防止外部实例化
        private MessgeManager()
        {
            // 初始化代码
        }
        // 公共静态属性，用于访问单例实例
        public static MessgeManager Instance
        {
            get
            {
                // 双重检查锁定模式确保线程安全
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new MessgeManager();
                        }
                    }
                }
                return _instance;
            }
        }
        /// <summary>
        /// 切换模型
        /// </summary>
        /// <param name="aiType"></param>
        /// <param name="model"></param>
        public void ChangeAIModel(int aiType,string model="") {
            AIModelManager.Instance.init(aiType);
        }
        public void SendPromptToAI(string prompt) {
            //AIModelManager.Instance.aiSdk.SendTextToAIModel(
            //   prompt, (string text, Boolean bol) =>
            //    {
            //        Console.WriteLine(text);

            //    });
        }
    }
}
