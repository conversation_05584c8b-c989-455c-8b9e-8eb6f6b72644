using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;

namespace SaveDataService
{
    /// <summary>
    /// 简单的 API 测试程序
    /// </summary>
    public class SimpleAPITest
    {
        /// <summary>
        /// 主测试方法
        /// </summary>
        public static void RunTests()
        {
            try
            {
                Console.WriteLine("=== 简单 RESTful API 系统测试 ===");
                Console.WriteLine();

                // 测试 1: 检查 AccountManage 是否继承了 RESTfulAPIBase
                Console.WriteLine("1. 检查继承关系:");
                var accountManageType = typeof(AccountManage);
                var isInherited = typeof(RESTfulAPIBase).IsAssignableFrom(accountManageType);
                Console.WriteLine($"   AccountManage 是否继承 RESTfulAPIBase: {isInherited}");
                Console.WriteLine();

                // 测试 2: 获取 AccountManage 的方法列表
                Console.WriteLine("2. AccountManage 的公共静态方法:");
                var methods = accountManageType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .Where(m => !m.IsSpecialName && 
                               m.DeclaringType == accountManageType && 
                               m.Name != "GetHttpPostFunction")
                    .ToList();

                foreach (var method in methods)
                {
                    Console.WriteLine($"   - {method.Name}");
                    var parameters = method.GetParameters();
                    if (parameters.Length > 0)
                    {
                        Console.WriteLine($"     参数: {string.Join(", ", parameters.Select(p => $"{p.ParameterType.Name} {p.Name}"))}");
                    }
                    Console.WriteLine($"     返回类型: {method.ReturnType.Name}");
                }
                Console.WriteLine();

                // 测试 3: 测试基类方法
                if (isInherited)
                {
                    Console.WriteLine("3. 测试基类 API 生成方法:");
                    try
                    {
                        var apiJson = RESTfulAPIBase.GetHttpPostFunction(accountManageType);
                        Console.WriteLine("   ✓ API 生成成功");
                        
                        // 验证 JSON 格式
                        var apiData = JsonConvert.DeserializeObject(apiJson);
                        Console.WriteLine("   ✓ JSON 格式验证通过");
                        
                        // 显示部分内容
                        var lines = apiJson.Split('\n');
                        Console.WriteLine("   API 描述预览 (前10行):");
                        for (int i = 0; i < Math.Min(10, lines.Length); i++)
                        {
                            Console.WriteLine($"     {lines[i]}");
                        }
                        Console.WriteLine("     ...");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ API 生成失败: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine("3. 跳过基类测试 (继承关系不正确)");
                }
                Console.WriteLine();

                // 测试 4: 测试 RESTfulAPIGen 类
                Console.WriteLine("4. 测试 RESTfulAPIGen 类:");
                try
                {
                    var genApiJson = RESTfulAPIGen.GetHttpPostFunction("AccountManage");
                    Console.WriteLine("   ✓ RESTfulAPIGen.GetHttpPostFunction 成功");
                    
                    var genApiData = JsonConvert.DeserializeObject(genApiJson);
                    Console.WriteLine("   ✓ JSON 格式验证通过");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ RESTfulAPIGen 测试失败: {ex.Message}");
                }
                Console.WriteLine();

                // 测试 5: 测试获取所有 API
                Console.WriteLine("5. 测试获取所有 API:");
                try
                {
                    var allApiJson = RESTfulAPIGen.GetAllHttpPostFunctions();
                    Console.WriteLine("   ✓ GetAllHttpPostFunctions 成功");
                    
                    var allApiData = JsonConvert.DeserializeObject(allApiJson);
                    Console.WriteLine("   ✓ JSON 格式验证通过");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 获取所有 API 失败: {ex.Message}");
                }
                Console.WriteLine();

                // 测试 6: 测试路由系统
                Console.WriteLine("6. 测试路由系统:");
                try
                {
                    var availableClasses = UniversalApiHandler.GetAvailableApiClasses();
                    Console.WriteLine($"   可用的 API 类数量: {availableClasses.Count}");
                    foreach (var className in availableClasses)
                    {
                        Console.WriteLine($"   - {className}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 路由系统测试失败: {ex.Message}");
                }

                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
                Console.WriteLine("✓ 新的 RESTful API 系统基本功能正常");
                Console.WriteLine();
                Console.WriteLine("新的路由规则:");
                Console.WriteLine("  - /api/{类名}/{方法名} (POST)");
                Console.WriteLine("  - /getrestful (GET) - 获取所有 API 描述");
                Console.WriteLine("  - /getrestful/{类名} (GET) - 获取特定类的 API 描述");
                Console.WriteLine();
                Console.WriteLine("示例:");
                Console.WriteLine("  - POST /api/accountmanage/register");
                Console.WriteLine("  - POST /api/accountmanage/loginbyusername");
                Console.WriteLine("  - GET  /getrestful/AccountManage");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试程序运行失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
