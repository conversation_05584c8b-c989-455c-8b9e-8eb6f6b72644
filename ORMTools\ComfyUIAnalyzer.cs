using System;
using System.Collections.Generic;
using System.IO;
using ORMTools;

namespace ORMTools.ComfyUI
{
    /// <summary>
    /// ComfyUI实体类分析器
    /// </summary>
    public class ComfyUIAnalyzer
    {
        /// <summary>
        /// 分析ComfyUI相关实体类并生成CSV文件
        /// </summary>
        public static void AnalyzeComfyUIEntities()
        {
            Console.WriteLine("=== ComfyUI实体类分析器 ===");
            
            try
            {
                // 创建输出目录
                string outputDir = Path.Combine(AppContext.BaseDirectory, "ComfyUI_CSV_Output");
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                Console.WriteLine($"📁 CSV文件输出目录: {outputDir}");
                Console.WriteLine($"📁 完整路径: {Path.GetFullPath(outputDir)}");

                // 定义ComfyUI相关的实体类（模拟）
                var comfyUIEntityTypes = new[]
                {
                    typeof(ComfyUIServer),
                    typeof(ComfyUIWorkflow),
                    typeof(ComfyUITask),
                    typeof(ComfyUIExecutionLog)
                };

                Console.WriteLine($"\n🔍 找到 {comfyUIEntityTypes.Length} 个ComfyUI实体类");
                
                var generatedFiles = new List<string>();
                int successCount = 0;
                int failCount = 0;

                foreach (var entityType in comfyUIEntityTypes)
                {
                    try
                    {
                        Console.WriteLine($"\n📊 正在分析: {entityType.Name}");
                        
                        string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(entityType, outputDir);
                        generatedFiles.Add(csvPath);
                        successCount++;
                        
                        // 显示文件信息
                        if (File.Exists(csvPath))
                        {
                            var fileInfo = new FileInfo(csvPath);
                            var lines = File.ReadAllLines(csvPath);
                            
                            Console.WriteLine($"   ✅ 成功生成: {Path.GetFileName(csvPath)}");
                            Console.WriteLine($"   📏 文件大小: {fileInfo.Length} 字节");
                            Console.WriteLine($"   🔢 属性数量: {lines[0].Split(',').Length}");
                            Console.WriteLine($"   📍 文件路径: {csvPath}");
                            
                            // 显示前几个属性作为预览
                            if (lines.Length >= 3)
                            {
                                var propertyNames = lines[0].Split(',');
                                var propertyTypes = lines[1].Split(',');
                                var propertyComments = lines[2].Split(',');
                                
                                Console.WriteLine("   🔍 属性预览:");
                                for (int i = 0; i < Math.Min(5, propertyNames.Length); i++)
                                {
                                    string name = propertyNames[i].Trim('"');
                                    string type = propertyTypes[i].Trim('"');
                                    string comment = i < propertyComments.Length ? propertyComments[i].Trim('"') : "";
                                    Console.WriteLine($"      • {name} ({type}): {comment}");
                                }
                                if (propertyNames.Length > 5)
                                {
                                    Console.WriteLine($"      ... 还有 {propertyNames.Length - 5} 个属性");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ 分析 {entityType.Name} 失败: {ex.Message}");
                        failCount++;
                    }
                }

                Console.WriteLine("\n" + new string('=', 60));
                Console.WriteLine("📋 分析结果汇总:");
                Console.WriteLine($"   ✅ 成功: {successCount} 个实体类");
                Console.WriteLine($"   ❌ 失败: {failCount} 个实体类");
                Console.WriteLine($"   📁 输出目录: {outputDir}");
                
                Console.WriteLine("\n📄 生成的CSV文件列表:");
                foreach (var filePath in generatedFiles)
                {
                    var fileName = Path.GetFileName(filePath);
                    var fileSize = new FileInfo(filePath).Length;
                    Console.WriteLine($"   📄 {fileName} ({fileSize} 字节)");
                }

                Console.WriteLine("\n📖 CSV文件格式说明：");
                Console.WriteLine("   • 第一行：属性名称");
                Console.WriteLine("   • 第二行：数据类型（数组/List显示为如string[]格式）");
                Console.WriteLine("   • 第三行：注释内容");
                
                Console.WriteLine("\n💡 使用提示：");
                Console.WriteLine("   • 可以使用Excel或其他工具打开CSV文件查看");
                Console.WriteLine("   • 数组和List类型会显示为如int[]、string[]格式");
                Console.WriteLine("   • 可空类型会显示为如int?格式");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 分析过程中发生错误: {ex.Message}");
                Console.WriteLine($"🔍 错误详情: {ex}");
            }
            
            Console.WriteLine("\n=== ComfyUI实体类分析完成 ===");
        }
    }

    // 模拟ComfyUI实体类定义
    /// <summary>
    /// ComfyUI服务器实体类
    /// </summary>
    public class ComfyUIServer
    {
        /// <summary>
        /// 服务器ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName { get; set; } = "";

        /// <summary>
        /// 服务器IP地址
        /// </summary>
        public string IpAddress { get; set; } = "";

        /// <summary>
        /// 服务器端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 服务器状态
        /// </summary>
        public string Status { get; set; } = "";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 支持的模型列表
        /// </summary>
        public List<string> SupportedModels { get; set; } = new List<string>();

        /// <summary>
        /// 配置参数
        /// </summary>
        public string[] ConfigParams { get; set; } = new string[0];
    }

    /// <summary>
    /// ComfyUI工作流实体类
    /// </summary>
    public class ComfyUIWorkflow
    {
        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = "";

        /// <summary>
        /// 工作流名称
        /// </summary>
        public string WorkflowName { get; set; } = "";

        /// <summary>
        /// 工作流JSON内容
        /// </summary>
        public string WorkflowJson { get; set; } = "";

        /// <summary>
        /// 工作流描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 输入参数列表
        /// </summary>
        public List<string> InputParams { get; set; } = new List<string>();

        /// <summary>
        /// 输出文件路径列表
        /// </summary>
        public string[] OutputPaths { get; set; } = new string[0];

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModified { get; set; }
    }

    /// <summary>
    /// ComfyUI任务实体类
    /// </summary>
    public class ComfyUITask
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = "";

        /// <summary>
        /// 工作流ID
        /// </summary>
        public string WorkflowId { get; set; } = "";

        /// <summary>
        /// 服务器ID
        /// </summary>
        public int ServerId { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public string Status { get; set; } = "";

        /// <summary>
        /// 进度百分比
        /// </summary>
        public decimal Progress { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 输入参数JSON
        /// </summary>
        public string InputJson { get; set; } = "";

        /// <summary>
        /// 输出结果JSON
        /// </summary>
        public string OutputJson { get; set; } = "";

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 生成的文件路径列表
        /// </summary>
        public List<string> GeneratedFiles { get; set; } = new List<string>();
    }

    /// <summary>
    /// ComfyUI执行日志实体类
    /// </summary>
    public class ComfyUIExecutionLog
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public long LogId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = "";

        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; } = "";

        /// <summary>
        /// 执行步骤
        /// </summary>
        public int Step { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "";

        /// <summary>
        /// 日志消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 执行时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 节点输入数据
        /// </summary>
        public string? NodeInputs { get; set; }

        /// <summary>
        /// 节点输出数据
        /// </summary>
        public string? NodeOutputs { get; set; }

        /// <summary>
        /// 执行耗时（毫秒）
        /// </summary>
        public long ExecutionTimeMs { get; set; }

        /// <summary>
        /// 额外信息
        /// </summary>
        public string? ExtraInfo { get; set; }
    }
}
