# AccountManageAPI - RESTful API 自动生成器

## 概述

`AccountManageAPI` 是 `AccountManage` 的继承类，通过反射技术自动生成 RESTful API 描述，并提供完整的 HTTP 接口测试功能。

## 主要功能

### 1. 自动 API 发现
- 通过反射获取 `AccountManage` 类的所有公共静态方法
- 自动分析方法参数类型、返回类型
- 生成详细的 API 文档描述

### 2. RESTful API 描述生成
- 生成标准的 JSON 格式 API 描述
- 包含方法名、参数、返回类型、示例等信息
- 支持复杂类型的属性展开

### 3. HTTP 服务器集成
- 集成到 Kestrel HTTP 服务器
- 提供 `/getRESTful` 接口获取 API 描述
- 支持 CORS 跨域访问

### 4. 在线测试界面
- 自动生成的 HTML 测试页面
- 支持在线测试所有 API 接口
- 实时显示请求和响应结果

## 使用方法

### 1. 启动服务器

```bash
dotnet run
```

服务器将在端口 5501 启动，控制台会显示：
```
HTTP 服务器将在端口 5501 启动
访问地址: http://127.0.0.1:5501/
API 测试页面: http://127.0.0.1:5501/api-test.html
RESTful API 描述: http://127.0.0.1:5501/getRESTful
```

### 2. 访问 API 描述

通过 GET 请求访问：
```
http://127.0.0.1:5501/getRESTful
```

返回 JSON 格式的 API 描述，包含所有 AccountManage 方法的详细信息。

### 3. 使用测试界面

访问测试页面：
```
http://127.0.0.1:5501/api-test.html
```

或者直接访问根路径：
```
http://127.0.0.1:5501/
```

测试界面提供：
- 所有 API 方法的详细信息
- 参数类型和描述
- 示例请求和响应
- 在线测试表单
- 实时结果显示

### 4. 调用 API 接口

所有 AccountManage 方法都映射为 POST 接口：

```
POST http://127.0.0.1:5501/api/account/{methodname}
Content-Type: application/json

{
  "parameter1": "value1",
  "parameter2": "value2"
}
```

例如，调用用户注册接口：
```
POST http://127.0.0.1:5501/api/account/registerbyusername
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123",
  "email": "<EMAIL>",
  "mobile": "***********"
}
```

## API 描述格式

生成的 JSON 包含以下结构：

```json
{
  "Name": "AccountManage API",
  "Description": "账号管理相关的 RESTful API 接口",
  "BaseUrl": "http://127.0.0.1:5501/api/account",
  "Version": "1.0",
  "GeneratedAt": "2024-01-01T00:00:00Z",
  "Methods": [
    {
      "MethodName": "RegisterByUsername",
      "Description": "用户注册 - RegisterByUsername",
      "HttpMethod": "POST",
      "Path": "/api/account/registerbyusername",
      "Parameters": [
        {
          "Name": "username",
          "Type": "string",
          "Required": true,
          "Description": "用户名",
          "ExampleValue": "testuser"
        }
      ],
      "ReturnType": {
        "Type": "RegisterResult",
        "Description": "注册操作结果",
        "Properties": [
          {
            "Name": "Success",
            "Type": "bool",
            "Description": "操作是否成功"
          }
        ]
      },
      "ExampleRequest": {
        "username": "testuser",
        "password": "password123"
      },
      "ExampleResponse": {
        "Success": true,
        "Message": "注册成功",
        "AccountId": "********-1234-1234-1234-************",
        "AccessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      }
    }
  ]
}
```

## 支持的方法

自动发现并生成以下 AccountManage 方法的 API：

### 注册功能
- `RegisterByUsername` - 用户名注册
- `RegisterByEmail` - 邮箱注册
- `RegisterByMobile` - 手机号注册

### 登录功能
- `LoginByUsername` - 用户名登录
- `LoginByEmail` - 邮箱登录
- `LoginByMobile` - 手机号登录
- `LoginByToken` - 令牌登录

### 验证码功能
- `SendEmailVerificationCode` - 发送邮箱验证码
- `SendMobileVerificationCode` - 发送手机验证码

### 密码管理
- `ResetPasswordByEmail` - 通过邮箱重置密码
- `ResetPasswordByMobile` - 通过手机重置密码
- `ResetPasswordBySecurityQuestion` - 通过安全问题重置密码
- `ChangePassword` - 修改密码

### 安全设置
- `GetSecurityQuestions` - 获取安全问题
- `SetSecurityQuestions` - 设置安全问题
- `SetTwoFactorAuth` - 设置二次验证

### 账号管理
- `UpdateAccountInfo` - 更新账号信息

## 扩展性

### 添加新的管理类

如果需要为其他管理类（如 `UserManage`、`OrderManage` 等）生成 API，可以：

1. 创建继承类：
```csharp
public class UserManageAPI : UserManage
{
    public static string GetHttpPostFunction()
    {
        // 使用相同的反射逻辑
        return RestfulApiDescriptor.GenerateApiDescription(typeof(UserManage));
    }
}
```

2. 在服务器中添加路由：
```csharp
if (path.StartsWith("/api/user/") && method == "POST")
{
    await HandleUserApi(context);
    return;
}
```

### 自定义 API 描述

可以通过修改 `RestfulApiDescriptor` 类来：
- 添加更多参数类型支持
- 自定义示例值生成
- 添加 API 版本控制
- 支持更多 HTTP 方法

## 注意事项

1. **安全性**: 当前实现没有身份验证，生产环境需要添加适当的安全措施
2. **错误处理**: API 调用失败时会返回详细的错误信息，包括堆栈跟踪
3. **类型转换**: 自动处理基本类型转换，复杂类型可能需要手动处理
4. **性能**: 反射操作有一定性能开销，可考虑添加缓存机制

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改 `Program.cs` 中的 `httpPort` 值
   - 或者停止占用端口的其他程序

2. **API 方法未显示**
   - 确保方法是 `public static`
   - 检查方法是否在 `AccountManage` 类中声明

3. **参数类型转换失败**
   - 检查请求 JSON 格式是否正确
   - 确认参数类型匹配

4. **CORS 错误**
   - 服务器已配置允许跨域访问
   - 如果仍有问题，检查浏览器控制台错误信息

## 技术实现

- **反射技术**: 使用 `System.Reflection` 获取方法信息
- **JSON 序列化**: 使用 `Newtonsoft.Json` 处理 JSON 数据
- **HTTP 服务器**: 基于 ASP.NET Core Kestrel
- **前端界面**: 纯 HTML/CSS/JavaScript，无需额外框架
