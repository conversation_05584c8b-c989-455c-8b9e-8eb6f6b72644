using System;
using System.Threading.Tasks;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI详细测试类 - 展示完整的日志记录功能
    /// </summary>
    public static class ComfyUIDetailedTest
    {
        /// <summary>
        /// 运行详细测试
        /// </summary>
        public static async Task RunDetailedTests()
        {
            var logger = ComfyUILogger.Instance;
            var manage = ComfyUIManage.Instance;

            Console.WriteLine("🎯 开始ComfyUI详细日志测试");
            Console.WriteLine();

            try
            {
                // 1. 添加测试服务器
                logger.LogInfo("添加测试服务器...");
                var serverId = manage.AddServer(
                    "本地ComfyUI测试服务器",
                    "127.0.0.1",
                    8888,
                    5,
                    "用于详细日志测试的本地ComfyUI服务器"
                );
                logger.LogInfo($"服务器添加成功: {serverId}");

                // 2. 测试服务器连接
                logger.LogInfo("测试服务器连接...");
                var isOnline = await manage.TestServerConnection(serverId);
                if (isOnline)
                {
                    logger.LogInfo("✅ 服务器连接成功");
                }
                else
                {
                    logger.LogError("❌ 服务器连接失败");
                    return;
                }

                // 3. 创建测试工作流
                logger.LogInfo("创建测试工作流...");
                var workflowJson = CreateDetailedTestWorkflow();
                var workflowId = manage.AddWorkflow(
                    "详细日志测试工作流",
                    workflowJson,
                    "text2img",
                    "用于测试详细日志记录的工作流",
                    "系统测试"
                );
                logger.LogInfo($"工作流创建成功: {workflowId}");

                // 4. 提交工作流并启动详细监控
                logger.LogInfo("提交工作流并启动详细监控...");
                var (taskId, result) = await manage.SubmitWorkflowToServerWithMonitoring(serverId, workflowJson);
                
                if (result.Contains("prompt_id"))
                {
                    logger.LogInfo($"工作流提交成功，任务ID: {taskId}");
                    logger.LogInfo("监控已启动，请观察详细的执行日志...");
                    
                    // 等待足够长的时间让工作流完成
                    logger.LogInfo("等待工作流执行完成（最多60秒）...");
                    await Task.Delay(60000);
                    
                    logger.LogInfo("测试完成！");
                }
                else
                {
                    logger.LogError($"工作流提交失败: {result}");
                }

                // 5. 显示监控统计
                var activeMonitors = ComfyUIMonitor.Instance.GetActiveMonitorCount();
                logger.LogInfo($"当前活动监控数量: {activeMonitors}");

            }
            catch (Exception ex)
            {
                logger.LogError($"详细测试过程中发生错误: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("🏁 ComfyUI详细日志测试结束");
        }

        /// <summary>
        /// 创建详细的测试工作流
        /// </summary>
        private static string CreateDetailedTestWorkflow()
        {
            // 创建一个包含多个节点的工作流，便于观察详细的执行过程
            var workflow = new Dictionary<string, object>
            {
                // 检查点加载器
                ["4"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CheckpointLoaderSimple",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["ckpt_name"] = "v1-5-pruned-emaonly-fp16.safetensors"
                    }
                },
                // 空白潜在图像
                ["5"] = new Dictionary<string, object>
                {
                    ["class_type"] = "EmptyLatentImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["width"] = 512,
                        ["height"] = 512,
                        ["batch_size"] = 1
                    }
                },
                // 正面提示词编码
                ["6"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "a beautiful landscape with mountains and lakes, detailed, high quality, masterpiece"
                    }
                },
                // 负面提示词编码
                ["7"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "blurry, low quality, bad anatomy, watermark, text"
                    }
                },
                // KSampler采样器
                ["3"] = new Dictionary<string, object>
                {
                    ["class_type"] = "KSampler",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["model"] = new object[] { "4", 0 },
                        ["positive"] = new object[] { "6", 0 },
                        ["negative"] = new object[] { "7", 0 },
                        ["latent_image"] = new object[] { "5", 0 },
                        ["seed"] = 123456789,
                        ["steps"] = 25,
                        ["cfg"] = 7.5,
                        ["sampler_name"] = "euler",
                        ["scheduler"] = "normal",
                        ["denoise"] = 1.0
                    }
                },
                // VAE解码器
                ["8"] = new Dictionary<string, object>
                {
                    ["class_type"] = "VAEDecode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["samples"] = new object[] { "3", 0 },
                        ["vae"] = new object[] { "4", 2 }
                    }
                },
                // 图像保存
                ["9"] = new Dictionary<string, object>
                {
                    ["class_type"] = "SaveImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["images"] = new object[] { "8", 0 },
                        ["filename_prefix"] = "ComfyUI_DetailedTest"
                    }
                },
                // 预览图像
                ["10"] = new Dictionary<string, object>
                {
                    ["class_type"] = "PreviewImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["images"] = new object[] { "8", 0 }
                    }
                }
            };

            return Newtonsoft.Json.JsonConvert.SerializeObject(workflow, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// 运行简化的详细测试（用于快速验证）
        /// </summary>
        public static async Task RunQuickDetailedTest()
        {
            var logger = ComfyUILogger.Instance;
            
            logger.LogInfo("开始快速详细测试...");
            
            // 模拟工作流执行过程
            var taskId = Guid.NewGuid().ToString();
            var workflowJson = CreateDetailedTestWorkflow();
            
            // 记录工作流开始
            logger.LogWorkflowStart(taskId, workflowJson, "test-server");
            
            // 模拟提交成功
            var mockResponse = $"{{\"prompt_id\": \"{Guid.NewGuid()}\", \"number\": 1}}";
            logger.LogWorkflowSubmission(taskId, true, mockResponse);
            
            // 模拟节点执行
            var nodes = new[]
            {
                ("4", "CheckpointLoaderSimple", "加载检查点模型"),
                ("5", "EmptyLatentImage", "创建空白潜在图像"),
                ("6", "CLIPTextEncode", "编码正面提示词"),
                ("7", "CLIPTextEncode", "编码负面提示词"),
                ("3", "KSampler", "执行采样生成"),
                ("8", "VAEDecode", "VAE解码"),
                ("9", "SaveImage", "保存图像"),
                ("10", "PreviewImage", "预览图像")
            };
            
            foreach (var (nodeId, nodeType, description) in nodes)
            {
                // 节点开始
                var inputs = new Newtonsoft.Json.Linq.JObject();
                inputs["description"] = description;
                logger.LogNodeStart(taskId, nodeId, nodeType, nodeType, inputs);
                
                // 模拟执行时间
                await Task.Delay(1000);
                
                // 节点进度
                logger.LogNodeProgress(taskId, nodeId, nodeType, 50, "执行中");
                await Task.Delay(500);
                
                // 节点完成
                var outputs = new Newtonsoft.Json.Linq.JObject();
                outputs["result"] = $"{nodeType} 执行完成";
                logger.LogNodeComplete(taskId, nodeId, nodeType, outputs, TimeSpan.FromSeconds(1.5));
            }
            
            // 工作流完成
            var outputFiles = new[] { "ComfyUI_DetailedTest_00001_.png" };
            logger.LogWorkflowComplete(taskId, true, TimeSpan.FromSeconds(12), outputFiles.ToList());
            
            logger.LogInfo("快速详细测试完成！");
        }
    }
}
