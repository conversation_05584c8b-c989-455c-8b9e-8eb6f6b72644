using System;
using System.Collections.Generic;
using System.Linq;
using ExcelToData;
using SaveDataService.Manage;

namespace SaveDataService.Test
{
    /// <summary>
    /// ComfyUI功能测试类
    /// </summary>
    public class ComfyUITest
    {
        /// <summary>
        /// 测试ComfyUI基本功能
        /// </summary>
        public static void TestComfyUIBasicFunctions()
        {
            Console.WriteLine("=== ComfyUI基本功能测试开始 ===");
            
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                
                // 1. 测试添加服务器
                Console.WriteLine("1. 测试添加服务器...");
                var serverId = comfyUIManage.AddServer("测试服务器", "http://localhost", 8188, 5, "测试用ComfyUI服务器");
                Console.WriteLine($"   添加服务器成功，ID: {serverId}");

                // 2. 测试获取服务器列表
                Console.WriteLine("2. 测试获取服务器列表...");
                var servers = comfyUIManage.GetAllServers();
                Console.WriteLine($"   获取到 {servers.Count} 个服务器");

                // 3. 测试添加工作流
                Console.WriteLine("3. 测试添加工作流...");
                var workflowId = comfyUIManage.AddWorkflow("测试工作流", "{\"test\": \"workflow\"}", "image_generation", "测试工作流描述", "测试用户");
                Console.WriteLine($"   添加工作流成功，ID: {workflowId}");

                // 4. 测试获取工作流列表
                Console.WriteLine("4. 测试获取工作流列表...");
                var workflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"   获取到 {workflows.Count} 个工作流");

                // 5. 测试创建任务
                Console.WriteLine("5. 测试创建任务...");
                var taskId = comfyUIManage.CreateTask(workflowId, "测试任务", "测试用户");
                Console.WriteLine($"   创建任务成功，ID: {taskId}");
                
                // 6. 测试添加任务日志
                Console.WriteLine("6. 测试添加任务日志...");
                var logResult = comfyUIManage.AddTaskLog(taskId, 1, "任务开始执行", "node_1", "开始节点", "{\"status\": \"started\"}");
                Console.WriteLine($"   添加日志结果: {logResult}");
                
                // 7. 测试获取任务日志
                Console.WriteLine("7. 测试获取任务日志...");
                var logs = comfyUIManage.GetTaskLogs(taskId);
                Console.WriteLine($"   获取日志: {logs}");
                
                // 8. 测试添加任务文件
                Console.WriteLine("8. 测试添加任务文件...");
                var fileId = comfyUIManage.AddTaskFile(taskId, 1, "output.png", "/path/to/output.png", 1024, "abc123", "输出图片");
                Console.WriteLine($"   添加文件成功，ID: {fileId}");
                
                // 9. 测试获取任务文件
                Console.WriteLine("9. 测试获取任务文件...");
                var files = comfyUIManage.GetTaskFiles(taskId);
                Console.WriteLine($"   获取文件: {files}");
                
                // 10. 测试更新任务状态
                Console.WriteLine("10. 测试更新任务状态...");
                var updateResult = comfyUIManage.UpdateTaskStatus(taskId, 2, 100, "任务完成");
                Console.WriteLine($"   更新任务状态结果: {updateResult}");
                
                // 11. 测试获取任务列表
                Console.WriteLine("11. 测试获取任务列表...");
                var tasks = comfyUIManage.GetTasks();
                Console.WriteLine($"   获取到 {tasks.Count} 个任务");
                
                // 12. 测试获取任务执行报告
                Console.WriteLine("12. 测试获取任务执行报告...");
                var comfyUIData = ComfyUIData.Instance;
                var report = comfyUIData.GetTaskExecutionReport(taskId);
                if (report != null)
                {
                    Console.WriteLine($"   任务报告 - 任务名: {report.TaskName}, 状态: {report.Status}, 进度: {report.Progress}%");
                    Console.WriteLine($"   日志数量: {report.Logs.Count}, 文件数量: {report.Files.Count}");
                }
                else
                {
                    Console.WriteLine("   获取任务报告失败");
                }
                
                Console.WriteLine("=== ComfyUI基本功能测试完成 ===");
                Console.WriteLine("✅ 所有测试通过！实体类合并成功，功能正常！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
        
        /// <summary>
        /// 测试JSON存储功能
        /// </summary>
        public static void TestJsonStorage()
        {
            Console.WriteLine("\n=== JSON存储功能测试开始 ===");
            
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                
                // 创建测试任务
                var serverId = comfyUIManage.AddServer("JSON测试服务器", "http://localhost", 8188, 5, "JSON测试");
                var workflowId = comfyUIManage.AddWorkflow("JSON测试工作流", "{\"test\": \"json\"}", "test", "JSON测试", "测试");
                var taskId = comfyUIManage.CreateTask(workflowId, "JSON测试任务", "测试用户");
                
                // 添加多个日志
                comfyUIManage.AddTaskLog(taskId, 0, "调试信息", "node_debug", "调试节点", "{\"debug\": true}");
                comfyUIManage.AddTaskLog(taskId, 1, "信息日志", "node_info", "信息节点", "{\"info\": \"processing\"}");
                comfyUIManage.AddTaskLog(taskId, 2, "警告信息", "node_warn", "警告节点", "{\"warning\": \"memory low\"}");
                comfyUIManage.AddTaskLog(taskId, 3, "错误信息", "node_error", "错误节点", "{\"error\": \"failed\"}");
                
                // 添加多个文件
                comfyUIManage.AddTaskFile(taskId, 0, "input.png", "/input/input.png", 2048, "input123", "输入图片");
                comfyUIManage.AddTaskFile(taskId, 1, "output1.png", "/output/output1.png", 4096, "output123", "输出图片1");
                comfyUIManage.AddTaskFile(taskId, 1, "output2.png", "/output/output2.png", 3072, "output456", "输出图片2");
                comfyUIManage.AddTaskFile(taskId, 2, "video.mp4", "/output/video.mp4", 10240, "video789", "输出视频");
                
                // 获取并验证JSON数据
                var logs = comfyUIManage.GetTaskLogs(taskId);
                var files = comfyUIManage.GetTaskFiles(taskId);
                var inputFiles = comfyUIManage.GetTaskFiles(taskId, 0);
                var outputFiles = comfyUIManage.GetTaskFiles(taskId, 1);
                
                Console.WriteLine($"日志JSON: {logs}");
                Console.WriteLine($"所有文件JSON: {files}");
                Console.WriteLine($"输入文件JSON: {inputFiles}");
                Console.WriteLine($"输出文件JSON: {outputFiles}");
                
                // 验证JSON格式
                if (logs.StartsWith("[") && logs.EndsWith("]"))
                {
                    Console.WriteLine("✅ 日志JSON格式正确");
                }
                else
                {
                    Console.WriteLine("❌ 日志JSON格式错误");
                }
                
                if (files.StartsWith("[") && files.EndsWith("]"))
                {
                    Console.WriteLine("✅ 文件JSON格式正确");
                }
                else
                {
                    Console.WriteLine("❌ 文件JSON格式错误");
                }
                
                Console.WriteLine("=== JSON存储功能测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON测试失败: {ex.Message}");
            }
        }
    }
}
