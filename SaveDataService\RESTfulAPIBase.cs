using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Newtonsoft.Json;
using static SaveDataService.RestfulApiDescriptor;

namespace SaveDataService
{
    /// <summary>
    /// RESTful API 基类 - 为继承类提供自动 RESTful API 生成功能
    /// </summary>
    public abstract class RESTfulAPIBase
    {
        /// <summary>
        /// 获取当前类的 RESTful API 描述
        /// </summary>
        /// <returns>RESTful API 的 JSON 描述</returns>
        public static string GetHttpPostFunction<T>() where T : RESTfulAPIBase
        {
            return GetHttpPostFunction(typeof(T));
        }

        /// <summary>
        /// 获取指定类型的 RESTful API 描述
        /// </summary>
        /// <param name="targetType">目标类型</param>
        /// <returns>RESTful API 的 JSON 描述</returns>
        public static string GetHttpPostFunction(Type targetType)
        {
            try
            {
                // 确保目标类型继承自 RESTfulAPIBase
                if (!typeof(RESTfulAPIBase).IsAssignableFrom(targetType))
                {
                    return JsonConvert.SerializeObject(new { error = $"类型 {targetType.Name} 必须继承自 RESTfulAPIBase" }, Formatting.Indented);
                }

                var className = targetType.Name;
                var apiCollection = new ApiCollectionInfo
                {
                    Name = $"{className} API",
                    Description = $"{className} 相关的 RESTful API 接口",
                    BaseUrl = $"http://127.0.0.1:7778/api/{className.ToLower()}",
                    Version = "1.0",
                    GeneratedAt = DateTime.UtcNow
                };

                // 获取所有公共静态方法
                var methods = targetType.GetMethods(BindingFlags.Public | BindingFlags.Static)
                    .Where(m => !m.IsSpecialName && // 排除属性的 get/set 方法
                               m.DeclaringType == targetType && // 只获取当前类声明的方法
                               m.Name != "GetHttpPostFunction") // 排除当前方法
                    .ToList();

                foreach (var method in methods)
                {
                    var apiMethod = CreateApiMethodInfo(method, className);
                    apiCollection.Methods.Add(apiMethod);
                }

                // 序列化为 JSON
                var jsonSettings = new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented,
                    NullValueHandling = NullValueHandling.Ignore
                };

                return JsonConvert.SerializeObject(apiCollection, jsonSettings);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成 API 描述时发生错误: {ex.Message}");
                return JsonConvert.SerializeObject(new { error = ex.Message }, Formatting.Indented);
            }
        }

        /// <summary>
        /// 创建 API 方法信息
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="className">类名</param>
        /// <returns>API 方法信息</returns>
        private static ApiMethodInfo CreateApiMethodInfo(MethodInfo method, string className)
        {
            var apiMethod = new ApiMethodInfo
            {
                MethodName = method.Name,
                Description = GetMethodDescription(method, className),
                HttpMethod = "POST",
                Path = $"/api/{className.ToLower()}/{method.Name.ToLower()}"
            };

            // 获取参数信息
            var parameters = method.GetParameters();
            foreach (var param in parameters)
            {
                var apiParam = new ApiParameterInfo
                {
                    Name = param.Name ?? "unknown",
                    Type = GetFriendlyTypeName(param.ParameterType),
                    Required = !param.HasDefaultValue,
                    Description = GetParameterDescription(param),
                    DefaultValue = param.HasDefaultValue ? param.DefaultValue : null,
                    ExampleValue = GenerateExampleValue(param.ParameterType, param.Name ?? "")
                };

                apiMethod.Parameters.Add(apiParam);
            }

            // 获取返回类型信息
            apiMethod.ReturnType = CreateReturnTypeInfo(method.ReturnType);

            // 生成示例请求和响应
            apiMethod.ExampleRequest = GenerateExampleRequest(method.Name, apiMethod.Parameters);
            apiMethod.ExampleResponse = GenerateExampleResponse(method.ReturnType);

            return apiMethod;
        }

        /// <summary>
        /// 获取方法描述
        /// </summary>
        /// <param name="method">方法信息</param>
        /// <param name="className">类名</param>
        /// <returns>方法描述</returns>
        private static string GetMethodDescription(MethodInfo method, string className)
        {
            var methodName = method.Name;
            
            // 根据类名和方法名生成描述
            if (className.ToLower().Contains("account"))
            {
                if (methodName.StartsWith("Register"))
                    return $"用户注册 - {methodName}";
                if (methodName.StartsWith("Login"))
                    return $"用户登录 - {methodName}";
                if (methodName.StartsWith("Send") && methodName.Contains("Code"))
                    return $"发送验证码 - {methodName}";
                if (methodName.StartsWith("Reset") && methodName.Contains("Password"))
                    return $"重置密码 - {methodName}";
                if (methodName.StartsWith("Change") && methodName.Contains("Password"))
                    return $"修改密码 - {methodName}";
                if (methodName.StartsWith("Get") && methodName.Contains("Security"))
                    return $"获取安全问题 - {methodName}";
                if (methodName.StartsWith("Set") && methodName.Contains("Security"))
                    return $"设置安全问题 - {methodName}";
                if (methodName.StartsWith("Set") && methodName.Contains("TwoFactor"))
                    return $"设置二次验证 - {methodName}";
                if (methodName.StartsWith("Update") && methodName.Contains("Account"))
                    return $"更新账号信息 - {methodName}";
            }

            return $"{className} 方法 - {methodName}";
        }

        /// <summary>
        /// 获取参数描述
        /// </summary>
        /// <param name="param">参数信息</param>
        /// <returns>参数描述</returns>
        private static string GetParameterDescription(ParameterInfo param)
        {
            var paramName = param.Name?.ToLower() ?? "";
            
            if (paramName.Contains("username"))
                return "用户名";
            if (paramName.Contains("password"))
                return "密码";
            if (paramName.Contains("email"))
                return "邮箱地址";
            if (paramName.Contains("mobile"))
                return "手机号码";
            if (paramName.Contains("code"))
                return "验证码";
            if (paramName.Contains("accountid"))
                return "账号ID";
            if (paramName.Contains("clientip"))
                return "客户端IP地址";
            if (paramName.Contains("token"))
                return "访问令牌";
            if (paramName.Contains("nickname"))
                return "昵称";
            if (paramName.Contains("avatar"))
                return "头像";
            if (paramName.Contains("realname"))
                return "真实姓名";
            if (paramName.Contains("enable"))
                return "是否启用";
            if (paramName.Contains("question"))
                return "安全问题";
            if (paramName.Contains("answer"))
                return "安全答案";

            return $"参数 - {param.Name}";
        }

        /// <summary>
        /// 创建返回类型信息
        /// </summary>
        /// <param name="returnType">返回类型</param>
        /// <returns>返回类型信息</returns>
        private static ApiReturnInfo CreateReturnTypeInfo(Type returnType)
        {
            var returnInfo = new ApiReturnInfo
            {
                Type = GetFriendlyTypeName(returnType),
                Description = GetReturnTypeDescription(returnType)
            };

            // 如果是复杂类型，获取其属性
            if (returnType.IsClass && returnType != typeof(string) && !returnType.IsPrimitive)
            {
                var properties = returnType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (var prop in properties)
                {
                    var propInfo = new ApiPropertyInfo
                    {
                        Name = prop.Name,
                        Type = GetFriendlyTypeName(prop.PropertyType),
                        Description = GetPropertyDescription(prop)
                    };
                    returnInfo.Properties.Add(propInfo);
                }
            }

            return returnInfo;
        }

        /// <summary>
        /// 获取返回类型描述
        /// </summary>
        /// <param name="returnType">返回类型</param>
        /// <returns>返回类型描述</returns>
        private static string GetReturnTypeDescription(Type returnType)
        {
            var typeName = returnType.Name;
            
            if (typeName.Contains("RegisterResult"))
                return "注册操作结果";
            if (typeName.Contains("LoginResult"))
                return "登录操作结果";
            if (typeName.Contains("SendCodeResult"))
                return "发送验证码结果";
            if (typeName.Contains("ResetPasswordResult"))
                return "重置密码结果";
            if (typeName.Contains("ChangePasswordResult"))
                return "修改密码结果";
            if (typeName.Contains("GetSecurityQuestionsResult"))
                return "获取安全问题结果";
            if (typeName.Contains("SetSecurityResult"))
                return "设置安全问题结果";
            if (typeName.Contains("TwoFactorResult"))
                return "二次验证设置结果";
            if (typeName.Contains("UpdateAccountResult"))
                return "更新账号信息结果";

            return $"返回类型 - {typeName}";
        }

        /// <summary>
        /// 获取属性描述
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>属性描述</returns>
        private static string GetPropertyDescription(PropertyInfo property)
        {
            var propName = property.Name.ToLower();
            
            if (propName.Contains("success"))
                return "操作是否成功";
            if (propName.Contains("message"))
                return "操作结果消息";
            if (propName.Contains("accountid"))
                return "账号ID";
            if (propName.Contains("accesstoken"))
                return "访问令牌";
            if (propName.Contains("account"))
                return "账号信息";
            if (propName.Contains("securityquestions"))
                return "安全问题列表";

            return $"属性 - {property.Name}";
        }

        /// <summary>
        /// 生成示例请求
        /// </summary>
        /// <param name="methodName">方法名</param>
        /// <param name="parameters">参数列表</param>
        /// <returns>示例请求对象</returns>
        private static object GenerateExampleRequest(string methodName, List<ApiParameterInfo> parameters)
        {
            var parametersObj = new Dictionary<string, object?>();

            foreach (var param in parameters)
            {
                parametersObj[param.Name] = param.ExampleValue;
            }

            return new
            {
                method = methodName,
                parameters = parametersObj
            };
        }

        /// <summary>
        /// 生成示例响应
        /// </summary>
        /// <param name="returnType">返回类型</param>
        /// <returns>示例响应对象</returns>
        private static object? GenerateExampleResponse(Type returnType)
        {
            if (returnType == typeof(void))
                return null;

            // 为常见的结果类型生成示例
            var typeName = returnType.Name;
            
            if (typeName.Contains("RegisterResult"))
            {
                return new
                {
                    Success = true,
                    Message = "注册成功",
                    AccountId = "********-1234-1234-1234-********9012",
                    AccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                };
            }
            
            if (typeName.Contains("LoginResult"))
            {
                return new
                {
                    Success = true,
                    Message = "登录成功",
                    Account = new { id = "12345", usrname = "testuser", email = "<EMAIL>" },
                    AccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                };
            }

            if (typeName.Contains("Result"))
            {
                return new
                {
                    Success = true,
                    Message = "操作成功"
                };
            }

            return GenerateExampleValue(returnType);
        }
    }
}
