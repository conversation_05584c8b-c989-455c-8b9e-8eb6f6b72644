﻿using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.GameService.Tools
{
    internal class ListCounterObj
    {
        [JsonIgnore]
        public ulong ListTimeCount = 0;
    }
    internal class ListTimeCounter<T> where T : ListCounterObj
    {
        public delegate void listCallBackDelegate(T text);
        private listCallBackDelegate _cb;
        private uint _timeInterval = 60;
        private float updataInterval = 1;
        private ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>> _list;
        /// <summary>
        /// 创建一个新的队列管理
        /// </summary>
        public ListTimeCounter()
        {
            timer = 0;
            _list = new ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>>();
        }
        /// <summary>
        /// 通过现有队列创建一个队列管理
        /// </summary>
        /// <param name="list"></param>
        public ListTimeCounter(ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>> list)
        {
            timer = 0;
            _list = list;
        }
        /// <summary>
        /// 通过现有队列创建一个队列管理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="cb">回调方法</param>
        public ListTimeCounter(ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>> list, listCallBackDelegate cb)
        {
            timer = 0;
            _list = list;
            _cb = cb;
        }
        /// <summary>
        /// 通过现有队列创建一个队列管理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="timeInterval">队列间隔</param>
        public ListTimeCounter(ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>> list, uint timeInterval)
        {
            timer = 0;
            _list = list;
            _timeInterval = timeInterval;
        }
        /// <summary>
        /// 通过现有队列创建一个队列管理
        /// </summary>
        /// <param name="list"></param>
        /// <param name="timeInterval">队列间隔</param>
        /// <param name="cb">回调方法</param>
        public ListTimeCounter(ConcurrentDictionary<ulong, ConcurrentDictionary<string, T>> list, uint timeInterval, listCallBackDelegate cb)
        {
            timer = 0;
            _list = list;
            _timeInterval = timeInterval;
            _cb = cb;
        }
        public void setCallBack(listCallBackDelegate cb)
        {
            _cb = cb;
        }
        public void dispose()
        {
            timer = 0;
            _list = null;
            _cb = null;
        }
        /// <summary>
        /// 添加一个对象
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="key"></param>
        public void addObj(T obj, string key)
        {
            ulong timeMin = (ulong)(Math.Floor((double)(obj.ListTimeCount / _timeInterval)));
            if (!_list.ContainsKey(timeMin))
            {
                _list[timeMin] = new ConcurrentDictionary<string, T>();
            }
            _list[timeMin][key] = obj;
        }
        /// <summary>
        /// 移除一个对象
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="key"></param>
        public void remove(T obj, string key)
        {
            ulong timeMin = (ulong)(Math.Floor((double)(obj.ListTimeCount / _timeInterval)));
            if (!_list.ContainsKey(timeMin))
            {
                return;
            }
            if (!_list[timeMin].ContainsKey(key))
            {
                return;
            }
            T outObj = null;
            _list[timeMin].Remove(key, out outObj);
        }
        /// <summary>
        /// 通过时间和key获取单一对象（精确时间 而不是队列时间）
        /// </summary>
        /// <param name="timeMin"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public T get(string key)
        {
            foreach (var item in _list)
            {
               if(item.Value.ContainsKey(key))
                {
                    return item.Value[key];
                }
            }

            return null;
        }
        /// <summary>
        /// 通过时间和key获取单一对象（精确时间 而不是队列时间）
        /// </summary>
        /// <param name="timeMin"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public T get(ulong timeMin, string key)
        {
            ulong timeMinList = (ulong)(Math.Floor((double)(timeMin / _timeInterval)));
            if (!_list.ContainsKey(timeMinList))
            {
                return null;
            }
            if (!_list[timeMinList].ContainsKey(key))
            {
                return null;
            }

            return _list[timeMinList][key];
        }
        /// <summary>
        /// 通过时间获取单一队列（精确时间 而不是队列时间）
        /// </summary>
        /// <param name="timeMin"></param>
        /// <returns></returns>
        public ConcurrentDictionary<string, T> getList(ulong timeMin)
        {
            ulong timeMinList = (ulong)(Math.Floor((double)(timeMin / _timeInterval)));
            if (!_list.ContainsKey(timeMinList))
            {
                return null;
            }
            return _list[timeMinList];
        }
        private float timer = 0;
        /// <summary>
        /// update逻辑
        /// </summary>
        /// <param name="d">帧时间 单位秒</param>
        public void Update(float d)
        {
            if (_list == null || _list.Count == 0)
            {
                return;
            }
            timer += d;
            if (timer < updataInterval)
            {
                return;
            }
            timer -= updataInterval;
            ulong nowTime = (ulong)TimeTools.getSTime();

            ulong timeMin = (ulong)(Math.Floor((double)(nowTime / _timeInterval)));
            var removeEndList = new Dictionary<string, T>();

            if (_list.ContainsKey(timeMin))
            {
                var listEnd = _list[timeMin];
                //筛选当前对立到期的
                foreach (var item in listEnd)
                {
                    if (item.Value != null)
                    {
                        if (nowTime >= item.Value.ListTimeCount)
                        {
                            removeEndList.Add(item.Key, item.Value);
                        }
                    }
                }
                T outEndInfo;
                foreach (var item in removeEndList)
                {
                    listEnd.Remove(item.Key, out outEndInfo);
                }
                if (listEnd.Count == 0)
                {
                    _list.Remove(timeMin, out listEnd);
                }
            }
            //如果有上一个队列还没处理完的 则一起处理
            if (_list.ContainsKey(timeMin - 1))
            {
                ConcurrentDictionary<string, T> Lastlist;
                _list.Remove(timeMin - 1, out Lastlist);
                if (Lastlist != null)
                {
                    foreach (var item in Lastlist)
                    {
                        if (!removeEndList.ContainsKey(item.Key))
                        {
                            removeEndList[item.Key] = item.Value;

                        }
                    }
                }
            }
            //处理需要处理的
            foreach (var item in removeEndList)
            {
                if (item.Value == null)
                {
                    continue;
                }
                if (_cb != null)
                {
                    _cb(item.Value);
                }
            }

        }
    }
}
