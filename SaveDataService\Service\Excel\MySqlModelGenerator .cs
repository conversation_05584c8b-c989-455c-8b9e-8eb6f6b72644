﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.IO;
using Microsoft.Extensions.Logging;
using MySqlConnector;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

public class DatabaseSchemaGenerator
{
    private readonly string _connectionString;
    private readonly ILogger _logger;

    public DatabaseSchemaGenerator(string connectionString)
    {
        _connectionString = connectionString;
        _logger = LoggerFactory.Create(builder => builder.AddConsole())
            .CreateLogger<DatabaseSchemaGenerator>();
    }

    public void Generate(bool generateExcel = false)
    {
        try
        {
            using var connection = new MySqlConnection(_connectionString);
            connection.Open();

            _logger.LogInformation("✅ 成功连接到数据库");

            // 获取所有表名
            var tables = GetTables(connection);
            _logger.LogInformation($"🔍 发现 {tables.Count} 个数据库表");

            if (tables.Count == 0)
            {
                _logger.LogWarning("⚠️ 没有发现任何表，请检查数据库连接和权限");
                return;
            }

            // 创建Models文件夹
            Directory.CreateDirectory("Models");

            // 创建Excel文件夹（如果需要）
            if (generateExcel)
            {
                Directory.CreateDirectory("ExcelExports");
            }

            // 生成DbContext
            GenerateDbContext(tables);

            // 为每个表生成实体类
            foreach (var table in tables)
            {
                var columns = GetTableColumns(connection, table);
                GenerateEntityClass(table, columns);

                // 如果需要生成Excel
                if (generateExcel)
                {
                    ExportTableToExcel(connection, table, columns);
                }
            }

            _logger.LogInformation("🎉 所有类和上下文已成功生成");
            if (generateExcel)
            {
                _logger.LogInformation("📊 Excel导出文件已生成在ExcelExports文件夹中");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"❌ 发生错误: {ex.Message}");
            if (ex.InnerException != null)
            {
                _logger.LogError($"➡️ 内部错误: {ex.InnerException.Message}");
            }
        }
    }

    private List<string> GetTables(MySqlConnection connection)
    {
        var tables = new List<string>();
        var command = new MySqlCommand(
            "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES " +
            "WHERE TABLE_SCHEMA = DATABASE() AND TABLE_TYPE = 'BASE TABLE'",
            connection);

        using var reader = command.ExecuteReader();
        while (reader.Read())
        {
            tables.Add(reader.GetString(0));
        }

        return tables;
    }

    private List<ColumnInfo> GetTableColumns(MySqlConnection connection, string tableName)
    {
        var columns = new List<ColumnInfo>();
        var command = new MySqlCommand(
            "SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, " +
            "IS_NULLABLE, COLUMN_KEY, EXTRA, COLUMN_COMMENT " +
            "FROM INFORMATION_SCHEMA.COLUMNS " +
            "WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = @tableName " +
            "ORDER BY ORDINAL_POSITION",
            connection);

        command.Parameters.AddWithValue("@tableName", tableName);

        using var reader = command.ExecuteReader();
        while (reader.Read())
        {
            columns.Add(new ColumnInfo
            {
                Name = reader.GetString(0),
                DataType = reader.GetString(1),
                CharacterMaxLength = !reader.IsDBNull(2) ? reader.GetInt64(2) : (long?)null,
                NumericPrecision = !reader.IsDBNull(3) ? reader.GetInt32(3) : (int?)null,
                NumericScale = !reader.IsDBNull(4) ? reader.GetInt32(4) : (int?)null,
                IsNullable = reader.GetString(5) == "YES",
                IsPrimaryKey = reader.GetString(6) == "PRI",
                IsAutoIncrement = reader.GetString(7).Contains("auto_increment"),
                Comment = !reader.IsDBNull(8) ? reader.GetString(8) : null
            });
        }

        return columns;
    }

    private void GenerateDbContext(List<string> tables)
    {
        var code = @"using Microsoft.EntityFrameworkCore;
using Models;

public class MyDbContext : DbContext
{
    public MyDbContext(DbContextOptions<MyDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
    }
";

        foreach (var table in tables)
        {
            var className = ToPascalCase(table);
            code += $@"
    public DbSet<{className}> {className} {{ get; set; }}";
        }

        code += @"
}";

        File.WriteAllText("MyDbContext.cs", code);
        _logger.LogInformation($"📄 已生成 MyDbContext.cs");
    }

    private void GenerateEntityClass(string tableName, List<ColumnInfo> columns)
    {
        var className = ToPascalCase(tableName);
        var code = "using System;\n";
        code += "using System.ComponentModel.DataAnnotations;\n";
        code += "using System.ComponentModel.DataAnnotations.Schema;\n\n";
        code += "namespace Models\n{\n";
        code += $"    [Table(\"{tableName}\")]\n";
        code += $"    public class {className}\n    {{\n";

        _logger.LogInformation($"\n📊 表结构: {tableName}");
        _logger.LogInformation($"- 类名: {className}");
        _logger.LogInformation($"- 列数: {columns.Count}");

        foreach (var column in columns)
        {
            var propertyName = ToPascalCase(column.Name);
            var propertyType = MySqlTypeToClrType(column.DataType, column.IsNullable);

            // 添加注释
            if (!string.IsNullOrWhiteSpace(column.Comment))
            {
                code += $"        /// <summary>\n";
                code += $"        /// {column.Comment}\n";
                code += $"        /// </summary>\n";
            }

            if (column.IsPrimaryKey)
            {
                code += "        [Key]\n";
                if (column.IsAutoIncrement)
                {
                    code += "        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]\n";
                }
            }

            // 添加数据类型特性
            if (propertyType == "string" && column.CharacterMaxLength.HasValue)
            {
                code += $"        [MaxLength({column.CharacterMaxLength.Value})]\n";
                if (column.DataType.Equals("char", StringComparison.OrdinalIgnoreCase))
                {
                    code += "        [Column(TypeName = \"char\")]\n";
                }
            }
            else if ((propertyType == "decimal" || propertyType == "decimal?") &&
                     column.NumericPrecision.HasValue && column.NumericScale.HasValue)
            {
                code += $"        [Column(TypeName = \"decimal({column.NumericPrecision.Value},{column.NumericScale.Value})\")]\n";
            }

            if (!column.IsNullable && propertyType != "string" && !propertyType.EndsWith("?"))
            {
                code += "        [Required]\n";
            }

            if (propertyName != column.Name)
            {
                code += $"        [Column(\"{column.Name}\")]\n";
            }

            code += $"        public {propertyType} {propertyName} {{ get; set; }}\n\n";

            _logger.LogInformation($"  - 列: {column.Name} => {propertyType} {propertyName} " +
                                 $"{(column.IsPrimaryKey ? "(PK)" : "")}" +
                                 $"{(column.IsAutoIncrement ? "(Auto)" : "")}" +
                                 $"{(column.CharacterMaxLength.HasValue ? $"(MaxLen:{column.CharacterMaxLength})" : "")}" +
                                 $"{(column.NumericPrecision.HasValue ? $"(Precision:{column.NumericPrecision},Scale:{column.NumericScale})" : "")}");
        }

        code += "    }\n}";

        File.WriteAllText($"Models/{className}.cs", code);
        _logger.LogInformation($"📄 已生成 Models/{className}.cs");
    }

    private void ExportTableToExcel(MySqlConnection connection, string tableName, List<ColumnInfo> columns)
    {




       


        //查询数据表总的数据数量
        // 构造 SQL 语句
        // 查询表中的总数据量
        string countQuery = $"SELECT COUNT(*) FROM {tableName}";
        var countCommand = new MySqlCommand(countQuery, connection);

        int totalCount = 0;
        using (var countReader = countCommand.ExecuteReader())
        {
            if (countReader.Read())
            {
                totalCount = countReader.GetInt32(0);
                _logger.LogInformation($"查询到表格:{tableName}有{totalCount}条数据要被存储到数据库中");
            }
            else
            {
                _logger.LogInformation($"查询到表格:{tableName}没有数据");
                return;
            }
        }

        if (totalCount == 0)
        {
            return;
        }

        // 定义分页参数
        int pageSize = 1000; // 每页读取的数据量
        int totalPages = (int)Math.Ceiling(totalCount / (double)pageSize); // 总页数

        _logger.LogInformation($"开始分页处理数据，总页数：{totalPages}");

        int rowIndex = 3; // 从第4行开始填充数据

        for (int page = 0; page < totalPages; page++)
        {
            DateTime startTime = DateTime.Now;
            

            int offset = page * pageSize;
            // 创建工作簿
            IWorkbook workbook = new XSSFWorkbook();
            ISheet sheet = workbook.CreateSheet(tableName);

            // 创建标题行
            IRow headerRow = sheet.CreateRow(0);
            IRow typeRow = sheet.CreateRow(1);
            IRow commentRow = sheet.CreateRow(2);

            // 设置标题行样式
            ICellStyle headerStyle = workbook.CreateCellStyle();
            IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            headerStyle.SetFont(headerFont);
            headerStyle.FillForegroundColor = IndexedColors.LightBlue.Index;
            headerStyle.FillPattern = FillPattern.SolidForeground;

            // 设置类型行样式
            ICellStyle typeStyle = workbook.CreateCellStyle();
            IFont typeFont = workbook.CreateFont();
            typeFont.IsItalic = true;
            typeStyle.SetFont(typeFont);
            typeStyle.FillForegroundColor = IndexedColors.LightYellow.Index;
            typeStyle.FillPattern = FillPattern.SolidForeground;

            // 设置注释行样式
            ICellStyle commentStyle = workbook.CreateCellStyle();
            IFont commentFont = workbook.CreateFont();
            commentFont.Color = IndexedColors.Grey50Percent.Index;
            commentStyle.SetFont(commentFont);

            // 填充标题行
            for (int i = 0; i < columns.Count; i++)
            {
                // 列名
                ICell headerCell = headerRow.CreateCell(i);
                headerCell.SetCellValue(columns[i].Name);
                headerCell.CellStyle = headerStyle;

                // 类型信息
                ICell typeCell = typeRow.CreateCell(i);
                string typeInfo = columns[i].DataType;
                if (columns[i].IsPrimaryKey) typeInfo += " (PK)";
                if (columns[i].IsAutoIncrement) typeInfo += " (Auto)";
                if (columns[i].IsNullable) typeInfo += " (Nullable)";
                typeCell.SetCellValue(typeInfo);
                typeCell.CellStyle = typeStyle;

                // 注释
                ICell commentCell = commentRow.CreateCell(i);
                string comment = !string.IsNullOrWhiteSpace(columns[i].Comment) ?
                    columns[i].Comment :
                    GuessColumnPurpose(columns[i].Name);
                commentCell.SetCellValue(comment);
                commentCell.CellStyle = commentStyle;
            }
            // 分页查询数据
            string query = $"SELECT * FROM {tableName} LIMIT {pageSize} OFFSET {offset}";
            var command = new MySqlCommand(query, connection);
            try
            {
                using (var reader = command.ExecuteReader())
                {

                    _logger.LogInformation($"本次sql查询消耗时间："+(DateTime.Now-startTime).TotalSeconds);
                    while (reader.Read())
                    {
                        IRow dataRow = sheet.CreateRow(rowIndex++);
                        for (int i = 0; i < reader.FieldCount; i++)
                        {
                            if (!reader.IsDBNull(i))
                            {
                                var value = reader.GetValue(i);
                                dataRow.CreateCell(i).SetCellValue(value.ToString());
                            }
                            else
                            {
                                dataRow.CreateCell(i).SetCellValue("NULL");
                            }
                        }
                    }

                }



                // 自动调整列宽
                for (int i = 0; i < columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }
                string fileName = "";
                if (totalPages > 1) {


                    // 保存Excel文件
                    fileName = $"ExcelExports/{tableName + "_" + page * pageSize}_Export.xlsx";
                   
                }
                else
                {
                    // 保存Excel文件
                    fileName = $"ExcelExports/{tableName}_Export.xlsx";
                }
                using (FileStream fileStream = new FileStream(fileName, FileMode.Create))
                {
                    workbook.Write(fileStream);
                }

                _logger.LogInformation($"📊 已生成Excel文件: {fileName}");
                
                workbook.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 导出表 {tableName} 到Excel时出错: {ex.Message}");
            }
            duration = DateTime.Now - startTime;
            _logger.LogInformation($"已完成第 {page + 1+"/"+ totalPages} 页数据处理上一次处理需要时间："+duration.TotalSeconds+"预计还需要时间："+duration.TotalSeconds*(totalPages-page+1));
        }





    }




    public void saveExcel()
    {

    }




    // 执行要计时的代码
    public TimeSpan duration;
    // 定义分页参数
    public int pageSize = 10; // 每页显示的记录数
    public int pageNumber = 1; // 当前页码（从1开始）

    private string GuessColumnPurpose(string columnName)
    {
        columnName = columnName.ToLower();

        if (columnName.Contains("id")) return "唯一标识符";
        if (columnName.Contains("name")) return "名称";
        if (columnName.Contains("title")) return "标题";
        if (columnName.Contains("desc") || columnName.Contains("description")) return "描述信息";
        if (columnName.Contains("create") || columnName.Contains("created")) return "创建时间";
        if (columnName.Contains("update") || columnName.Contains("modified")) return "更新时间";
        if (columnName.Contains("status")) return "状态标识";
        if (columnName.Contains("type")) return "类型分类";
        if (columnName.Contains("price") || columnName.Contains("amount")) return "金额/价格";
        if (columnName.Contains("quantity") || columnName.Contains("qty")) return "数量";
        if (columnName.Contains("email")) return "电子邮件地址";
        if (columnName.Contains("phone")) return "电话号码";
        if (columnName.Contains("address")) return "地址信息";
        if (columnName.Contains("url") || columnName.Contains("link")) return "网址链接";
        if (columnName.Contains("image") || columnName.Contains("img")) return "图片路径或数据";
        if (columnName.Contains("order") || columnName.Contains("sort")) return "排序序号";
        if (columnName.Contains("is_") || columnName.StartsWith("is")) return "是否标志";
        if (columnName.Contains("flag")) return "标志位";
        if (columnName.Contains("code")) return "编码";
        if (columnName.Contains("date")) return "日期";
        if (columnName.Contains("time")) return "时间";

        return "未知用途";
    }

    private string MySqlTypeToClrType(string mySqlType, bool isNullable)
    {
        var questionMark = isNullable ? "?" : "";

        switch (mySqlType.ToLower())
        {
            case "bigint": return "long" + questionMark;
            case "int":
            case "integer": return "int" + questionMark;
            case "smallint": return "short" + questionMark;
            case "tinyint": return "byte" + questionMark;
            case "bit": return "bool" + questionMark;
            case "decimal":
            case "numeric": return "decimal" + questionMark;
            case "double": return "double" + questionMark;
            case "float": return "float" + questionMark;
            case "char":
            case "varchar":
            case "text":
            case "longtext":
            case "mediumtext":
            case "tinytext": return "string";
            case "date":
            case "datetime":
            case "timestamp": return "DateTime" + questionMark;
            case "time": return "TimeSpan" + questionMark;
            case "binary":
            case "varbinary":
            case "blob":
            case "longblob":
            case "mediumblob":
            case "tinyblob": return "byte[]";
            case "json": return "string";
            case "guid": return "Guid" + questionMark;
            default: return "object" + questionMark;
        }
    }

    private string ToPascalCase(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        var parts = input.Split(new[] { '_', ' ' }, StringSplitOptions.RemoveEmptyEntries);
        return string.Join("", parts.Select(p => char.ToUpper(p[0]) + p.Substring(1).ToLower()));
    }
}

public class ColumnInfo
{
    public string Name { get; set; }
    public string DataType { get; set; }
    public long? CharacterMaxLength { get; set; }
    public int? NumericPrecision { get; set; }
    public int? NumericScale { get; set; }
    public bool IsNullable { get; set; }
    public bool IsPrimaryKey { get; set; }
    public bool IsAutoIncrement { get; set; }
    public string Comment { get; set; }
}

// 使用示例
//class Program
//{
//    static void Main333(string[] args)
//    {
//        // 替换为你的数据库连接字符串
//        var connectionString = "server=192.168.1.99;port=60006;database=hse_data_save_android_prod;user=root;password=your_new_root_password;"
//        + "Connection Timeout=60;"            // 连接建立阶段的超时时间（秒）
//        + "Default Command Timeout=300;"      // 单条 SQL 命令的超时时间（秒）
//        + "Pooling=true;"                     // 启用连接池（默认 true）
//        + "MinimumPoolSize=5;"               // 最小连接池大小
//        + "MaximumPoolSize=100;";             // 最大连接池大小
//        // 创建生成器实例
//        var generator = new DatabaseSchemaGenerator(connectionString);

//        // 生成代码和Excel (将false改为true以生成Excel)
//        generator.Generate(generateExcel: true);
//    }
//}