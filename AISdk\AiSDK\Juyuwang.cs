﻿using System.Diagnostics;
using System.Reflection;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class Juyuwang:AISDKProvider
	{
		private string url = "http://192.168.15.13:11434/api/chat";
		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			var data = new JuyuwangHttp("gemma2-27b:latest", messages, false);
			Instance.Sendkk(url, data, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				callback(backText, bol);
			});
		}
        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            //if (string.IsNullOrEmpty(Model))
            //{
            //    Model = "doubao-embedding-text-240715";
            //}
            //string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            //callback(backText, false);
        }

        public void ChatWithAi(string prompt, Action<string, bool> callback)
        {
            //List<Message> messages = new List<Message>();
            //messages.Add(new Message { role = "user", content = prompt });
            //var data = new JuyuwangHttp("gemma2-27b:latest", messages, false);
            //Instance.Sendkk(url, data, (backText, bol) =>
            //{
            //    // UnityEngine.Debug.Log(backText);
            //    callback(backText, bol);
            //});
        }
        public async Task<AiChatBase> KernelFunAsync()
        {
            return null;
        }


        public class JuyuwangHttp
		{
			public string model { get; set; }
			public List<Message> messages { get; set; }

			public Boolean stream { get; set; }

			//public Boolean stream { get; set; }

			// 你可以添加一个构造函数来方便地初始化对象  
			//public KuaiShuData(string model, Message[] messages, bool stream)
			//{
			//	this.model = model;
			//	//this.messages = messages;
			//	this.stream = stream;
			//}

			// 或者，如果你想要一个更简单的构造函数，只接受一个消息  
			public JuyuwangHttp(string model, List<Message> content, Boolean stream)
			{
				this.model = model;
				this.messages = new List<Message>();
				this.messages = content;
				//this.messages.Add(new Message { role = "urser", content = "上一次提问的什么问题" });

				this.stream = stream;
			}
		}

	}
}
