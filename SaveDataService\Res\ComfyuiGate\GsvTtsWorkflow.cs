﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// GsvTtsWorkflow - ComfyUI工作流调用类
    /// 基于文件: gsv_tts_workflow .json
    /// 自动生成时间: 2025-06-08 00:08:55
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class GsvTtsWorkflow : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义（ComfyUI API格式）
        /// </summary>
        private const string WORKFLOW_JSON = @"{
  ""2"": {
    ""inputs"": {
      ""text"": ""我是杨思纯，是兄弟就来砍我"",
      ""language"": ""中文"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""TextDictNode"",
    ""_meta"": {
      ""title"": ""input-text-需要的台词""
    }
  },
  ""3"": {
    ""inputs"": {
      ""text"": """",
      ""language"": ""中文"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""TextDictNode"",
    ""_meta"": {
      ""title"": ""input-text-还没测试的输入暂时不输入""
    }
  },
  ""4"": {
    ""inputs"": {
      ""audio"": ""委屈-要不是溜出来的时候身上没带够钱，本小姐也不至于…….wav"",
      ""audioUI"": """"
    },
    ""class_type"": ""LoadAudio"",
    ""_meta"": {
      ""title"": ""input-audio-参考人声音频""
    }
  },
  ""6"": {
    ""inputs"": {
      ""exp_name"": ""aifsh"",
      ""version"": ""v2"",
      ""is_half"": true,
      ""if_redataset"": true,
      ""if_ft_sovits"": true,
      ""if_ft_gpt"": true
    },
    ""class_type"": ""ExperienceNode"",
    ""_meta"": {
      ""title"": ""ExperienceNode""
    }
  },
  ""8"": {
    ""inputs"": {
      ""GPT_weight"": ""gsv-v2final-pretrained/s1bert25hz-5kh-longer-epoch=12-step=369668.ckpt"",
      ""SoVITS_weight"": ""gsv-v2final-pretrained/s2G2333k.pth"",
      ""how_to_cut"": ""凑四句一切"",
      ""speed"": 0.9800000000000002,
      ""top_k"": 15,
      ""top_p"": 1,
      ""temperature"": 1,
      ""text_dict"": [
        ""2"",
        0
      ],
      ""prompt_text_dict"": [
        ""3"",
        0
      ],
      ""prompt_audio"": [
        ""4"",
        0
      ],
      ""config"": [
        ""6"",
        0
      ]
    },
    ""class_type"": ""GSVTTSNode"",
    ""_meta"": {
      ""title"": ""GSVTTSNode""
    }
  },
  ""9"": {
    ""inputs"": {
      ""filename_prefix"": [
        ""10"",
        0
      ],
      ""audioUI"": """",
      ""audio"": [
        ""8"",
        0
      ]
    },
    ""class_type"": ""SaveAudio"",
    ""_meta"": {
      ""title"": ""output-audio-生成的音频""
    }
  },
  ""10"": {
    ""inputs"": {
      ""text"": ""audio/comfy"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""CR Text"",
    ""_meta"": {
      ""title"": ""input-text-文件前缀""
    }
  }
}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="text_text">input-text-需要的台词 - text</param>
        /// <param name="text_language">input-text-需要的台词 - language</param>
        /// <param name="text_speak_and_recognation">input-text-需要的台词 - speak_and_recognation</param>
        /// <param name="text_text1">input-text-还没测试的输入暂时不输入 - text</param>
        /// <param name="text_language1">input-text-还没测试的输入暂时不输入 - language</param>
        /// <param name="text_speak_and_recognation1">input-text-还没测试的输入暂时不输入 - speak_and_recognation</param>
        /// <param name="audio_audio">input-audio-参考人声音频 - audio</param>
        /// <param name="audio_audioUI">input-audio-参考人声音频 - audioUI</param>
        /// <param name="text_text2">input-text-文件前缀 - text</param>
        /// <param name="text_speak_and_recognation2">input-text-文件前缀 - speak_and_recognation</param>
        /// <returns>任务ID</returns>
        public static async Task<string> runWorkflow(string text_text = "我是杨思纯，是兄弟就来砍我", string text_language = "中文", string text_speak_and_recognation = @"{
  ""__value__"": [
    false,
    true
  ]
}", string text_text1 = "", string text_language1 = "中文", string text_speak_and_recognation1 = @"{
  ""__value__"": [
    false,
    true
  ]
}", string audio_audio = "委屈-要不是溜出来的时候身上没带够钱，本小姐也不至于…….wav", string audio_audioUI = "", string text_text2 = "audio/comfy", string text_speak_and_recognation2 = @"{
  ""__value__"": [
    false,
    true
  ]
}")
        {
            try
            {
                // 解析工作流JSON（已经是ComfyUI API格式）
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数（工作流已经是API格式）
                // 更新节点 2 的 text 参数
                if (workflow["2"]?["inputs"]?["text"] != null)
                {
                    workflow["2"]!["inputs"]!["text"] = JToken.FromObject(text_text);
                }

                // 更新节点 2 的 language 参数
                if (workflow["2"]?["inputs"]?["language"] != null)
                {
                    workflow["2"]!["inputs"]!["language"] = JToken.FromObject(text_language);
                }

                // 更新节点 2 的 speak_and_recognation 参数
                if (workflow["2"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["2"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation);
                }

                // 更新节点 3 的 text 参数
                if (workflow["3"]?["inputs"]?["text"] != null)
                {
                    workflow["3"]!["inputs"]!["text"] = JToken.FromObject(text_text1);
                }

                // 更新节点 3 的 language 参数
                if (workflow["3"]?["inputs"]?["language"] != null)
                {
                    workflow["3"]!["inputs"]!["language"] = JToken.FromObject(text_language1);
                }

                // 更新节点 3 的 speak_and_recognation 参数
                if (workflow["3"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["3"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation1);
                }

                // 更新节点 4 的 audio 参数
                if (workflow["4"]?["inputs"]?["audio"] != null)
                {
                    workflow["4"]!["inputs"]!["audio"] = JToken.FromObject(audio_audio);
                }

                // 更新节点 4 的 audioUI 参数
                if (workflow["4"]?["inputs"]?["audioUI"] != null)
                {
                    workflow["4"]!["inputs"]!["audioUI"] = JToken.FromObject(audio_audioUI);
                }

                // 更新节点 10 的 text 参数
                if (workflow["10"]?["inputs"]?["text"] != null)
                {
                    workflow["10"]!["inputs"]!["text"] = JToken.FromObject(text_text2);
                }

                // 更新节点 10 的 speak_and_recognation 参数
                if (workflow["10"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["10"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation2);
                }

                // 提交工作流到ComfyUI（已经是API格式，无需转换）
                string workflowJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(workflowJson, "GsvTtsWorkflow");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 获取可用的服务器
                var onlineServers = comfyUIManage.GetOnlineServers();
                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器可用");
                    return "";
                }

                // 使用第一个在线服务器
                var selectedServer = onlineServers[0];
                Console.WriteLine($"🚀 使用服务器: {selectedServer.serverName} ({selectedServer.serverUrl}:{selectedServer.port})");

                // 准备输入参数用于日志记录
                var inputParams = new {
                    text_text = text_text,
                    text_language = text_language,
                    text_speak_and_recognation = text_speak_and_recognation,
                    text_text1 = text_text1,
                    text_language1 = text_language1,
                    text_speak_and_recognation1 = text_speak_and_recognation1,
                    audio_audio = audio_audio,
                    audio_audioUI = audio_audioUI,
                    text_text2 = text_text2,
                    text_speak_and_recognation2 = text_speak_and_recognation2
                };

                // 直接提交工作流到ComfyUI服务器执行（API格式）
                var result = await comfyUIManage.SubmitWorkflowToServerWithMonitoring(selectedServer.id, workflowJson, workflowId, "GsvTtsWorkflow", inputParams);
                string taskId = result.taskId;
                string submitResult = result.result;

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine($"工作流提交失败: {submitResult}");
                    return "";
                }

                // 检查提交结果
                if (submitResult.Contains("prompt_id") || submitResult.Contains("成功"))
                {
                    Console.WriteLine($"✅ 工作流已成功提交到ComfyUI服务器，任务ID: {taskId}");
                    Console.WriteLine($"服务器响应: {submitResult}");
                    
                    // 等待一段时间后检查是否有生成的文件
                    await Task.Delay(5000); // 等待5秒
                    await CheckGeneratedFiles(taskId);
                    
                    return taskId;
                }
                else
                {
                    Console.WriteLine($"❌ 工作流提交失败: {submitResult}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                return comfyUIManage.AddWorkflow(workflowName, workflowJson, "generated", $"自动生成的工作流: {workflowName}", "system");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存工作流到数据库失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 检查任务生成的文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        private static async Task CheckGeneratedFiles(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    // 检查任务文件
                    var filesJson = comfyUIManage.GetTaskFiles(taskId);
                    if (!string.IsNullOrEmpty(filesJson) && filesJson != "[]")
                    {
                        Console.WriteLine($"📁 任务 {taskId} 生成的文件: {filesJson}");
                        
                        // 解析文件信息并检查文件大小
                        var files = JsonConvert.DeserializeObject<List<dynamic>>(filesJson);
                        if (files != null)
                        {
                            foreach (var file in files)
                            {
                                string filePath = file.filePath?.ToString() ?? "";
                                string fileName = file.fileName?.ToString() ?? "";
                                long fileSize = file.fileSize ?? 0;
                                
                                if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                                {
                                    var actualSize = new System.IO.FileInfo(filePath).Length;
                                    Console.WriteLine($"✅ 文件存在: {fileName} (大小: {actualSize} 字节)");
                                }
                                else
                                {
                                    Console.WriteLine($"❌ 文件不存在: {fileName} (路径: {filePath})");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ 任务 {taskId} 暂未生成文件，可能仍在处理中");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查生成文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取API描述信息
        /// </summary>
        /// <returns>API描述信息</returns>
        public static string GetApiDescription()
        {
            return "工作流: GsvTtsWorkflow\n" +
                   "描述: GsvTtsWorkflow工作流\n" +
                   "参数:\n" +
                   "  - text_text: text\n" +
                   "  - text_language: language\n" +
                   "  - text_speak_and_recognation: speak_and_recognation\n" +
                   "  - text_text1: text\n" +
                   "  - text_language1: language\n" +
                   "  - text_speak_and_recognation1: speak_and_recognation\n" +
                   "  - audio_audio: audio\n" +
                   "  - audio_audioUI: audioUI\n" +
                   "  - text_text2: text\n" +
                   "  - text_speak_and_recognation2: speak_and_recognation\n" +
                   "";
        }

    }
}
