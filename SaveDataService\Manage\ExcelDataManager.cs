﻿using ExcelToData;
using GameServer.Util;
using GameServer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace SaveDataService.Manage
{
    public class ExcelDataManager
    {
        // 私有静态实例变量
        private static ExcelDataManager _instance;
        // 线程安全锁对象
        private static readonly object _lock = new object();
        // 私有构造函数，防止外部实例化
        private ExcelDataManager()
        {
            // 初始化代码
        }
        // 公共静态属性，用于访问单例实例
        public static ExcelDataManager Instance
        {
            get
            {
                // 双重检查锁定模式确保线程安全
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ExcelDataManager();
                        }
                    }
                }
                return _instance;
            }
        }
        /// <summary>
        /// 导表
        /// </summary>
        public void ExcuteExcelData()
        {
            #region 生成实体类ORM文件
            ExcelData.ExcuteBuild();
            #endregion
        }
        /// <summary>
        /// 数据库上生成表格
        /// </summary>
        public void CreateDatabaseTables() {
            if (MysqlUtil.canConnect(AppConfig.Instance.mysqlConnectString))
            {
                //MysqlUtil.CheckOrCreateDatabase(AppConfig.Instance.mysqlDatabaseName, AppConfig.Instance.mysqlConnectString);
                string mysqlstr = MysqlUtil.remoteMysqlDababaseStr(AppConfig.Instance.mysqlConnectString);
                if (MysqlUtil.CheckOrCreateDatabase(AppConfig.Instance.mysqlDatabaseName, mysqlstr))
                {
                    if (!MysqlUtil.TableIsNull(AppConfig.Instance.mysqlDatabaseName, AppConfig.Instance.mysqlConnectString))
                    {
                        using (var context = new ORMTables())
                        {
                            bool created = context.Database.EnsureCreated(); // 返回 true 表示数据库被创建

                            if (created)
                            {
                                Console.WriteLine("数据库的表格已创建！");
                                RefreshDatabaseTables();
                            }

                        }
                    }
                    else
                    {
                        //using (var context = new ORMTables())
                        //{
                        //    if (context.Database.EnsureDeleted())
                        //    {
                        //        Console.WriteLine("数据库表格已删除!");
                        //    }
                        //}

                    }
                }
                else
                {
                    Console.WriteLine("数据库已存在");
                    //RefreshDatabaseTables();
                }


            }
        }
        ///重新保存数据库表
        public void RefreshDatabaseTables() {

            // 使用 using 语句确保正确释放数据库上下文，避免影响单例
            using (var db = new ORMTables())
            {
                db.Accounts.ExecuteDelete();
                db.AI_Game_Outputs.ExecuteDelete();
                db.AI_Game_OutputDatas.ExecuteDelete();
                db.ArtAIChat_Outputs.ExecuteDelete();
                db.DesignAIChat_Outputs.ExecuteDelete();
                db.ErrorInfos.ExecuteDelete();
                db.serverVars.ExecuteDelete();
                db.SeverConfigBases.ExecuteDelete();
                db.SeverDatas.ExecuteDelete();
                db.SysActives.ExecuteDelete();
                db.TimeEvents.ExecuteDelete();
                db.UserVars.ExecuteDelete();
                db.SaveChanges();

                db.readAllData();
                // 不需要手动调用 Dispose()，using 语句会自动处理
            }
            Console.WriteLine("数据上传数据库完毕");
        }
    }
}
