﻿//using GameServer.ExcelData;
//using System;
//using System.Collections.Concurrent;
//using System.Collections.Generic;
//using System.Diagnostics.CodeAnalysis;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//namespace GameServer.GameService.Tools
//{
//    internal class ExcelList<T> where T : EntityBase
//    {
//        private List<T> _list=new List<T>();
//        public int Count
//        {
//            get
//            {
//                return _list.Count;
//            }
//        }

//    }
//    internal class ExcelDictionary<TKey, TValue> where TValue : EntityBase
//    {
//        private EntityBase baseExcel;
//        private ConcurrentDictionary<TKey, string> dict = new ConcurrentDictionary<TKey, string>();
//        public int Count
//        {
//            get
//            {
//                return dict.Count;
//            }
//        }
//        public TValue this[TKey index]
//        {
//            get
//            {
//                if (baseExcel == null)
//                {
//                    return null;
//                }
//                return (TValue)baseExcel.getDataByIdInstance(dict[index]);
//            }
//            set
//            {
//                if (baseExcel == null)
//                {
//                    baseExcel = (TValue)value.Instance;
//                }
//                dict[index] = value.id;
//            }
//        }
//        public void Add(TKey key, TValue value)
//        {
//            dict[key] = value.id;
//        }
//        public bool ContainsKey(TKey key)
//        {
//            return dict.ContainsKey(key);
//        }
//        public bool TryRemove(TKey key, [MaybeNullWhen(false)] out TValue value)
//        {
//            string outID = "";
//            bool isRight = dict.TryRemove(key, out outID);
//            if (baseExcel != null)
//            {
//                value = (TValue)baseExcel.getDataByIdInstance(outID);
//            }
//            else
//            {
//                value = null;
//            }
//            return isRight;
//        }
//    }
//}
