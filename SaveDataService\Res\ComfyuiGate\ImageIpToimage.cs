﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SaveDataService;
using SaveDataService.Manage;

namespace ComfyuiGate
{
    /// <summary>
    /// ImageIpToimage - ComfyUI工作流调用类
    /// 基于文件: ImageIpToimage.json
    /// 自动生成时间: 2025-06-08 00:08:55
    /// 继承RESTfulAPIBase，自动提供RESTful API功能
    /// </summary>
    public class ImageIpToimage : RESTfulAPIBase
    {
        /// <summary>
        /// 工作流JSON定义（ComfyUI API格式）
        /// </summary>
        private const string WORKFLOW_JSON = @"{
  ""410"": {
    ""inputs"": {
      ""image"": ""ComfyUI_02736_.png""
    },
    ""class_type"": ""LoadImage"",
    ""_meta"": {
      ""title"": ""input-image-加载图片""
    }
  },
  ""411"": {
    ""inputs"": {
      ""text_0"": ""The image is a digital illustration of a woman wearing a long, flowing dress. The dress is predominantly white with maroon accents on the sleeves and waist. The skirt of the dress is long and flows down to the floor. The bodice is a deep brown color with a gold-colored corset in the center. The corset has a floral design on it. The woman has long, light blue hair that is styled in loose waves and falls over her shoulders. She has a serious expression on her face and is standing with her hands on her hips. The background is a light beige color."",
      ""text"": [
        ""507"",
        2
      ]
    },
    ""class_type"": ""ShowText|pysssss"",
    ""_meta"": {
      ""title"": ""Show Text 🐍""
    }
  },
  ""415"": {
    ""inputs"": {
      ""ckpt_name"": ""XL\\4nimaPencilXL_v101.safetensors""
    },
    ""class_type"": ""CheckpointLoaderSimple"",
    ""_meta"": {
      ""title"": ""Checkpoint加载器（简易）""
    }
  },
  ""416"": {
    ""inputs"": {
      ""text"": [
        ""499"",
        0
      ],
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""clip"": [
        ""483"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""CLIP文本编码""
    }
  },
  ""417"": {
    ""inputs"": {
      ""text"": ""ng_deepnegative_v1_75t,(badhandv4:1.2),EasyNegative,(worst quality:2),"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""clip"": [
        ""483"",
        1
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""CLIP文本编码""
    }
  },
  ""418"": {
    ""inputs"": {
      ""seed"": 522449837528387,
      ""steps"": 30,
      ""cfg"": 3,
      ""sampler_name"": ""euler_ancestral"",
      ""scheduler"": ""normal"",
      ""denoise"": 1,
      ""model"": [
        ""485"",
        0
      ],
      ""positive"": [
        ""416"",
        0
      ],
      ""negative"": [
        ""417"",
        0
      ],
      ""latent_image"": [
        ""419"",
        0
      ]
    },
    ""class_type"": ""KSampler"",
    ""_meta"": {
      ""title"": ""K采样器""
    }
  },
  ""419"": {
    ""inputs"": {
      ""width"": 768,
      ""height"": 1280,
      ""batch_size"": 1
    },
    ""class_type"": ""EmptyLatentImage"",
    ""_meta"": {
      ""title"": ""空Latent图像""
    }
  },
  ""422"": {
    ""inputs"": {
      ""samples"": [
        ""418"",
        0
      ],
      ""vae"": [
        ""415"",
        2
      ]
    },
    ""class_type"": ""VAEDecode"",
    ""_meta"": {
      ""title"": ""VAE解码""
    }
  },
  ""430"": {
    ""inputs"": {
      ""pixels"": [
        ""422"",
        0
      ],
      ""vae"": [
        ""432"",
        0
      ]
    },
    ""class_type"": ""VAEEncode"",
    ""_meta"": {
      ""title"": ""VAE编码""
    }
  },
  ""431"": {
    ""inputs"": {
      ""text"": [
        ""411"",
        0
      ],
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""clip"": [
        ""445"",
        0
      ]
    },
    ""class_type"": ""CLIPTextEncode"",
    ""_meta"": {
      ""title"": ""CLIP文本编码""
    }
  },
  ""432"": {
    ""inputs"": {
      ""vae_name"": ""ae.safetensors""
    },
    ""class_type"": ""VAELoader"",
    ""_meta"": {
      ""title"": ""加载VAE""
    }
  },
  ""433"": {
    ""inputs"": {
      ""sampler_name"": ""euler""
    },
    ""class_type"": ""KSamplerSelect"",
    ""_meta"": {
      ""title"": ""K采样器选择""
    }
  },
  ""434"": {
    ""inputs"": {
      ""scheduler"": ""simple"",
      ""steps"": 20,
      ""denoise"": [
        ""519"",
        0
      ],
      ""model"": [
        ""447"",
        0
      ]
    },
    ""class_type"": ""BasicScheduler"",
    ""_meta"": {
      ""title"": ""基本调度器""
    }
  },
  ""435"": {
    ""inputs"": {
      ""noise_seed"": 928567015015668
    },
    ""class_type"": ""RandomNoise"",
    ""_meta"": {
      ""title"": ""随机噪波""
    }
  },
  ""436"": {
    ""inputs"": {
      ""guidance"": 3.5,
      ""conditioning"": [
        ""431"",
        0
      ]
    },
    ""class_type"": ""FluxGuidance"",
    ""_meta"": {
      ""title"": ""Flux引导""
    }
  },
  ""438"": {
    ""inputs"": {
      ""model"": [
        ""447"",
        0
      ],
      ""conditioning"": [
        ""436"",
        0
      ]
    },
    ""class_type"": ""BasicGuider"",
    ""_meta"": {
      ""title"": ""基本引导器""
    }
  },
  ""439"": {
    ""inputs"": {
      ""noise"": [
        ""435"",
        0
      ],
      ""guider"": [
        ""438"",
        0
      ],
      ""sampler"": [
        ""433"",
        0
      ],
      ""sigmas"": [
        ""434"",
        0
      ],
      ""latent_image"": [
        ""430"",
        0
      ]
    },
    ""class_type"": ""SamplerCustomAdvanced"",
    ""_meta"": {
      ""title"": ""自定义采样器（高级）""
    }
  },
  ""440"": {
    ""inputs"": {
      ""samples"": [
        ""439"",
        0
      ],
      ""vae"": [
        ""432"",
        0
      ]
    },
    ""class_type"": ""VAEDecode"",
    ""_meta"": {
      ""title"": ""VAE解码""
    }
  },
  ""445"": {
    ""inputs"": {
      ""clip_name1"": ""t5xxl_fp8_e4m3fn.safetensors"",
      ""clip_name2"": ""clip_l.safetensors"",
      ""type"": ""flux"",
      ""device"": ""default""
    },
    ""class_type"": ""DualCLIPLoader"",
    ""_meta"": {
      ""title"": ""双CLIP加载器""
    }
  },
  ""447"": {
    ""inputs"": {
      ""unet_name"": ""flux1-dev-Q8_0.gguf""
    },
    ""class_type"": ""UnetLoaderGGUF"",
    ""_meta"": {
      ""title"": ""Unet Loader (GGUF)""
    }
  },
  ""483"": {
    ""inputs"": {
      ""lora_name"": ""xl\\游戏角色LoRA_v1.0.safetensors"",
      ""strength_model"": 0.9000000000000001,
      ""strength_clip"": 1,
      ""model"": [
        ""415"",
        0
      ],
      ""clip"": [
        ""415"",
        1
      ]
    },
    ""class_type"": ""LoraLoader"",
    ""_meta"": {
      ""title"": ""加载LoRA""
    }
  },
  ""484"": {
    ""inputs"": {
      ""preset"": ""PLUS (high strength)"",
      ""model"": [
        ""483"",
        0
      ]
    },
    ""class_type"": ""IPAdapterUnifiedLoader"",
    ""_meta"": {
      ""title"": ""IPAdapter Unified Loader""
    }
  },
  ""485"": {
    ""inputs"": {
      ""weight"": [
        ""520"",
        0
      ],
      ""weight_faceidv2"": 1,
      ""weight_type"": ""style transfer"",
      ""combine_embeds"": ""concat"",
      ""start_at"": 0.001,
      ""end_at"": 0.999,
      ""embeds_scaling"": ""V only"",
      ""layer_weights"": ""3:0, 6:1.1"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""model"": [
        ""484"",
        0
      ],
      ""ipadapter"": [
        ""484"",
        1
      ],
      ""image"": [
        ""410"",
        0
      ]
    },
    ""class_type"": ""IPAdapterMS"",
    ""_meta"": {
      ""title"": ""IPAdapterMS""
    }
  },
  ""498"": {
    ""inputs"": {
      ""text"": ""Exquisite features, realistic painting style, real details, HD details, white background, simple background,fullbody,standing"",
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      }
    },
    ""class_type"": ""CR Text"",
    ""_meta"": {
      ""title"": ""input-text-关键词""
    }
  },
  ""499"": {
    ""inputs"": {
      ""separator"": """",
      ""text1"": [
        ""498"",
        0
      ],
      ""text2"": [
        ""505"",
        0
      ]
    },
    ""class_type"": ""CR Text Concatenate"",
    ""_meta"": {
      ""title"": ""🔤 CR Text Concatenate""
    }
  },
  ""504"": {
    ""inputs"": {
      ""vae_name"": ""sdxlVaeAnimeTest_beta120000.safetensors""
    },
    ""class_type"": ""VAELoader"",
    ""_meta"": {
      ""title"": ""加载VAE""
    }
  },
  ""505"": {
    ""inputs"": {
      ""find1"": "" upper_body"",
      ""replace1"": """",
      ""find2"": ""half_body"",
      ""replace2"": """",
      ""find3"": ""sitting"",
      ""replace3"": """",
      ""text"": [
        ""411"",
        0
      ]
    },
    ""class_type"": ""CR Text Replace"",
    ""_meta"": {
      ""title"": ""🔤 CR Text Replace""
    }
  },
  ""506"": {
    ""inputs"": {
      ""model"": ""Florence-2-Flux-Large"",
      ""precision"": ""fp16"",
      ""attention"": ""sdpa""
    },
    ""class_type"": ""Florence2ModelLoader"",
    ""_meta"": {
      ""title"": ""Florence2ModelLoader""
    }
  },
  ""507"": {
    ""inputs"": {
      ""text_input"": """",
      ""task"": ""more_detailed_caption"",
      ""fill_mask"": true,
      ""keep_model_loaded"": false,
      ""max_new_tokens"": 1024,
      ""num_beams"": 3,
      ""do_sample"": true,
      ""output_mask_select"": """",
      ""seed"": 507214141179509,
      ""speak_and_recognation"": {
        ""__value__"": [
          false,
          true
        ]
      },
      ""image"": [
        ""410"",
        0
      ],
      ""florence2_model"": [
        ""506"",
        0
      ]
    },
    ""class_type"": ""Florence2Run"",
    ""_meta"": {
      ""title"": ""Florence2Run""
    }
  },
  ""517"": {
    ""inputs"": {
      ""filename_prefix"": ""ComfyUI"",
      ""images"": [
        ""440"",
        0
      ]
    },
    ""class_type"": ""SaveImage"",
    ""_meta"": {
      ""title"": ""output-image-第2次图片""
    }
  },
  ""518"": {
    ""inputs"": {
      ""filename_prefix"": ""ComfyUI"",
      ""images"": [
        ""422"",
        0
      ]
    },
    ""class_type"": ""SaveImage"",
    ""_meta"": {
      ""title"": ""output-image-第一次图片""
    }
  },
  ""519"": {
    ""inputs"": {
      ""value"": 0.4000000000000001
    },
    ""class_type"": ""easy float"",
    ""_meta"": {
      ""title"": ""input-float-第二次修复重绘幅度""
    }
  },
  ""520"": {
    ""inputs"": {
      ""value"": 1.0000000000000002
    },
    ""class_type"": ""easy float"",
    ""_meta"": {
      ""title"": ""input-float-图片参考强度""
    }
  }
}";

        /// <summary>
        /// 运行工作流
        /// </summary>
        /// <param name="image_image">input-image-加载图片 - image</param>
        /// <param name="text_text">input-text-关键词 - text</param>
        /// <param name="text_speak_and_recognation">input-text-关键词 - speak_and_recognation</param>
        /// <param name="float_value">input-float-第二次修复重绘幅度 - value</param>
        /// <param name="float_value1">input-float-图片参考强度 - value</param>
        /// <returns>任务ID</returns>
        public static async Task<string> runWorkflow(string image_image = "ComfyUI_02736_.png", string text_text = "Exquisite features, realistic painting style, real details, HD details, white background, simple background,fullbody,standing", string text_speak_and_recognation = @"{
  ""__value__"": [
    false,
    true
  ]
}", double float_value = 0.4, double float_value1 = 1.0)
        {
            try
            {
                // 解析工作流JSON（已经是ComfyUI API格式）
                var workflow = JsonConvert.DeserializeObject<JObject>(WORKFLOW_JSON);
                if (workflow == null)
                {
                    throw new Exception("无法解析工作流JSON");
                }

                // 更新输入参数（工作流已经是API格式）
                // 更新节点 410 的 image 参数
                if (workflow["410"]?["inputs"]?["image"] != null)
                {
                    workflow["410"]!["inputs"]!["image"] = JToken.FromObject(image_image);
                }

                // 更新节点 498 的 text 参数
                if (workflow["498"]?["inputs"]?["text"] != null)
                {
                    workflow["498"]!["inputs"]!["text"] = JToken.FromObject(text_text);
                }

                // 更新节点 498 的 speak_and_recognation 参数
                if (workflow["498"]?["inputs"]?["speak_and_recognation"] != null)
                {
                    workflow["498"]!["inputs"]!["speak_and_recognation"] = JToken.FromObject(text_speak_and_recognation);
                }

                // 更新节点 519 的 value 参数
                if (workflow["519"]?["inputs"]?["value"] != null)
                {
                    workflow["519"]!["inputs"]!["value"] = JToken.FromObject(float_value);
                }

                // 更新节点 520 的 value 参数
                if (workflow["520"]?["inputs"]?["value"] != null)
                {
                    workflow["520"]!["inputs"]!["value"] = JToken.FromObject(float_value1);
                }

                // 提交工作流到ComfyUI（已经是API格式，无需转换）
                string workflowJson = JsonConvert.SerializeObject(workflow);
                var comfyUIManage = ComfyUIManage.Instance;

                // 首先将工作流保存到数据库（如果不存在）
                string workflowId = EnsureWorkflowExists(workflowJson, "ImageIpToimage");
                if (string.IsNullOrEmpty(workflowId))
                {
                    Console.WriteLine("无法保存工作流到数据库");
                    return "";
                }

                // 获取可用的服务器
                var onlineServers = comfyUIManage.GetOnlineServers();
                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器可用");
                    return "";
                }

                // 使用第一个在线服务器
                var selectedServer = onlineServers[0];
                Console.WriteLine($"🚀 使用服务器: {selectedServer.serverName} ({selectedServer.serverUrl}:{selectedServer.port})");

                // 准备输入参数用于日志记录
                var inputParams = new {
                    image_image = image_image,
                    text_text = text_text,
                    text_speak_and_recognation = text_speak_and_recognation,
                    float_value = float_value,
                    float_value1 = float_value1
                };

                // 直接提交工作流到ComfyUI服务器执行（API格式）
                var result = await comfyUIManage.SubmitWorkflowToServerWithMonitoring(selectedServer.id, workflowJson, workflowId, "ImageIpToimage", inputParams);
                string taskId = result.taskId;
                string submitResult = result.result;

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine($"工作流提交失败: {submitResult}");
                    return "";
                }

                // 检查提交结果
                if (submitResult.Contains("prompt_id") || submitResult.Contains("成功"))
                {
                    Console.WriteLine($"✅ 工作流已成功提交到ComfyUI服务器，任务ID: {taskId}");
                    Console.WriteLine($"服务器响应: {submitResult}");
                    
                    // 等待一段时间后检查是否有生成的文件
                    await Task.Delay(5000); // 等待5秒
                    await CheckGeneratedFiles(taskId);
                    
                    return taskId;
                }
                else
                {
                    Console.WriteLine($"❌ 工作流提交失败: {submitResult}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"运行工作流失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 确保工作流存在于数据库中
        /// </summary>
        /// <param name="workflowJson">工作流JSON</param>
        /// <param name="workflowName">工作流名称</param>
        /// <returns>工作流ID</returns>
        private static string EnsureWorkflowExists(string workflowJson, string workflowName)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                return comfyUIManage.AddWorkflow(workflowName, workflowJson, "generated", $"自动生成的工作流: {workflowName}", "system");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存工作流到数据库失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 检查任务生成的文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        private static async Task CheckGeneratedFiles(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    // 检查任务文件
                    var filesJson = comfyUIManage.GetTaskFiles(taskId);
                    if (!string.IsNullOrEmpty(filesJson) && filesJson != "[]")
                    {
                        Console.WriteLine($"📁 任务 {taskId} 生成的文件: {filesJson}");
                        
                        // 解析文件信息并检查文件大小
                        var files = JsonConvert.DeserializeObject<List<dynamic>>(filesJson);
                        if (files != null)
                        {
                            foreach (var file in files)
                            {
                                string filePath = file.filePath?.ToString() ?? "";
                                string fileName = file.fileName?.ToString() ?? "";
                                long fileSize = file.fileSize ?? 0;
                                
                                if (!string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath))
                                {
                                    var actualSize = new System.IO.FileInfo(filePath).Length;
                                    Console.WriteLine($"✅ 文件存在: {fileName} (大小: {actualSize} 字节)");
                                }
                                else
                                {
                                    Console.WriteLine($"❌ 文件不存在: {fileName} (路径: {filePath})");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ 任务 {taskId} 暂未生成文件，可能仍在处理中");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查生成文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务状态信息</returns>
        public static string GetTaskStatus(string taskId)
        {
            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                var task = comfyUIManage.GetTaskById(taskId);
                if (task != null)
                {
                    return JsonConvert.SerializeObject(task, Formatting.Indented);
                }
                return "任务不存在";
            }
            catch (Exception ex)
            {
                return $"获取任务状态失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取API描述信息
        /// </summary>
        /// <returns>API描述信息</returns>
        public static string GetApiDescription()
        {
            return "工作流: ImageIpToimage\n" +
                   "描述: ImageIpToimage工作流\n" +
                   "参数:\n" +
                   "  - image_image: image\n" +
                   "  - text_text: text\n" +
                   "  - text_speak_and_recognation: speak_and_recognation\n" +
                   "  - float_value: value\n" +
                   "  - float_value1: value\n" +
                   "";
        }

    }
}
