﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Text;

namespace GameServer.Util
{
    class NetUtil
    {
		/// <summary>
		/// 判断ip地址是不是内网
		/// </summary>
		/// <param name="ipv4Address"></param>
		/// <returns></returns>
		bool IsPrivateNetwork(string ipv4Address)
		{
			if (IPAddress.TryParse(ipv4Address, out var ip))
			{
				byte[] ipBytes = ip.GetAddressBytes();
				if (ipBytes[0] == 10) return true;
				if (ipBytes[0] == 172 && ipBytes[1] >= 16 && ipBytes[1] <= 31) return true;
				if (ipBytes[0] == 192 && ipBytes[1] == 168) return true;
			}

			return false;
		}
	}
}
