using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SaveDataService.Manage;
using ComfyuiGate;

namespace SaveDataService
{
    /// <summary>
    /// 测试20：全面测试所有生成的ComfyUI工作流类
    /// 验证每个工作流类的基本功能和API调用
    /// </summary>
    public class Test20AllComfyUIWorkflowsTest
    {
        /// <summary>
        /// 运行所有ComfyUI工作流类的综合测试
        /// </summary>
        public static async Task RunAllWorkflowsTest()
        {
            Console.WriteLine("=== 测试20：全面测试所有生成的ComfyUI工作流类 ===");
            Console.WriteLine("目标：验证每个工作流类的基本功能和API调用");
            Console.WriteLine();

            try
            {
                // 1. 检查ComfyUI管理器状态
                Console.WriteLine("1. 检查ComfyUI管理器状态...");
                var comfyUIManage = ComfyUIManage.Instance;
                var onlineServers = comfyUIManage.GetOnlineServers();

                if (onlineServers.Count == 0)
                {
                    Console.WriteLine("❌ 没有在线的ComfyUI服务器，无法进行工作流测试");
                    Console.WriteLine("请确保至少有一个ComfyUI服务器在线");
                    return;
                }

                Console.WriteLine($"✅ 发现 {onlineServers.Count} 个在线服务器");
                foreach (var server in onlineServers)
                {
                    Console.WriteLine($"   - {server.serverName}: {server.serverUrl}:{server.port}");
                }
                Console.WriteLine();

                // 2. 测试所有工作流类
                var workflowTests = new List<(string name, Func<Task<bool>> test)>
                {
                    ("AceStepAll", TestAceStepAllWorkflow),
                    ("SDXL", TestSDXLWorkflow),
                    ("IndexTts", TestIndexTtsWorkflow),
                    ("ImageIpToimage", TestImageIpToimageWorkflow),
                    ("GsvTtsWorkflow", TestGsvTtsWorkflow)
                };

                Console.WriteLine("2. 开始测试所有工作流类...");
                Console.WriteLine();

                var results = new Dictionary<string, bool>();

                foreach (var (name, test) in workflowTests)
                {
                    Console.WriteLine($"🧪 测试工作流: {name}");
                    Console.WriteLine(new string('-', 50));

                    try
                    {
                        bool success = await test();
                        results[name] = success;

                        if (success)
                        {
                            Console.WriteLine($"✅ {name} 工作流测试成功");
                        }
                        else
                        {
                            Console.WriteLine($"❌ {name} 工作流测试失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ {name} 工作流测试异常: {ex.Message}");
                        results[name] = false;
                    }

                    Console.WriteLine();

                    // 在测试之间添加延迟，避免服务器过载
                    await Task.Delay(2000);
                }

                // 3. 输出测试总结
                Console.WriteLine("3. 测试总结");
                Console.WriteLine(new string('=', 60));

                int successCount = 0;
                int totalCount = results.Count;

                foreach (var (name, success) in results)
                {
                    string status = success ? "✅ 成功" : "❌ 失败";
                    Console.WriteLine($"   {name,-20}: {status}");
                    if (success) successCount++;
                }

                Console.WriteLine();
                Console.WriteLine($"总计: {successCount}/{totalCount} 个工作流测试成功");

                if (successCount == totalCount)
                {
                    Console.WriteLine("🎉 所有工作流测试都成功！ComfyUI集成工作正常");
                }
                else
                {
                    Console.WriteLine($"⚠️ 有 {totalCount - successCount} 个工作流测试失败，需要检查");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }

            Console.WriteLine("\n=== 测试20完成 ===");
        }

        /// <summary>
        /// 测试AceStepAll工作流
        /// </summary>
        private static async Task<bool> TestAceStepAllWorkflow()
        {
            try
            {
                Console.WriteLine("📋 API描述:");
                Console.WriteLine(AceStepAll.GetApiDescription());
                Console.WriteLine();

                Console.WriteLine("🚀 执行工作流...");
                var taskId = await AceStepAll.runWorkflow(
                    "[verse]\n测试歌词 (test lyrics!)\n验证音乐生成 (verify music generation!)",
                    @"{""__value__"":[false,true]}",
                    30
                );

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                    return false;
                }

                Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                // 检查任务状态
                await Task.Delay(3000);
                var status = AceStepAll.GetTaskStatus(taskId);
                Console.WriteLine($"📊 任务状态: {(status.Length > 100 ? status.Substring(0, 100) + "..." : status)}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AceStepAll测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试SDXL工作流
        /// </summary>
        private static async Task<bool> TestSDXLWorkflow()
        {
            try
            {
                Console.WriteLine("📋 API描述:");
                Console.WriteLine(SDXL.GetApiDescription());
                Console.WriteLine();

                Console.WriteLine("🚀 执行工作流...");
                var taskId = await SDXL.runWorkflow(
                    "test image, simple drawing, masterpiece, best quality",
                    @"{""__value__"":[false,true]}",
                    "low quality, blurry",
                    @"{""__value__"":[false,true]}",
                    512, // 高
                    512, // 宽
                    1    // 图片数量
                );

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                    return false;
                }

                Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                // 检查任务状态
                await Task.Delay(3000);
                var status = SDXL.GetTaskStatus(taskId);
                Console.WriteLine($"📊 任务状态: {(status.Length > 100 ? status.Substring(0, 100) + "..." : status)}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ SDXL测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试IndexTts工作流
        /// </summary>
        private static async Task<bool> TestIndexTtsWorkflow()
        {
            try
            {
                Console.WriteLine("📋 API描述:");
                Console.WriteLine(IndexTts.GetApiDescription());
                Console.WriteLine();

                Console.WriteLine("🚀 执行工作流...");
                var taskId = await IndexTts.runWorkflow(
                    "test-audio.wav",
                    "",
                    "测试语音合成",
                    @"{""__value__"":[false,true]}"
                );

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                    return false;
                }

                Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                // 检查任务状态
                await Task.Delay(3000);
                var status = IndexTts.GetTaskStatus(taskId);
                Console.WriteLine($"📊 任务状态: {(status.Length > 100 ? status.Substring(0, 100) + "..." : status)}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ IndexTts测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试ImageIpToimage工作流
        /// </summary>
        private static async Task<bool> TestImageIpToimageWorkflow()
        {
            try
            {
                Console.WriteLine("📋 API描述:");
                Console.WriteLine(ImageIpToimage.GetApiDescription());
                Console.WriteLine();

                Console.WriteLine("🚀 执行工作流...");
                // 注意：这个工作流可能需要图像输入，我们使用默认参数
                var taskId = await ImageIpToimage.runWorkflow();

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                    return false;
                }

                Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                // 检查任务状态
                await Task.Delay(3000);
                var status = ImageIpToimage.GetTaskStatus(taskId);
                Console.WriteLine($"📊 任务状态: {(status.Length > 100 ? status.Substring(0, 100) + "..." : status)}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ImageIpToimage测试失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试GsvTtsWorkflow工作流
        /// </summary>
        private static async Task<bool> TestGsvTtsWorkflow()
        {
            try
            {
                Console.WriteLine("📋 API描述:");
                Console.WriteLine(GsvTtsWorkflow.GetApiDescription());
                Console.WriteLine();

                Console.WriteLine("🚀 执行工作流...");
                // 使用默认参数执行工作流
                var taskId = await GsvTtsWorkflow.runWorkflow();

                if (string.IsNullOrEmpty(taskId))
                {
                    Console.WriteLine("❌ 工作流执行失败，返回空任务ID");
                    return false;
                }

                Console.WriteLine($"✅ 工作流执行成功，任务ID: {taskId}");

                // 检查任务状态
                await Task.Delay(3000);
                var status = GsvTtsWorkflow.GetTaskStatus(taskId);
                Console.WriteLine($"📊 任务状态: {(status.Length > 100 ? status.Substring(0, 100) + "..." : status)}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ GsvTtsWorkflow测试失败: {ex.Message}");
                return false;
            }
        }
    }
}
