﻿using Newtonsoft.Json;
using System.Reflection;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
    public class XunFeiAISDK : AISDKProvider
    {
        private string url = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
        private string key = "dbe49b6864973dfc78eae7db994541b5.hJviOs7Q7OYFgPKz";
		public string Model;
        public XunFeiAISDK(string userMode)
		{
			this.Model = userMode;
		}

		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
        {
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "glm-4";
			}
			var data = new RequestData(Model, "user", messages);
			string jsonData = JsonConvert.SerializeObject(data);

			Instance.SendText(url, key, jsonData, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				callback(backText, bol);
			});
		}
		//根据输入的图像内容、视频内容和自然语言指令完成任务
		public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback) {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "glm-4v-plus";
            }
            try
            {
                //string videoPath = @"C:\Users\<USER>\Documents\WXWork\1688850840218820\Cache\Video\2024-10\2024_1029_163032.mp4_2024_1029_163032(1).mp4"; // 替换为你的文件路径                                                                                                                                           // 读取文件的所有字节
                //byte[] videoBytes = File.ReadAllBytes(videoPath);
                //// 将字节数组转换为Base64编码字符串
                //videoBase64Str = Convert.ToBase64String(videoBytes);
                if (videoBase64Str != null && videoBase64Str != "")
                {
                    VideoOrImageInfoData infoData = new VideoOrImageInfoData();
                    infoData.model = Model;
                    infoData.messages = new List<MessageData>();
                    MessageData messageData = new MessageData();
                    messageData.role = "user";
                    messageData.content = new List<ContentData>();

                    ContentData conten1 = new ContentData();
                    conten1.type = "video_url";
                    VideoOrImageBase64Str urldata = new VideoOrImageBase64Str();
                    urldata.url = videoBase64Str;
                    conten1.video_url = urldata;
                    conten1.text = "";
                    messageData.content.Add(conten1);

                    ContentData conten2 = new ContentData();
                    conten2.type = "text";
                    conten2.text = requestDescrib;//请仔细描述这个视频
                    conten2.video_url = null;
                    messageData.content.Add(conten2);
                    infoData.messages.Add(messageData);
                    string jsonString = JsonConvert.SerializeObject(infoData);
                    Instance.SendJson(url, key, jsonString, (backText, bol) =>
                    {
                        // UnityEngine.Debug.Log(backText);
                        if (!bol)
                        {
                            backText = this.Model + ": " + backText;
                        }
                        callback(backText, bol);
                    });
                }
                else {
                    string backText = this.Model + ": 请上传正确的图片或视频数据";
                    callback(backText, false);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error: " + ex.Message);
            }
        }

        public void ChatWithAi(string prompt, Action<string, bool> callback)
        {
            List<Message> messages = new List<Message>();
            messages.Add(new Message { role = "user", content = prompt });
            if (string.IsNullOrEmpty(Model))
            {
                Model = "qwen-max";
            }
            var data = new KuaiShuData(Model, "user", messages, true);
            string jsonData = JsonConvert.SerializeObject(data);

            Instance.SendText(url, key, jsonData, (backText, bol) =>
            {
                callback(backText, bol);
            });
        }
        public async Task<AiChatBase> KernelFunAsync()
        {
            return null;
        }
    }
}
