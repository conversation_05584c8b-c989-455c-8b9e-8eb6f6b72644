using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Linq;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI工作流执行监控器
    /// </summary>
    public class ComfyUIMonitor
    {
        private static ComfyUIMonitor? _instance;
        public static ComfyUIMonitor Instance => _instance ??= new ComfyUIMonitor();

        private readonly HttpClient _httpClient;
        private readonly Dictionary<string, WorkflowMonitorInfo> _activeMonitors;
        private readonly ComfyUILogger _logger;

        private ComfyUIMonitor()
        {
            _httpClient = new HttpClient();
            _activeMonitors = new Dictionary<string, WorkflowMonitorInfo>();
            _logger = ComfyUILogger.Instance;
        }

        /// <summary>
        /// 开始监控工作流执行
        /// </summary>
        public async Task StartMonitoring(string taskId, string promptId, string serverUrl, string workflowJson,
            string serverId = "", string workflowId = "", string workflowName = "", object inputParameters = null)
        {
            var monitorInfo = new WorkflowMonitorInfo
            {
                TaskId = taskId,
                PromptId = promptId,
                ServerUrl = serverUrl,
                WorkflowJson = workflowJson,
                StartTime = DateTime.Now,
                IsActive = true,
                ServerId = serverId,
                WorkflowId = workflowId,
                WorkflowName = workflowName
            };

            // 创建工作流日志记录
            try
            {
                monitorInfo.LogId = await ComfyUILogService.CreateWorkflowLogAsync(
                    taskId, serverId, workflowId, workflowName, inputParameters);

                if (!string.IsNullOrEmpty(monitorInfo.LogId))
                {
                    // 更新状态为提交中
                    await ComfyUILogService.UpdateWorkflowStatusAsync(
                        monitorInfo.LogId, 1, "工作流已提交，等待执行", promptId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"创建工作流日志失败: {ex.Message}");
            }

            _activeMonitors[promptId] = monitorInfo;

            _logger.LogInfo($"开始监控工作流执行 [任务: {taskId}] [Prompt: {promptId}] [日志: {monitorInfo.LogId}]");

            // 解析工作流节点信息
            try
            {
                var workflow = JsonConvert.DeserializeObject<JObject>(workflowJson);
                if (workflow != null)
                {
                    monitorInfo.Nodes = new Dictionary<string, NodeInfo>();
                    foreach (var node in workflow)
                    {
                        var nodeData = node.Value as JObject;
                        var nodeInfo = new NodeInfo
                        {
                            NodeId = node.Key,
                            ClassType = nodeData?["class_type"]?.ToString() ?? "Unknown",
                            Inputs = nodeData?["inputs"] as JObject,
                            Status = "Pending"
                        };
                        monitorInfo.Nodes[node.Key] = nodeInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"解析工作流节点失败: {ex.Message}");
            }

            // 启动监控任务
            _ = Task.Run(() => MonitorWorkflowExecution(monitorInfo));
        }

        /// <summary>
        /// 监控工作流执行过程
        /// </summary>
        private async Task MonitorWorkflowExecution(WorkflowMonitorInfo monitorInfo)
        {
            var lastStatus = "";
            var nodeStartTimes = new Dictionary<string, DateTime>();
            var workflowCompleted = false;

            try
            {
                while (monitorInfo.IsActive && !workflowCompleted)
                {
                    // 查询队列状态
                    await CheckQueueStatus(monitorInfo);

                    // 查询历史状态
                    var historyData = await GetHistoryData(monitorInfo);
                    if (historyData != null)
                    {
                        var currentStatus = historyData["status"]?.ToString() ?? "";

                        // 状态变化时记录日志
                        if (currentStatus != lastStatus && !string.IsNullOrEmpty(currentStatus))
                        {
                            _logger.LogInfo($"工作流状态变化: {lastStatus} -> {historyData} [任务: {monitorInfo.TaskId}]");
                            lastStatus = currentStatus;
                        }

                        // 检查节点执行状态
                        await CheckNodeExecution(monitorInfo, historyData, nodeStartTimes);

                        // 检查是否完成
                        if (currentStatus == "success" || currentStatus == "error" ||
                            historyData["outputs"] != null)
                        {
                            await HandleWorkflowCompletion(monitorInfo, historyData);
                            workflowCompleted = true;
                            break;
                        }
                    }

                    // 等待一段时间再次检查
                    await Task.Delay(2000);

                    // 超时检查 (10分钟)
                    if (DateTime.Now - monitorInfo.StartTime > TimeSpan.FromMinutes(10))
                    {
                        _logger.LogWarning($"工作流监控超时 [任务: {monitorInfo.TaskId}]");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"监控工作流执行时发生错误: {ex.Message}");
            }
            finally
            {
                monitorInfo.IsActive = false;
                _activeMonitors.Remove(monitorInfo.PromptId);
                _logger.LogInfo($"工作流监控结束 [任务: {monitorInfo.TaskId}] [Prompt: {monitorInfo.PromptId}]");
            }
        }

        /// <summary>
        /// 检查队列状态
        /// </summary>
        private async Task CheckQueueStatus(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/queue";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var queueData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    var running = queueData?["queue_running"] as JArray;
                    var pending = queueData?["queue_pending"] as JArray;
                    
                    var totalInQueue = (pending?.Count ?? 0) + (running?.Count ?? 0);
                    var position = FindQueuePosition(monitorInfo.PromptId, running, pending);

                    if (position >= 0)
                    {
                        _logger.LogQueueStatus(monitorInfo.TaskId, position, totalInQueue);

                        // 更新日志中的队列状态
                        if (!string.IsNullOrEmpty(monitorInfo.LogId))
                        {
                            var statusDesc = position == 0 ? "工作流正在执行" : $"工作流在队列中等待，位置: {position}";
                            var status = position == 0 ? 3 : 2; // 3:执行中, 2:队列中
                            await ComfyUILogService.UpdateWorkflowStatusAsync(
                                monitorInfo.LogId, status, statusDesc, null, position);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询队列状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取历史数据
        /// </summary>
        private async Task<JObject?> GetHistoryData(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/history/{monitorInfo.PromptId}";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var historyData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    if (historyData?[monitorInfo.PromptId] != null)
                    {
                        return historyData[monitorInfo.PromptId] as JObject;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询历史数据失败: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 检查节点执行状态
        /// </summary>
        private async Task CheckNodeExecution(WorkflowMonitorInfo monitorInfo, JObject historyData, Dictionary<string, DateTime> nodeStartTimes)
        {
            try
            {
                // 检查执行消息
                var status = historyData["status"] as JObject;
                if (status != null)
                {
                    var messages = status["messages"] as JArray;
                    if (messages != null && monitorInfo.Nodes != null)
                    {
                        await ProcessExecutionMessages(monitorInfo, messages, nodeStartTimes);
                    }
                }

                // 检查输出节点（最终完成的节点）
                var outputs = historyData["outputs"] as JObject;
                if (outputs != null && monitorInfo.Nodes != null)
                {
                    foreach (var output in outputs)
                    {
                        var nodeId = output.Key;
                        if (monitorInfo.Nodes.ContainsKey(nodeId))
                        {
                            var nodeInfo = monitorInfo.Nodes[nodeId];

                            // 如果节点还没完成，标记为完成
                            if (nodeInfo.Status != "Completed")
                            {
                                if (nodeInfo.Status == "Pending")
                                {
                                    // 节点被缓存，直接完成
                                    nodeStartTimes[nodeId] = DateTime.Now;
                                    _logger.LogNodeStart(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, nodeInfo.ClassType, nodeInfo.Inputs);
                                }

                                nodeInfo.Status = "Completed";
                                var duration = DateTime.Now - (nodeStartTimes.ContainsKey(nodeId) ? nodeStartTimes[nodeId] : DateTime.Now);
                                _logger.LogNodeComplete(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, output.Value as JObject, duration);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"检查节点执行状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理执行消息
        /// </summary>
        private async Task ProcessExecutionMessages(WorkflowMonitorInfo monitorInfo, JArray messages, Dictionary<string, DateTime> nodeStartTimes)
        {
            var processedNodes = new HashSet<string>();
            var totalNodes = monitorInfo.Nodes?.Count ?? 0;
            var completedNodes = 0;

            foreach (var message in messages)
            {
                if (message is JArray msgArray && msgArray.Count >= 2)
                {
                    var msgType = msgArray[0]?.ToString();
                    var msgData = msgArray[1] as JObject;

                    switch (msgType)
                    {
                        case "execution_start":
                            await HandleExecutionStart(monitorInfo, msgData);
                            break;

                        case "execution_cached":
                            await HandleExecutionCached(monitorInfo, msgData, nodeStartTimes, processedNodes);
                            break;

                        case "executing":
                            await HandleNodeExecuting(monitorInfo, msgData, nodeStartTimes, processedNodes);
                            break;

                        case "execution_success":
                            await HandleExecutionSuccess(monitorInfo, msgData);
                            break;

                        case "execution_error":
                            await HandleExecutionError(monitorInfo, msgData);
                            break;
                    }
                }
            }

            // 计算并显示总体进度
            if (monitorInfo.Nodes != null)
            {
                completedNodes = monitorInfo.Nodes.Values.Count(n => n.Status == "Completed" || n.Status == "Cached");
                var progress = totalNodes > 0 ? (int)((double)completedNodes / totalNodes * 100) : 0;

                if (completedNodes > 0 && completedNodes < totalNodes)
                {
                    _logger.LogWorkflowProgress(monitorInfo.TaskId, completedNodes, totalNodes, progress);
                }
            }
        }

        /// <summary>
        /// 处理执行开始消息
        /// </summary>
        private async Task HandleExecutionStart(WorkflowMonitorInfo monitorInfo, JObject? msgData)
        {
            if (msgData != null)
            {
                var promptId = msgData["prompt_id"]?.ToString();
                var timestamp = msgData["timestamp"]?.ToObject<long>();

                _logger.LogInfo($"🚀 工作流开始执行 [任务: {monitorInfo.TaskId}] [Prompt: {promptId}]");

                // 更新日志状态为执行中
                if (!string.IsNullOrEmpty(monitorInfo.LogId))
                {
                    await ComfyUILogService.UpdateWorkflowStatusAsync(
                        monitorInfo.LogId, 3, "工作流开始执行", promptId);
                }

                if (monitorInfo.Nodes != null)
                {
                    _logger.LogInfo($"📊 工作流总节点数: {monitorInfo.Nodes.Count}");

                    // 更新总节点数到日志
                    if (!string.IsNullOrEmpty(monitorInfo.LogId))
                    {
                        await ComfyUILogService.UpdateExecutionProgressAsync(
                            monitorInfo.LogId, 0, null, null, null, monitorInfo.Nodes.Count);
                    }

                    foreach (var node in monitorInfo.Nodes.Values)
                    {
                        _logger.LogInfo($"   📋 节点 {node.NodeId}: {node.ClassType}");
                    }
                }
            }
        }

        /// <summary>
        /// 处理节点缓存消息
        /// </summary>
        private async Task HandleExecutionCached(WorkflowMonitorInfo monitorInfo, JObject? msgData, Dictionary<string, DateTime> nodeStartTimes, HashSet<string> processedNodes)
        {
            if (msgData != null && monitorInfo.Nodes != null)
            {
                var nodes = msgData["nodes"] as JArray;
                if (nodes != null)
                {
                    foreach (var nodeToken in nodes)
                    {
                        var nodeId = nodeToken?.ToString();
                        if (!string.IsNullOrEmpty(nodeId) && monitorInfo.Nodes.ContainsKey(nodeId) && !processedNodes.Contains(nodeId))
                        {
                            var nodeInfo = monitorInfo.Nodes[nodeId];
                            nodeInfo.Status = "Cached";
                            processedNodes.Add(nodeId);

                            _logger.LogInfo($"💾 节点被缓存跳过 [任务: {monitorInfo.TaskId}]");
                            _logger.LogInfo($"   🏷️  节点ID: {nodeId}");
                            _logger.LogInfo($"   📝 节点名称: {nodeInfo.ClassType}");
                            _logger.LogInfo($"   ⏰ 时间: {DateTime.Now:HH:mm:ss.fff}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理节点执行消息
        /// </summary>
        private async Task HandleNodeExecuting(WorkflowMonitorInfo monitorInfo, JObject? msgData, Dictionary<string, DateTime> nodeStartTimes, HashSet<string> processedNodes)
        {
            if (msgData != null && monitorInfo.Nodes != null)
            {
                var nodeId = msgData["node"]?.ToString();
                if (!string.IsNullOrEmpty(nodeId) && monitorInfo.Nodes.ContainsKey(nodeId))
                {
                    var nodeInfo = monitorInfo.Nodes[nodeId];

                    if (!processedNodes.Contains(nodeId))
                    {
                        // 节点开始执行
                        nodeInfo.Status = "Running";
                        nodeStartTimes[nodeId] = DateTime.Now;
                        processedNodes.Add(nodeId);

                        _logger.LogNodeStart(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, nodeInfo.ClassType, nodeInfo.Inputs);

                        // 更新执行进度到日志
                        if (!string.IsNullOrEmpty(monitorInfo.LogId) && monitorInfo.Nodes != null)
                        {
                            var completedNodes = monitorInfo.Nodes.Values.Count(n => n.Status == "Completed" || n.Status == "Cached");
                            var progress = monitorInfo.Nodes.Count > 0 ? (int)((double)completedNodes / monitorInfo.Nodes.Count * 100) : 0;

                            await ComfyUILogService.UpdateExecutionProgressAsync(
                                monitorInfo.LogId, progress, nodeId, nodeInfo.ClassType, nodeInfo.ClassType);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 处理执行成功消息
        /// </summary>
        private async Task HandleExecutionSuccess(WorkflowMonitorInfo monitorInfo, JObject? msgData)
        {
            if (msgData != null)
            {
                var promptId = msgData["prompt_id"]?.ToString();
                _logger.LogInfo($"✅ 工作流执行成功 [任务: {monitorInfo.TaskId}] [Prompt: {promptId}]");
            }
        }

        /// <summary>
        /// 处理执行错误消息
        /// </summary>
        private async Task HandleExecutionError(WorkflowMonitorInfo monitorInfo, JObject? msgData)
        {
            if (msgData != null)
            {
                var promptId = msgData["prompt_id"]?.ToString();
                var error = msgData["exception_message"]?.ToString() ?? "未知错误";
                _logger.LogError($"❌ 工作流执行失败 [任务: {monitorInfo.TaskId}] [Prompt: {promptId}]: {error}");
            }
        }

        /// <summary>
        /// 处理工作流完成
        /// </summary>
        private async Task HandleWorkflowCompletion(WorkflowMonitorInfo monitorInfo, JObject historyData)
        {
            var success = historyData["status"]?.ToString() == "success" || historyData["outputs"] != null;
            var duration = DateTime.Now - monitorInfo.StartTime;
            var outputFiles = new List<string>();
            var downloadedFiles = new List<string>();

            _logger.LogInfo($"开始处理工作流完成 [任务: {monitorInfo.TaskId}]");
            _logger.LogInfo($"历史数据结构: {historyData}");

            // 收集输出文件
            try
            {
                var outputs = historyData["outputs"] as JObject;
                _logger.LogInfo($"输出数据: {outputs}");

                if (outputs != null)
                {
                    _logger.LogInfo($"找到输出数据，节点数量: {outputs.Count}");

                    foreach (var output in outputs)
                    {
                        _logger.LogInfo($"处理输出节点: {output.Key}");
                        var outputData = output.Value as JObject;
                        if (outputData != null)
                        {
                            _logger.LogInfo($"节点 {output.Key} 输出数据: {outputData}");

                            foreach (var item in outputData)
                            {
                                _logger.LogInfo($"处理输出项: {item.Key} = {item.Value}");

                                if (item.Value is JArray array)
                                {
                                    _logger.LogInfo($"找到文件数组，文件数量: {array.Count}");

                                    foreach (var file in array)
                                    {
                                        _logger.LogInfo($"处理文件: {file}");

                                        if (file is JObject fileObj && fileObj["filename"] != null)
                                        {
                                            var filename = fileObj["filename"].ToString();
                                            outputFiles.Add(filename);
                                            _logger.LogInfo($"找到输出文件: {filename}");

                                            // 下载文件到本地
                                            var downloadedPath = await DownloadOutputFile(monitorInfo, filename, fileObj);
                                            if (!string.IsNullOrEmpty(downloadedPath))
                                            {
                                                downloadedFiles.Add(downloadedPath);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("没有找到输出数据");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"收集输出文件失败: {ex.Message}");
                _logger.LogError($"异常堆栈: {ex.StackTrace}");
            }

            _logger.LogWorkflowComplete(monitorInfo.TaskId, success, duration, outputFiles);

            // 记录工作流完成到数据库
            if (!string.IsNullOrEmpty(monitorInfo.LogId))
            {
                try
                {
                    // 记录工作流完成
                    await ComfyUILogService.CompleteWorkflowAsync(
                        monitorInfo.LogId, success, success ? null : "工作流执行失败");

                    // 记录文件下载信息
                    if (monitorInfo.DownloadDetails.Count > 0)
                    {
                        await ComfyUILogService.RecordFileDownloadAsync(monitorInfo.LogId, monitorInfo.DownloadDetails);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"记录工作流完成信息到数据库失败: {ex.Message}");
                }
            }

            // 记录批量下载结果
            if (outputFiles.Count > 0)
            {
                var successCount = downloadedFiles.Count;
                var failedCount = outputFiles.Count - successCount;
                _logger.LogBatchDownloadComplete(monitorInfo.TaskId, outputFiles.Count, successCount, failedCount, downloadedFiles);
            }
            else
            {
                _logger.LogWarning($"没有找到任何输出文件 [任务: {monitorInfo.TaskId}]");
            }
        }

        /// <summary>
        /// 下载输出文件到本地
        /// </summary>
        private async Task<string> DownloadOutputFile(WorkflowMonitorInfo monitorInfo, string filename, JObject fileObj)
        {
            var startTime = DateTime.Now;
            var config = ComfyUIDownloadConfig.Instance;
            var downloadInfo = new FileDownloadInfo
            {
                FileName = filename,
                DownloadTime_DateTime = startTime,
                Success = false
            };

            _logger.LogInfo($"开始下载文件: {filename}");
            _logger.LogInfo($"文件对象: {fileObj}");

            try
            {
                // 确定文件类型和扩展名
                var fileExtension = Path.GetExtension(filename).ToLower();
                var fileType = GetFileType(fileExtension);

                _logger.LogInfo($"文件扩展名: {fileExtension}, 文件类型: {fileType}");

                // 检查文件类型是否被支持
                if (!config.IsFileTypeSupported(fileExtension))
                {
                    _logger.LogInfo($"跳过不支持的文件类型: {filename} ({fileType})");
                    return "";
                }

                // 创建本地下载目录
                var downloadDir = config.GetDownloadPath(monitorInfo.TaskId, fileType, filename);
                Directory.CreateDirectory(downloadDir);
                _logger.LogInfo($"下载目录: {downloadDir}");

                // 构建下载URL
                var downloadUrl = $"{monitorInfo.ServerUrl}/view";

                // 从fileObj中获取文件信息
                var subfolder = fileObj["subfolder"]?.ToString() ?? "";
                var type = fileObj["type"]?.ToString() ?? "output";

                _logger.LogInfo($"文件信息 - subfolder: '{subfolder}', type: '{type}'");

                // 构建完整的下载URL
                var fullUrl = $"{downloadUrl}?filename={Uri.EscapeDataString(filename)}&subfolder={Uri.EscapeDataString(subfolder)}&type={type}";
                downloadInfo.FileUrl = fullUrl;

                // 记录下载开始
                _logger.LogFileDownloadStart(monitorInfo.TaskId, filename, fileType, fullUrl);

                // 下载文件
                using var response = await _httpClient.GetAsync(fullUrl);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsByteArrayAsync();

                    // 计算文件大小
                    var fileSizeMB = content.Length / (1024.0 * 1024.0);

                    // 检查文件大小限制
                    if (!config.IsFileSizeAllowed(content.Length))
                    {
                        var errorMsg = $"文件大小 {fileSizeMB:F2} MB 超过限制 {config.MaxFileSizeMB} MB";
                        _logger.LogFileDownloadError(monitorInfo.TaskId, filename, errorMsg);

                        downloadInfo.ErrorMessage = errorMsg;
                        monitorInfo.DownloadDetails.Add(downloadInfo);
                        return "";
                    }

                    // 生成最终文件名
                    var finalFilename = config.GenerateFilename(filename);
                    var localFilePath = Path.Combine(downloadDir, finalFilename);

                    // 写入文件
                    await File.WriteAllBytesAsync(localFilePath, content);

                    var duration = DateTime.Now - startTime;
                    var fullPath = Path.GetFullPath(localFilePath);

                    // 记录下载成功
                    _logger.LogFileDownloadSuccess(monitorInfo.TaskId, filename, fullPath, fileSizeMB, duration);

                    // 更新下载信息
                    downloadInfo.LocalPath = fullPath;
                    downloadInfo.FileSize = content.Length;
                    downloadInfo.DownloadTime = duration.TotalSeconds;
                    downloadInfo.Success = true;
                    monitorInfo.DownloadDetails.Add(downloadInfo);

                    return localFilePath;
                }
                else
                {
                    var error = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                    _logger.LogFileDownloadError(monitorInfo.TaskId, filename, error);

                    downloadInfo.ErrorMessage = error;
                    downloadInfo.DownloadTime = (DateTime.Now - startTime).TotalSeconds;
                    monitorInfo.DownloadDetails.Add(downloadInfo);
                    return "";
                }
            }
            catch (Exception ex)
            {
                _logger.LogFileDownloadError(monitorInfo.TaskId, filename, ex.Message);

                downloadInfo.ErrorMessage = ex.Message;
                downloadInfo.DownloadTime = (DateTime.Now - startTime).TotalSeconds;
                monitorInfo.DownloadDetails.Add(downloadInfo);
                return "";
            }
        }

        /// <summary>
        /// 根据文件扩展名确定文件类型
        /// </summary>
        private string GetFileType(string extension)
        {
            return extension.ToLower() switch
            {
                // 图片文件
                ".png" or ".jpg" or ".jpeg" or ".gif" or ".bmp" or ".tiff" or ".webp" or ".svg" or ".ico" or ".tga" or ".exr" or ".hdr" => "图片",

                // 视频文件
                ".mp4" or ".avi" or ".mov" or ".wmv" or ".flv" or ".mkv" or ".webm" or ".m4v" or ".3gp" or ".ogv" or ".ts" or ".mts" => "视频",

                // 3D模型文件
                ".fbx" or ".obj" or ".dae" or ".3ds" or ".blend" or ".max" or ".ma" or ".mb" or ".c4d" or ".lwo" or ".x3d" or ".ply" or ".stl" or ".gltf" or ".glb" => "3D模型",

                // 音频文件
                ".wav" or ".mp3" or ".flac" or ".aac" or ".ogg" or ".wma" or ".m4a" or ".opus" or ".aiff" or ".au" => "音频",

                // 文本文件
                ".txt" or ".json" or ".xml" or ".csv" or ".yaml" or ".yml" or ".ini" or ".cfg" or ".conf" or ".log" or ".md" or ".rst" => "文本",

                // 压缩包
                ".zip" or ".rar" or ".7z" or ".tar" or ".gz" or ".bz2" or ".xz" or ".lzma" or ".cab" or ".iso" => "压缩包",

                // 文档
                ".pdf" or ".doc" or ".docx" or ".xls" or ".xlsx" or ".ppt" or ".pptx" or ".odt" or ".ods" or ".odp" => "文档",

                // 可执行文件
                ".exe" or ".msi" or ".dmg" or ".pkg" or ".deb" or ".rpm" or ".app" or ".apk" => "可执行文件",

                // 字体文件
                ".ttf" or ".otf" or ".woff" or ".woff2" or ".eot" => "字体",

                // 数据文件
                ".db" or ".sqlite" or ".sql" or ".mdb" or ".accdb" => "数据库",

                // 脚本文件
                ".py" or ".js" or ".ts" or ".php" or ".rb" or ".pl" or ".sh" or ".bat" or ".ps1" => "脚本",

                // 其他常见格式
                ".psd" or ".ai" or ".eps" or ".sketch" => "设计文件",
                ".unity" or ".unitypackage" => "Unity资源",
                ".uasset" or ".umap" => "Unreal资源",

                _ => "未知类型"
            };
        }

        /// <summary>
        /// 查找队列位置
        /// </summary>
        private int FindQueuePosition(string promptId, JArray? running, JArray? pending)
        {
            // 检查运行中的队列
            if (running != null)
            {
                for (int i = 0; i < running.Count; i++)
                {
                    if (running[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return 0; // 正在运行
                    }
                }
            }

            // 检查等待中的队列
            if (pending != null)
            {
                for (int i = 0; i < pending.Count; i++)
                {
                    if (pending[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return i + 1; // 等待位置
                    }
                }
            }

            return -1; // 未找到
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring(string promptId)
        {
            if (_activeMonitors.ContainsKey(promptId))
            {
                _activeMonitors[promptId].IsActive = false;
                _activeMonitors.Remove(promptId);
                _logger.LogInfo($"停止监控工作流 [Prompt: {promptId}]");
            }
        }

        /// <summary>
        /// 获取活动监控数量
        /// </summary>
        public int GetActiveMonitorCount()
        {
            return _activeMonitors.Count;
        }
    }

    /// <summary>
    /// 工作流监控信息
    /// </summary>
    public class WorkflowMonitorInfo
    {
        public string TaskId { get; set; } = "";
        public string PromptId { get; set; } = "";
        public string ServerUrl { get; set; } = "";
        public string WorkflowJson { get; set; } = "";
        public DateTime StartTime { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, NodeInfo>? Nodes { get; set; }
        public string LogId { get; set; } = ""; // 关联的日志ID
        public string ServerId { get; set; } = ""; // 服务器ID
        public string WorkflowId { get; set; } = ""; // 工作流ID
        public string WorkflowName { get; set; } = ""; // 工作流名称
        public List<FileDownloadInfo> DownloadDetails { get; set; } = new List<FileDownloadInfo>(); // 下载详情
    }

    /// <summary>
    /// 节点信息
    /// </summary>
    public class NodeInfo
    {
        public string NodeId { get; set; } = "";
        public string ClassType { get; set; } = "";
        public JObject? Inputs { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Running, Completed, Cached, Error
    }
}
