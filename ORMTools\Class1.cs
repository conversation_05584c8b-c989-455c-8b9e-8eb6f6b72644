﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Xml;

namespace ORMTools
{
    /// <summary>
    /// ORM实体类反射工具，用于生成CSV文件
    /// </summary>
    public class ORMEntityAnalyzer
    {
        /// <summary>
        /// 分析指定的ORM实体类并生成CSV文件
        /// </summary>
        /// <param name="entityType">要分析的实体类类型</param>
        /// <param name="outputDirectory">输出目录，默认为当前目录</param>
        /// <returns>生成的CSV文件路径</returns>
        public static string GenerateEntityCsv(Type entityType, string outputDirectory = ".")
        {
            if (entityType == null)
                throw new ArgumentNullException(nameof(entityType));

            // 确保输出目录存在
            if (!Directory.Exists(outputDirectory))
                Directory.CreateDirectory(outputDirectory);

            // 获取类的所有公共属性
            var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite) // 只获取可读写的属性
                .ToArray();

            // 准备CSV数据
            var propertyNames = new List<string>();
            var propertyTypes = new List<string>();
            var propertyComments = new List<string>();

            // 加载XML文档注释
            var xmlComments = LoadXmlDocumentation(entityType.Assembly);

            foreach (var property in properties)
            {
                // 获取属性名
                propertyNames.Add(property.Name);

                // 获取属性类型
                string typeString = GetTypeString(property.PropertyType);
                propertyTypes.Add(typeString);

                // 获取属性注释
                string comment = GetPropertyComment(property, xmlComments);
                propertyComments.Add(comment);
            }

            // 生成CSV内容
            var csvContent = new StringBuilder();

            // 第一行：属性名称
            csvContent.AppendLine(string.Join(",", propertyNames.Select(EscapeCsvField)));

            // 第二行：数据类型
            csvContent.AppendLine(string.Join(",", propertyTypes.Select(EscapeCsvField)));

            // 第三行：注释内容
            csvContent.AppendLine(string.Join(",", propertyComments.Select(EscapeCsvField)));

            // 生成文件路径
            string fileName = $"{entityType.Name}.csv";
            string filePath = Path.Combine(outputDirectory, fileName);

            // 写入文件
            File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);

            Console.WriteLine($"已生成CSV文件: {filePath}");
            return filePath;
        }

        /// <summary>
        /// 获取类型的字符串表示，特别处理数组和List类型
        /// </summary>
        /// <param name="type">属性类型</param>
        /// <returns>类型字符串</returns>
        private static string GetTypeString(Type type)
        {
            // 处理可空类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                var underlyingType = Nullable.GetUnderlyingType(type);
                return GetTypeString(underlyingType) + "?";
            }

            // 处理数组类型
            if (type.IsArray)
            {
                var elementType = type.GetElementType();
                return GetTypeString(elementType) + "[]";
            }

            // 处理List类型
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>))
            {
                var elementType = type.GetGenericArguments()[0];
                return GetTypeString(elementType) + "[]";
            }

            // 处理基本类型
            if (type == typeof(string)) return "string";
            if (type == typeof(int)) return "int";
            if (type == typeof(uint)) return "uint";
            if (type == typeof(long)) return "long";
            if (type == typeof(ulong)) return "ulong";
            if (type == typeof(short)) return "short";
            if (type == typeof(ushort)) return "ushort";
            if (type == typeof(byte)) return "byte";
            if (type == typeof(sbyte)) return "sbyte";
            if (type == typeof(bool)) return "bool";
            if (type == typeof(float)) return "float";
            if (type == typeof(double)) return "double";
            if (type == typeof(decimal)) return "decimal";
            if (type == typeof(DateTime)) return "DateTime";
            if (type == typeof(Guid)) return "Guid";

            // 其他类型返回类型名
            return type.Name;
        }

        /// <summary>
        /// 加载程序集的XML文档注释
        /// </summary>
        /// <param name="assembly">程序集</param>
        /// <returns>XML文档</returns>
        private static XmlDocument LoadXmlDocumentation(Assembly assembly)
        {
            try
            {
                string xmlPath = Path.ChangeExtension(assembly.Location, ".xml");
                if (File.Exists(xmlPath))
                {
                    var xmlDoc = new XmlDocument();
                    xmlDoc.Load(xmlPath);
                    return xmlDoc;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"无法加载XML文档: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 获取属性的注释内容
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <param name="xmlDoc">XML文档</param>
        /// <returns>注释内容</returns>
        private static string GetPropertyComment(PropertyInfo property, XmlDocument xmlDoc)
        {
            if (xmlDoc == null) return "";

            try
            {
                string memberName = $"P:{property.DeclaringType.FullName}.{property.Name}";
                var memberNode = xmlDoc.SelectSingleNode($"//member[@name='{memberName}']");

                if (memberNode != null)
                {
                    var summaryNode = memberNode.SelectSingleNode("summary");
                    if (summaryNode != null)
                    {
                        return summaryNode.InnerText.Trim().Replace("\n", " ").Replace("\r", "");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取属性 {property.Name} 注释时出错: {ex.Message}");
            }

            return "";
        }

        /// <summary>
        /// 转义CSV字段，处理包含逗号、引号和换行符的情况
        /// </summary>
        /// <param name="field">字段内容</param>
        /// <returns>转义后的字段</returns>
        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }
    }
}
