using System;
using System.IO;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// 手动测试SimpleWorkflowGenerator功能
    /// </summary>
    public class ManualTest
    {
        public static async Task TestWorkflowGeneration()
        {
            try
            {
                Console.WriteLine("=== 手动测试SimpleWorkflowGenerator ===");
                
                // 测试JSON文件路径
                var jsonFilePath = Path.Combine(Directory.GetCurrentDirectory(), "TestData", "cafelabs-test.json");
                
                if (!File.Exists(jsonFilePath))
                {
                    Console.WriteLine($"❌ 测试文件不存在: {jsonFilePath}");
                    
                    // 尝试创建测试数据目录
                    var testDataDir = Path.GetDirectoryName(jsonFilePath);
                    if (!Directory.Exists(testDataDir))
                    {
                        Directory.CreateDirectory(testDataDir);
                        Console.WriteLine($"创建测试数据目录: {testDataDir}");
                    }
                    return;
                }
                
                Console.WriteLine($"✅ 找到测试文件: {jsonFilePath}");
                
                // 直接调用生成方法
                await SimpleWorkflowGenerator.GenerateWorkflowAsync(jsonFilePath);
                
                // 检查生成结果
                var outputDir = Path.Combine(Directory.GetCurrentDirectory(), "ComfyuiGate");
                var outputFile = Path.Combine(outputDir, "CafelabsTest.cs");
                
                if (File.Exists(outputFile))
                {
                    Console.WriteLine($"✅ 成功生成文件: {outputFile}");
                    
                    var generatedCode = await File.ReadAllTextAsync(outputFile);
                    Console.WriteLine($"生成的代码长度: {generatedCode.Length} 字符");
                    
                    // 检查关键内容
                    bool hasRunWorkflow = generatedCode.Contains("runWorkflow");
                    bool hasInputParams = generatedCode.Contains("promt_") || generatedCode.Contains("fumianPromt_");
                    
                    Console.WriteLine($"包含runWorkflow方法: {(hasRunWorkflow ? "✅" : "❌")}");
                    Console.WriteLine($"包含input参数: {(hasInputParams ? "✅" : "❌")}");
                    
                    // 显示方法签名
                    var lines = generatedCode.Split('\n');
                    foreach (var line in lines)
                    {
                        if (line.Contains("runWorkflow"))
                        {
                            Console.WriteLine($"方法签名: {line.Trim()}");
                            break;
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"❌ 文件生成失败: {outputFile}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
