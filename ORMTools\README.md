# ORMTools - ORM实体类分析工具

ORMTools是一个用于分析ORM实体类并生成CSV文件的.NET库。它可以通过反射获取实体类的属性信息，包括属性名称、数据类型和注释内容，并将这些信息导出为CSV格式。

## 功能特性

- 🔍 **反射分析**: 通过反射获取类的所有公共属性
- 📝 **类型识别**: 智能识别各种数据类型，包括基本类型、可空类型、数组和List
- 💬 **注释提取**: 从XML文档注释中提取属性的说明信息
- 📊 **CSV导出**: 生成标准的CSV文件，便于在Excel等工具中查看
- 🎯 **特殊处理**: 对数组和List类型使用特殊格式（如`int[]`、`string[]`）

## 安装和使用

### 1. 作为库使用

将ORMTools项目添加到您的解决方案中，并在目标项目中添加引用：

```xml
<ProjectReference Include="..\ORMTools\ORMTools.csproj" />
```

### 2. 基本用法

```csharp
using ORMTools;

// 分析单个实体类
string csvPath = ORMEntityAnalyzer.GenerateEntityCsv(typeof(YourEntity), "output");

// 批量分析多个实体类
var entityTypes = new[] { typeof(Entity1), typeof(Entity2), typeof(Entity3) };
foreach (var entityType in entityTypes)
{
    ORMEntityAnalyzer.GenerateEntityCsv(entityType, "output");
}
```

### 3. 高级用法

```csharp
// 使用示例类中的方法
ORMToolsUsageExample.DemonstrateORMToolsUsage();
ORMToolsUsageExample.AnalyzeSingleEntity(typeof(YourEntity));
ORMToolsUsageExample.AnalyzeEntitiesInNamespace("YourNamespace");
```

## CSV文件格式

生成的CSV文件包含三行数据：

1. **第一行**: 属性名称
2. **第二行**: 数据类型
3. **第三行**: 注释内容

### 示例输出

```csv
Id,Name,Email,Age,IsActive,Tags,Scores
int,string,string,int,bool,string[],int[]
主键ID,用户名称,邮箱地址,年龄,是否激活,标签列表,分数数组
```

## 支持的数据类型

### 基本类型
- `string`, `int`, `uint`, `long`, `ulong`
- `short`, `ushort`, `byte`, `sbyte`
- `bool`, `float`, `double`, `decimal`
- `DateTime`, `Guid`

### 特殊类型
- **可空类型**: `int?` → `int?`
- **数组类型**: `int[]` → `int[]`
- **List类型**: `List<string>` → `string[]`
- **其他类型**: 显示类型名称

## 注释支持

ORMTools支持从XML文档注释中提取属性说明：

```csharp
public class ExampleEntity
{
    /// <summary>
    /// 用户的唯一标识符
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 用户的显示名称
    /// </summary>
    public string Name { get; set; }
}
```

**注意**: 要启用XML文档注释提取，需要在项目文件中添加：

```xml
<PropertyGroup>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
</PropertyGroup>
```

## 示例项目

项目包含完整的示例代码：

- `TestProgram.cs`: 基本使用示例
- `ORMToolsUsageExample.cs`: 高级使用示例
- `ORMToolsDemo.cs`: 演示程序

运行示例：

```bash
dotnet run --project ORMTools
```

## API参考

### ORMEntityAnalyzer类

#### GenerateEntityCsv方法

```csharp
public static string GenerateEntityCsv(Type entityType, string outputDirectory = ".")
```

**参数**:
- `entityType`: 要分析的实体类类型
- `outputDirectory`: 输出目录，默认为当前目录

**返回值**: 生成的CSV文件的完整路径

**异常**:
- `ArgumentNullException`: 当entityType为null时抛出

## 注意事项

1. **属性过滤**: 只分析公共的、可读写的实例属性
2. **文件编码**: CSV文件使用UTF-8编码保存
3. **文件命名**: CSV文件名为`{类名}.csv`
4. **CSV转义**: 自动处理包含逗号、引号和换行符的字段
5. **XML文档**: 需要启用XML文档生成才能提取注释

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
