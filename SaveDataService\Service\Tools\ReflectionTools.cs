﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace GameServer.GameService.Tools
{
    /// <summary>
    /// 反射工具 尽量少用
    /// </summary>
    class ReflectionTools
    {
        /// <summary>
        /// 通过表名字符串添加数据
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="data"></param>
        public static void addDataByTableName(string tableName, object data)
        {
            var table = getTableType(tableName);
            try
            {
                addData(data, table);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }
        /// <summary>
        /// 通过字符串获取
        /// </summary>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private static Type getTableType(string tableName)
        {
            dynamic q = from t in Assembly.GetExecutingAssembly().GetTypes()
                        where t.IsClass && t.Name == tableName
                        select t;
            Type tableType = null;
            foreach (var item in q)
            {
                if (item != null)
                {
                    tableType = item;
                    break;
                }
            }
            return tableType;
        }
        private static void addData(dynamic DataOrg, Type tableType)
        {
            if (tableType == null)
            {
                Console.WriteLine("傻逼策划填配置表的时候填错了！！！！自己检查表里面的表名");
                return;
            }
            if (DataOrg==null)
            {
                Console.WriteLine("ReflectionTools→addData出错，DataOrg为空");
                return;
            }
            tableType.InvokeMember("add", System.Reflection.BindingFlags.InvokeMethod | System.Reflection.BindingFlags.Static
                | System.Reflection.BindingFlags.Public, null, null, new object[] { DataOrg });
        }
    }
}
