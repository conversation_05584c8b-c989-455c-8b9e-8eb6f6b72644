﻿using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;
using static System.Net.Mime.MediaTypeNames;

namespace AISdk.Manage
{
    internal class AudioManager
    {
        private static AudioManager _instance;
        public static AudioManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new AudioManager();
                }
                return _instance;
            }
        }
        public List<string> audioTextLIst =new List<string>();
        private string AddAudioText = "";
        public int Index = 0;
        public void addAudioTextLIst(string text, Boolean bol)
        {
            if (text.Length > 384)
            {
                audioTextLIst = new List<string>();
                var start = 0;
                while (start < text.Length)
                {
                    int end = Math.Min(start + 384, text.Length);
                    audioTextLIst.Add(text.Substring(start, end - start));
                    start = end;
                }

                SetText(audioTextLIst[0], 0);
            }
            else
            {
                AddAudioText += text;
                if (!bol)
                {
                    audioTextLIst.Add(AddAudioText);
                    SetText(audioTextLIst[0], 0);
                }
                else
                {
                    if (AddAudioText.Length > 384)
                    {
                        audioTextLIst.Add(AddAudioText);
                        AddAudioText = "";
                    }
                }
            }
        }

        public void SetText(string inputText, int i)
        {
            string replaceText = Regex.Replace(inputText, @",:!?'", string.Empty, RegexOptions.IgnoreCase);

            Dictionary<string, string> data = new Dictionary<string, string>();
            data.Add("text", replaceText);
            data.Add("prompt", "");
            data.Add("voice", "seed_1397_restored_emb.pt");
            data.Add("speed", "5");
            PostHttps(data, i);
        }
        private static readonly HttpClient client = new HttpClient();
        private string http = "http://192.168.15.26:9966/tts";
        public async void PostHttps(Dictionary<string, string> formData, int i)
        {
           

            var wwwForm = new FormUrlEncodedContent(formData);

            using (HttpResponseMessage www = await client.PostAsync(http, wwwForm))
            {
                www.EnsureSuccessStatusCode();

                JObject json = JObject.Parse(await www.Content.ReadAsStringAsync());
                string url = json["url"].ToString();
                //Console.WriteLine(url);
                //AudioManagr.Instance.getClipHttp(url);
                i++;
                if (i < audioTextLIst.Count)
                {
                    SetText(audioTextLIst[i], i);
                }
                else
                {
                    Index = 0;
                    audioTextLIst = new List<string>();
                }
            }
        }
    }
}
