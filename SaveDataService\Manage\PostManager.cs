﻿using Aliyun.OSS;
using Azure;
using ExcelDataReader.Log;
using ExcelToData;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Net.Http.Headers;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using NPOI.OpenXmlFormats.Spreadsheet;
using SaveDataService.Server.Websocket;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Threading.Tasks;
using static Aliyun.OSS.Model.ListMultipartUploadsResult;

namespace SaveDataService.Manage
{
    internal class PostManager
    {
        public static void init()
        {

            var httpserver = new WebsocketService();
            httpserver.SetFailAction(on404);
            //httpserver.SetWebsocketAction("/wsapi", onWebsocketIn);
            //httpserver.SetHttpAction("/userInput", onUpload);
            //前置需求
            httpserver.SetHttpAction("/basetemplate", OnSetBaseTemplate);
            //用户输入生成游戏描述
            httpserver.SetHttpAction("/userinput", onUserInput);
            //生成世界观和游戏目标
            httpserver.SetHttpAction("/worldviewandtarget", onWorldviewAndTarget);
            //在模板的基础上，用户调整的游戏内容
            httpserver.SetHttpAction("/refreshgamebase", onRefreshGamebase);
            ////IGNlink 评测连接
            //httpserver.SetHttpAction("/ignreviewlink", onIGNreviewlink);
            ////游戏简介
            //httpserver.SetHttpAction("/gameintroduce", onGameIntroduce);
            //游戏章节
            httpserver.SetHttpAction("/chapter", onChapter);
            //角色设定 关键字
            httpserver.SetHttpAction("/characterkeys", onCharacterKeys);
            //场景设定 关键字
            httpserver.SetHttpAction("/scencekeys", onScenceKeys);
            //场景背景音乐 
            httpserver.SetHttpAction("/scencemusic", onScenceMusic);
            //场景音效
            httpserver.SetHttpAction("/scenceeffect", onScenceEffect);
            //天空盒子
            httpserver.SetHttpAction("/skybox", onSkybox);

            //策划案
            httpserver.SetHttpAction("/designcases", onDesignCases);

            httpserver.SetHttpAction("/reexceldb", onRefreshExcel);

            //流式输出
            httpserver.SetHttpAction("/streamoutput", onStreamOutput);

            //测试
            httpserver.SetHttpAction("/pause", onPausetask);
            httpserver.SetHttpAction("/resume", onResumetask);
            httpserver.SetHttpAction("/cancel", onCanceltask);

            var IS_LOG = Environment.GetEnvironmentVariable("IS_LOG") != "FALSE";
            //httpserver.Start(7777,7778, PatchUtil.ResRootPatch + "/WebsocketKey/9309951_dai.cafegame.cn.pfx", "rq1ghtek");
            httpserver.Start(7778);
        }
        static int postCount = 0;

        static async Task OnSetBaseTemplate(HttpContext context)
        {
            await order(context, ProcessType.basetemplate);
        }
        static async Task onUserInput(HttpContext context)
        {
            //order
            await order(context, ProcessType.userInput);
        }
        static async Task onWorldviewAndTarget(HttpContext context)
        {
            await order(context, ProcessType.worldvieAndTarget);
        }
        static async Task onRefreshGamebase(HttpContext context)
        {
            await order(context, ProcessType.refreshGamebase);
        }
        static async Task onIGNreviewlink(HttpContext context)
        {
            await order(context, ProcessType.IGNreviewlink);
        }
        static async Task onGameIntroduce(HttpContext context)
        {
            await order(context, ProcessType.GameIntroduce);
        }
        static async Task onChapter(HttpContext context)
        {
            await order(context, ProcessType.Chapter);
        }
        static async Task onCharacterKeys(HttpContext context)
        {
            await order(context, ProcessType.CharacterKeys);
        }
        static async Task onScenceKeys(HttpContext context)
        {
            await order(context, ProcessType.ScenceKeys);
        }
        static async Task onScenceMusic(HttpContext context)
        {
            await order(context, ProcessType.ScenceMusic);
        }
        static async Task onScenceEffect(HttpContext context)
        {
            await order(context, ProcessType.ScenceEffect);
        }
        static async Task onSkybox(HttpContext context)
        {
            await order(context, ProcessType.skybox);
        }
        static async Task onDesignCases(HttpContext context)
        {
            await order(context, ProcessType.designCases);
        }
        static async Task onRefreshExcel(HttpContext context)
        {

            ExcelDataManager.Instance.RefreshDatabaseTables();
        }
        static async Task onStreamOutput(HttpContext context)
        {
            await order(context, ProcessType.StreamOutput);
        }

        static async Task onPausetask(HttpContext context)
        {
            await order(context, ProcessType.pause);
        }
        static async Task onResumetask(HttpContext context)
        {
            await order(context, ProcessType.resume);
        }
        static async Task onCanceltask(HttpContext context)
        {
            await order(context, ProcessType.cancel);
        }



        static async Task order(HttpContext context, ProcessType processtype)
        {
            bool streamOutputBol = false;
            Console.WriteLine("api userlogin");
            postCount++;
            if (context.Request.Method == "OPTIONS")
            {
                context.Response.StatusCode = 200;
                context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                await context.Response.WriteAsync("");
            }
            else if (context.Request.Method == "POST")
            {
                string[] contentType = context.Request.ContentType.Split(';');
                if (contentType[0] == "application/json" || contentType[0] == "application/x-www-form-urlencoded")
                {
                    int filesize = (int)context.Request.ContentLength;
                    RequestBackInfo backInfo = null;
                    using (var file = new MemoryStream())
                    {
                        int peekpos = 0;
                        byte[] buf = new byte[4096];
                        while (peekpos < filesize)
                        {
                            try
                            {
                                int read = await context.Request.Body.ReadAsync(buf, 0, buf.Length);
                                peekpos += read;
                                if (read > 0)
                                {
                                    await file.WriteAsync(buf, 0, read);
                                }
                            }
                            catch (Exception err)
                            {
                                context.Response.StatusCode = 400;
                                await context.Response.WriteAsync(err.Message);
                                return;
                            }
                        }
                        string json = System.Text.Encoding.UTF8.GetString(file.ToArray());

                        RequestInfoData RequestInfo = JsonConvert.DeserializeObject<RequestInfoData>(json);
                        backInfo = new RequestBackInfo();

                        //context.Response.StatusCode = 200;
                        //context.Response.ContentType = "text/event-stream; charset=utf-8";
                        //context.Response.Headers.Add("Cache-Control", "no-cache");
                        //context.Response.Headers.Add("Connection", "keep-alive");
                        //context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                        switch (processtype)
                        {
                            case ProcessType.basetemplate:
                                backInfo.content = await DeepseekManager.Instance.SetBaseTemplate();
                                backInfo.recode = 200;
                                backInfo.result = "OK";
                                break;
                            case ProcessType.userInput:
                                //await DeepseekManager.Instance.SetBaseTemplate();
                                //backInfo.content = await DeepseekManager.Instance.SetGameBaseType(RequestInfo.userInputStr);
                                AI_Game_Output item1 = AI_Game_Output.getByid("10001");
                                if (item1 != null)
                                {
                                    if (item1.ifSse == true)
                                    {
                                        InputPromptData promptData1 = new InputPromptData();
                                        promptData1.systemPrompt = item1.systemPrompt;
                                        promptData1.userPrompt = RequestInfo.userInputStr;
                                        context.Response.StatusCode = 200;
                                        context.Response.ContentType = "text/event-stream; charset=utf-8";
                                        context.Response.Headers.Add("Cache-Control", "no-cache");
                                        context.Response.Headers.Add("Connection", "keep-alive");
                                        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                                        streamOutputBol = true;
                                        string result = await StreamResponseToClient(context, promptData1);
                                        switch (item1.priority)
                                        {
                                            case "JSON":
                                                await NewDeepSeekManager.Instance.GenerateGameBaseJson(item1.jsonModle, result, "GameBase");

                                                Console.WriteLine($"\n------ 生成 worldviewandtarget .json完成 ------");
                                                break;
                                            case "MARKDOWN":
                                                await NewDeepSeekManager.Instance.GenerateMDfile(result, "GameBase");
                                                Console.WriteLine($"\n------ 生成 worldviewandtarget .json完成 ------");
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        await NewDeepSeekManager.Instance.GenerateGameBase(RequestInfo.userInputStr, (str) =>
                                        {
                                            Console.WriteLine($"返回的数据 ：{str}");
                                            string[] Part = str.Split("|");
                                            string Id = Part[0];
                                            string response = Part[1];
                                            backInfo.content = response;
                                        });
                                    }
                                }

                                break;
                            case ProcessType.worldvieAndTarget:
                                AI_Game_Output item = AI_Game_Output.getByid("10002");
                                if (item != null)
                                {
                                    if (item.ifSse == true)
                                    {
                                        InputPromptData promptData1 = new InputPromptData();
                                        promptData1.systemPrompt = item.systemPrompt;
                                        string outputPath = Path.Combine("baseTemplate", "GameBase.json");
                                        string jsonString = File.ReadAllText(outputPath);
                                        promptData1.userPrompt = jsonString;

                                        context.Response.StatusCode = 200;
                                        context.Response.ContentType = "text/event-stream; charset=utf-8";
                                        context.Response.Headers.Add("Cache-Control", "no-cache");
                                        context.Response.Headers.Add("Connection", "keep-alive");
                                        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                                        streamOutputBol = true;
                                        string result = await StreamResponseToClient(context, promptData1);
                                        switch (item.priority)
                                        {
                                            case "JSON":
                                                await NewDeepSeekManager.Instance.GenerateGameBaseJson(item.jsonModle, result, "worldviewandtarget");

                                                Console.WriteLine($"\n------ 生成 worldviewandtarget .json完成 ------");
                                                break;
                                            case "MARKDOWN":
                                                await NewDeepSeekManager.Instance.GenerateMDfile(result, "worldviewandtarget");
                                                Console.WriteLine($"\n------ 生成 worldviewandtarget .json完成 ------");
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        await NewDeepSeekManager.Instance.GenerateWorldViewAndTarget((str) =>
                                        {
                                            Console.WriteLine($"返回的数据 ：{str}");
                                            string[] Part = str.Split("|");
                                            string Id = Part[0];
                                            string response = Part[1];
                                            backInfo.content = response;
                                        });

                                    }
                                }

                                break;
                            case ProcessType.refreshGamebase:
                                //backInfo.content = await DeepseekManager.Instance.RefreshGameBaseType(RequestInfo.theme, RequestInfo.gametype, RequestInfo.gameangleOfview, RequestInfo.dimension, RequestInfo.worldview);
                                //backInfo.ignJson = await DeepseekManager.Instance.GetIGNreviewlink();
                                //backInfo.gameIntroduceJson = await DeepseekManager.Instance.SetGameIntroduce();
                                streamOutputBol = true;
                                context.Response.StatusCode = 200;
                                context.Response.ContentType = "text/event-stream; charset=utf-8";
                                context.Response.Headers.Add("Cache-Control", "no-cache");
                                context.Response.Headers.Add("Connection", "keep-alive");
                                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                                await context.Response.Body.FlushAsync(); // 立即发送 headers
                                AI_Game_Output item2 = AI_Game_Output.getByid("10003");
                                if (item2 != null)
                                {
                                    if (item2.ifSse == true)
                                    {
                                    }
                                    else
                                    {
                                        string outstr = await NewDeepSeekManager.Instance.RefreshGameBaseType(RequestInfo.theme, RequestInfo.gametype, RequestInfo.gameangleOfview, RequestInfo.dimension, RequestInfo.worldview);
                                        if (!string.IsNullOrEmpty(outstr))
                                        {
                                            int backInfoTime = 0;
                                            await NewDeepSeekManager.Instance.GetIGNreviewlink(async (str) =>
                                            {

                                                Console.WriteLine($"返回值： {str}");
                                                string[] Part = str.Split("|");
                                                string Id = Part[0];
                                                string response = Part[1];
                                                string Note = "";
                                                switch (Id)
                                                {
                                                    case "10004":
                                                        //backInfo.ignJson = response;
                                                        Note = "ign";
                                                        break;
                                                    case "10005":

                                                        //backInfo.gameIntroduceJson = response;
                                                        Note = "gameIntroduce";
                                                        break;
                                                    case "10006":
                                                        //backInfo.content = response;
                                                        Note = "chapter";
                                                        break;
                                                }
                                                // 返回部分结果给客户端
                                                var eventData = new
                                                {
                                                    id = Id,
                                                    note = Note,
                                                    data = response
                                                };
                                                string json = JsonConvert.SerializeObject(eventData);
                                                await context.Response.WriteAsync($"data: {json}\n\n");
                                                await context.Response.Body.FlushAsync();
                                                backInfoTime++;

                                                if (backInfoTime >= 3)
                                                {
                                                    // 全部完成后发送结束标记
                                                    await context.Response.WriteAsync("data: [DONE]\n\n");
                                                    await context.Response.Body.FlushAsync();
                                                }

                                            });
                                        }
                                    }
                                }

                                break;
                            case ProcessType.Chapter:
                                backInfo.content = await DeepseekManager.Instance.GenerateChapter();
                                break;
                            case ProcessType.CharacterKeys:
                                backInfo.content = await DeepseekManager.Instance.GenerateCharacters();
                                break;
                            case ProcessType.ScenceKeys:
                                backInfo.content = await DeepseekManager.Instance.GenerateScences();
                                break;
                            case ProcessType.ScenceMusic:
                                backInfo.content = await DeepseekManager.Instance.GenerateScenceMusic();
                                break;
                            case ProcessType.ScenceEffect:
                                backInfo.content = await DeepseekManager.Instance.GenerateEffects();
                                break;
                            case ProcessType.skybox:
                                backInfo.content = await DeepseekManager.Instance.GenerateSkyboxTemplate();
                                break;
                            case ProcessType.designCases:
                                await DeepseekManager.Instance.GenerateDesignCases();
                                backInfo.content = "生成策划案完成。";
                                break;
                            case ProcessType.StreamOutput:
                                context.Response.StatusCode = 200;
                                context.Response.ContentType = "text/event-stream; charset=utf-8";
                                context.Response.Headers.Add("Cache-Control", "no-cache");
                                context.Response.Headers.Add("Connection", "keep-alive");
                                context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                                streamOutputBol = true;
                                InputPromptData promptData = new InputPromptData();
                                await StreamResponseToClient(context, RequestInfo.userInputStr);
                                break;
                            case ProcessType.pause:
                                string pauseId = RequestInfo.userInputStr;
                                NewDeepSeekManager.Instance.PauseConversation(pauseId);
                                break;
                            case ProcessType.resume:
                                string resumeId = RequestInfo.userInputStr;
                                NewDeepSeekManager.Instance.ResumeConversation(resumeId);
                                break;
                            case ProcessType.cancel:
                                string cancelId = RequestInfo.userInputStr;
                                NewDeepSeekManager.Instance.CancelConversation(cancelId);
                                break;


                        }
                        if (streamOutputBol == false)
                        {
                            if (backInfo.content != "")
                            {
                                backInfo.recode = 200;
                                backInfo.result = "OK";
                            }
                            else
                            {
                                backInfo.recode = 400;
                                backInfo.result = "未生成游戏内容";
                            }
                        }
                    }
                    if (streamOutputBol == false)
                    {
                        var reqInfo = JsonConvert.SerializeObject(backInfo);
                        context.Response.StatusCode = backInfo.recode;
                        context.Response.ContentType = "application/json;charset=UTF-8";
                        context.Response.Headers.Add("Allow", "GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS");
                        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
                        context.Response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");
                        await context.Response.WriteAsync(reqInfo);
                    }

                }
                else
                {
                    await context.Response.WriteAsync("not support contentType=" + context.Request.ContentType);
                }
            }
            else
            {
                await context.Response.WriteAsync("error method");
            }
        }

        private static async Task StreamResponseToClient(HttpContext context, string prompt)
        {

            try
            {
                var chatHistory = new ChatHistory();
                //if (!string.IsNullOrEmpty(data.systemPrompt))
                //{
                //    chatHistory.AddSystemMessage(data.systemPrompt);
                //}
                await foreach (var chunk in NewDeepSeekManager.Instance._chatService.GetStreamingChatMessageContentsAsync(
                    chatHistory))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        Console.Write(chunk.Content);
                        // 使用 Server-Sent Events (SSE) 格式发送数据
                        await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = chunk.Content })}\n\n");
                        await context.Response.Body.FlushAsync();
                    }

                    if (context.RequestAborted.IsCancellationRequested)
                    {
                        break;
                    }
                }
                // 发送结束标记
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
            }
            catch (Exception ex)
            {
                // 发生错误时发送错误信息
                await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = ex.Message })}\n\n");
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
            }
        }

        private static async Task<string> StreamResponseToClient(HttpContext context, InputPromptData data)
        {
            string outStr = "";
            try
            {
                var chatHistory = new ChatHistory();
                if (!string.IsNullOrEmpty(data.systemPrompt))
                {
                    chatHistory.AddSystemMessage(data.systemPrompt);
                }
                chatHistory.AddUserMessage(data.userPrompt);
                StringBuilder result = new StringBuilder();
                await foreach (var chunk in NewDeepSeekManager.Instance._chatService.GetStreamingChatMessageContentsAsync(
                    chatHistory))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        Console.Write(chunk.Content);
                        result.Append(chunk.Content);
                        // 使用 Server-Sent Events (SSE) 格式发送数据
                        await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = chunk.Content })}\n\n");
                        await context.Response.Body.FlushAsync();
                    }

                    if (context.RequestAborted.IsCancellationRequested)
                    {
                        break;
                    }
                }
                outStr = result.ToString();
                // 发送结束标记
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
            }
            catch (Exception ex)
            {
                // 发生错误时发送错误信息
                await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = ex.Message })}\n\n");
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
            }
            return outStr;
        }


        static async Task on404(HttpContext context)
        {
            await context.Response.WriteAsync("only websocket connect.");
            return;
        }
    }

    public class RequestBackInfo
    {
        public string result;
        public int recode;
        public string content;
        public string ignJson;
        public string gameIntroduceJson;
    }

    public class RequestInfoData
    {
        public string userInputStr;
        public string theme;
        public string gametype;
        public string gameangleOfview;
        public string dimension;
        public string worldview;
    }
    public class InputPromptData
    {
        public string systemPrompt;
        public string userPrompt;
    }
    public enum ProcessType
    {
        basetemplate,
        userInput,
        worldvieAndTarget,
        refreshGamebase,
        IGNreviewlink,
        GameIntroduce,
        Chapter,
        CharacterKeys,
        ScenceKeys,
        ScenceMusic,
        ScenceEffect,
        skybox,
        designCases,
        StreamOutput,
        pause,
        cancel,
        resume
    }
    public static class StringBuilderExtensions
    {
        public static StringBuilder AppendWithLog(this StringBuilder sb, string value)
        {
            Console.WriteLine($"拦截到 Append: {value}");
            return sb.Append(value);
        }
    }
    public class Conversation()
    {
        public string taskid { get; set; }
        public string prompt { get; set; }
        public string response { get; set; }
        public StringBuilder stringBuilder { get; set; }
    }
}
