using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Newtonsoft.Json;
using static SaveDataService.RestfulApiDescriptor;

namespace SaveDataService
{
    /// <summary>
    /// RESTful API 生成器 - 提供通用的 RESTful API 描述功能
    /// </summary>
    public class RESTfulAPIGen
    {
        /// <summary>
        /// 获取所有继承 RESTfulAPIBase 的类的 API 描述
        /// </summary>
        /// <returns>所有 API 的 JSON 描述</returns>
        public static string GetAllHttpPostFunctions()
        {
            try
            {
                var allApis = new Dictionary<string, object>();

                // 获取当前程序集中所有继承 RESTfulAPIBase 的类
                var assembly = Assembly.GetExecutingAssembly();
                var apiTypes = assembly.GetTypes()
                    .Where(t => typeof(RESTfulAPIBase).IsAssignableFrom(t) &&
                               !t.IsAbstract &&
                               t != typeof(RESTfulAPIBase))
                    .ToList();

                foreach (var apiType in apiTypes)
                {
                    var apiJson = RESTfulAPIBase.GetHttpPostFunction(apiType);
                    var apiData = JsonConvert.DeserializeObject(apiJson);
                    allApis[apiType.Name] = apiData;
                }

                var result = new
                {
                    GeneratedAt = DateTime.UtcNow,
                    TotalApiClasses = allApis.Count,
                    ApiClasses = allApis
                };

                return JsonConvert.SerializeObject(result, Formatting.Indented);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成所有 API 描述时发生错误: {ex.Message}");
                return JsonConvert.SerializeObject(new { error = ex.Message }, Formatting.Indented);
            }
        }

        /// <summary>
        /// 获取指定类的 API 描述（向后兼容）
        /// </summary>
        /// <param name="className">类名</param>
        /// <returns>RESTful API 的 JSON 描述</returns>
        public static string GetHttpPostFunction(string className = "AccountManage")
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var targetType = assembly.GetTypes()
                    .FirstOrDefault(t => t.Name.Equals(className, StringComparison.OrdinalIgnoreCase) &&
                                        typeof(RESTfulAPIBase).IsAssignableFrom(t));

                if (targetType == null)
                {
                    return JsonConvert.SerializeObject(new { error = $"未找到类 {className} 或该类未继承 RESTfulAPIBase" }, Formatting.Indented);
                }

                return RESTfulAPIBase.GetHttpPostFunction(targetType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成 {className} API 描述时发生错误: {ex.Message}");
                return JsonConvert.SerializeObject(new { error = ex.Message }, Formatting.Indented);
            }
        }

        /// <summary>
        /// 获取指定类的 API 描述（新方法名）
        /// </summary>
        /// <param name="className">类名</param>
        /// <returns>RESTful API 的 JSON 描述</returns>
        public static string GetHttpPostFunctionsByClass(string className)
        {
            return GetHttpPostFunction(className);
        }



    }
}
