﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
    public interface AISDKProvider
    {
        void SendTextToAIModel(List<string> text, Action<string, Boolean> callback);
        void SendImageOrVideoToAiModle(string imageOrVideoBase64,string requestText, Action<string, Boolean> callback);
         Task<AiChatBase> KernelFunAsync();
        void ChatWithAi(string prompt,Action<string,Boolean>callback);
    }
}
