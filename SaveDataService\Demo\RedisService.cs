﻿using StackExchange.Redis;
using System;
using System.Threading.Tasks;

public class RedisService : IDisposable
{
    private readonly ConnectionMultiplexer _redis;
    private readonly IDatabase _db;

    /// <summary>
    /// 初始化 Redis 服务
    /// </summary>
    /// <param name="connectionString">Redis 连接字符串，例如 "localhost:6379"</param>
    public RedisService(string connectionString)
    {
        // 创建 Redis 连接
        _redis = ConnectionMultiplexer.Connect(connectionString);

        // 获取默认数据库 (默认是 db0)
        _db = _redis.GetDatabase();

        Console.WriteLine("Redis 连接已建立");
    }

    #region 缓存操作

    /// <summary>
    /// 设置缓存
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiry">过期时间</param>
    public async Task SetCacheAsync(string key, string value, TimeSpan? expiry = null)
    {
        await _db.StringSetAsync(key, value, expiry);
        Console.WriteLine($"已设置缓存: {key}");
    }

    /// <summary>
    /// 获取缓存
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>缓存值，如果不存在则返回 null</returns>
    public async Task<string?> GetCacheAsync(string key)
    {
        var value = await _db.StringGetAsync(key);
        Console.WriteLine($"获取缓存: {key} = {value}");
        return value.IsNullOrEmpty ? null : (string?)value;
    }

    /// <summary>
    /// 删除缓存
    /// </summary>
    /// <param name="key">缓存键</param>
    public async Task<bool> DeleteCacheAsync(string key)
    {
        var result = await _db.KeyDeleteAsync(key);
        Console.WriteLine($"删除缓存: {key} {(result ? "成功" : "失败")}");
        return result;
    }

    #endregion

    #region 存储操作

    /// <summary>
    /// 设置哈希表字段
    /// </summary>
    /// <param name="hashKey">哈希表键</param>
    /// <param name="field">字段名</param>
    /// <param name="value">字段值</param>
    public async Task SetHashFieldAsync(string hashKey, string field, string value)
    {
        await _db.HashSetAsync(hashKey, field, value);
        Console.WriteLine($"已设置哈希表 {hashKey} 的字段 {field}");
    }

    /// <summary>
    /// 获取哈希表字段
    /// </summary>
    /// <param name="hashKey">哈希表键</param>
    /// <param name="field">字段名</param>
    /// <returns>字段值，如果不存在则返回 null</returns>
    public async Task<string?> GetHashFieldAsync(string hashKey, string field)
    {
        var value = await _db.HashGetAsync(hashKey, field);
        Console.WriteLine($"获取哈希表 {hashKey} 的字段 {field} = {value}");
        return value.IsNullOrEmpty ? null : (string?)value;
    }

    #endregion

    #region 队列操作

    /// <summary>
    /// 入队
    /// </summary>
    /// <param name="queueName">队列名</param>
    /// <param name="message">消息</param>
    public async Task EnqueueAsync(string queueName, string message)
    {
        await _db.ListRightPushAsync(queueName, message);
        Console.WriteLine($"消息已入队 {queueName}: {message}");
    }

    /// <summary>
    /// 出队
    /// </summary>
    /// <param name="queueName">队列名</param>
    /// <returns>出队的消息，如果队列为空则返回 null</returns>
    public async Task<string?> DequeueAsync(string queueName)
    {
        var message = await _db.ListLeftPopAsync(queueName);
        Console.WriteLine($"消息已出队 {queueName}: {message}");
        return message.IsNullOrEmpty ? null : (string?)message;
    }

    #endregion

    #region 发布/订阅消息

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="channel">频道名</param>
    /// <param name="message">消息内容</param>
    public async Task PublishAsync(string channel, string message)
    {
        var subscriber = _redis.GetSubscriber();
        var redisChannel = new RedisChannel(channel, RedisChannel.PatternMode.Literal);

        var count = await subscriber.PublishAsync(redisChannel, message);
        Console.WriteLine($"消息已发布到 {channel}，接收者数量: {count}");
    }

    /// <summary>
    /// 订阅消息
    /// </summary>
    /// <param name="channel">频道名</param>
    /// <param name="handler">消息处理程序</param>
    public void Subscribe(string channel, Action<RedisChannel, RedisValue> handler)
    {
        var subscriber = _redis.GetSubscriber();
        var redisChannel = new RedisChannel(channel, RedisChannel.PatternMode.Literal);

        subscriber.Subscribe(redisChannel, (ch, value) =>
        {
            Console.WriteLine($"从频道 {ch} 接收到消息: {value}");
            handler(ch, value);
        });

        Console.WriteLine($"已订阅频道: {channel}");
    }

    #endregion

    #region 分布式锁

    /// <summary>
    /// 获取分布式锁
    /// </summary>
    /// <param name="lockKey">锁键</param>
    /// <param name="lockValue">锁值(通常唯一标识持有者)</param>
    /// <param name="expiry">锁的过期时间</param>
    public async Task<bool> AcquireLockAsync(string lockKey, string lockValue, TimeSpan expiry)
    {
        var acquired = await _db.StringSetAsync(
            lockKey,
            lockValue,
            expiry,
            When.NotExists);

        Console.WriteLine($"尝试获取锁 {lockKey}: {(acquired ? "成功" : "失败")}");
        return acquired;
    }

    /// <summary>
    /// 释放分布式锁
    /// </summary>
    /// <param name="lockKey">锁键</param>
    /// <param name="lockValue">锁值(必须与获取时一致)</param>
    public async Task<bool> ReleaseLockAsync(string lockKey, string lockValue)
    {
        var script = @"
            if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
            else
                return 0
            end";

        var result = (int)await _db.ScriptEvaluateAsync(
            script,
            new RedisKey[] { lockKey },
            new RedisValue[] { lockValue });

        var released = result == 1;
        Console.WriteLine($"尝试释放锁 {lockKey}: {(released ? "成功" : "失败")}");
        return released;
    }

    #endregion

    #region 指标和统计

    /// <summary>
    /// 增加计数器
    /// </summary>
    /// <param name="metricKey">指标键</param>
    /// <param name="increment">增量值</param>
    public async Task<long> IncrementMetricAsync(string metricKey, long increment = 1)
    {
        var newValue = await _db.StringIncrementAsync(metricKey, increment);
        Console.WriteLine($"指标 {metricKey} 增加 {increment}，新值: {newValue}");
        return newValue;
    }

    /// <summary>
    /// 获取服务器信息
    /// </summary>
    public void GetServerInfo()
    {
        var server = _redis.GetServer(_redis.GetEndPoints()[0]);
        var info = server.Info();

        Console.WriteLine("Redis 服务器信息:");
        foreach (var section in info)
        {
            Console.WriteLine($"Section: {section.Key}");
            foreach (var item in section)
            {
                Console.WriteLine($"  {item.Key}: {item.Value}");
            }
        }
    }

    #endregion

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _redis?.Dispose();
        Console.WriteLine("Redis 连接已关闭");
        GC.SuppressFinalize(this);
    }
}
// 使用示例
public class RedisExample
{
    public static async Task RunExample()
    {
        // 创建 Redis 服务实例
        var redisService = new RedisService("localhost:6379");

        try
        {
            // 1. 缓存操作示例
            await redisService.SetCacheAsync("test_key", "Hello Redis", TimeSpan.FromMinutes(10));
            var value = await redisService.GetCacheAsync("test_key");
            await redisService.DeleteCacheAsync("test_key");

            // 2. 哈希表操作示例
            await redisService.SetHashFieldAsync("user:1000", "name", "Alice");
            await redisService.SetHashFieldAsync("user:1000", "email", "<EMAIL>");
            var name = await redisService.GetHashFieldAsync("user:1000", "name");

            // 3. 队列操作示例
            await redisService.EnqueueAsync("my_queue", "message1");
            await redisService.EnqueueAsync("my_queue", "message2");
            var msg = await redisService.DequeueAsync("my_queue");

            // 4. 发布/订阅示例
            redisService.Subscribe("news", (channel, message) =>
            {
                Console.WriteLine($"处理消息: {message}");
            });
            await redisService.PublishAsync("news", "Breaking news!");

            // 5. 分布式锁示例
            var lockKey = "resource_lock";
            var lockValue = Guid.NewGuid().ToString();
            if (await redisService.AcquireLockAsync(lockKey, lockValue, TimeSpan.FromSeconds(30)))
            {
                try
                {
                    Console.WriteLine("执行受锁保护的代码");
                    await Task.Delay(1000);
                }
                finally
                {
                    await redisService.ReleaseLockAsync(lockKey, lockValue);
                }
            }

            // 6. 指标操作示例
            await redisService.IncrementMetricAsync("page_views");

            // 7. 服务器信息
            redisService.GetServerInfo();
        }
        finally
        {
            redisService.Dispose();
        }
    }
}