using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SaveDataService;
using ExcelToData;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI日志服务类
    /// 负责记录工作流执行的完整流程和文件下载信息
    /// </summary>
    public static class ComfyUILogService
    {
        /// <summary>
        /// 创建新的工作流执行日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="serverId">服务器ID</param>
        /// <param name="workflowId">工作流ID</param>
        /// <param name="workflowName">工作流名称</param>
        /// <param name="inputParameters">输入参数</param>
        /// <returns>日志ID</returns>
        public static async Task<string> CreateWorkflowLogAsync(
            string taskId, 
            string serverId, 
            string workflowId, 
            string workflowName, 
            object inputParameters = null)
        {
            try
            {
                var log = new ComfyUILog
                {
                    taskId = taskId,
                    serverId = serverId,
                    workflowId = workflowId,
                    workflowName = workflowName,
                    status = 0, // 初始化状态
                    statusDescription = "工作流初始化",
                    startTime = DateTime.Now,
                    inputParameters = inputParameters != null ? JsonConvert.SerializeObject(inputParameters) : null
                };

                await ORMTables.Instance.ComfyUILogs.AddAsync(log);
                await ORMTables.Instance.SaveChangesAsync();

                Console.WriteLine($"📝 创建工作流日志: {log.id} - {workflowName}");
                return log.id;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建工作流日志失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 更新工作流状态
        /// </summary>
        /// <param name="logId">日志ID</param>
        /// <param name="status">状态</param>
        /// <param name="statusDescription">状态描述</param>
        /// <param name="promptId">Prompt ID</param>
        /// <param name="queuePosition">队列位置</param>
        public static async Task UpdateWorkflowStatusAsync(
            string logId, 
            int status, 
            string statusDescription, 
            string promptId = null, 
            int? queuePosition = null)
        {
            try
            {
                var log = await ORMTables.Instance.ComfyUILogs.FindAsync(logId);
                if (log != null)
                {
                    log.status = status;
                    log.statusDescription = statusDescription;
                    
                    if (!string.IsNullOrEmpty(promptId))
                        log.promptId = promptId;
                    
                    if (queuePosition.HasValue)
                        log.queuePosition = queuePosition.Value;

                    await ORMTables.Instance.SaveChangesAsync();
                    Console.WriteLine($"📝 更新工作流状态: {logId} - {statusDescription}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 更新工作流状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新执行进度
        /// </summary>
        /// <param name="logId">日志ID</param>
        /// <param name="progress">进度 (0-100)</param>
        /// <param name="currentNodeId">当前节点ID</param>
        /// <param name="currentNodeName">当前节点名称</param>
        /// <param name="currentNodeType">当前节点类型</param>
        /// <param name="totalNodes">总节点数</param>
        public static async Task UpdateExecutionProgressAsync(
            string logId, 
            int progress, 
            string currentNodeId = null, 
            string currentNodeName = null, 
            string currentNodeType = null, 
            int? totalNodes = null)
        {
            try
            {
                var log = await ORMTables.Instance.ComfyUILogs.FindAsync(logId);
                if (log != null)
                {
                    log.progress = progress;
                    
                    if (!string.IsNullOrEmpty(currentNodeId))
                        log.currentNodeId = currentNodeId;
                    
                    if (!string.IsNullOrEmpty(currentNodeName))
                        log.currentNodeName = currentNodeName;
                    
                    if (!string.IsNullOrEmpty(currentNodeType))
                        log.currentNodeType = currentNodeType;
                    
                    if (totalNodes.HasValue)
                        log.totalNodes = totalNodes.Value;

                    await ORMTables.Instance.SaveChangesAsync();
                    Console.WriteLine($"📝 更新执行进度: {logId} - {progress}% ({currentNodeName})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 更新执行进度失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录工作流完成
        /// </summary>
        /// <param name="logId">日志ID</param>
        /// <param name="success">是否成功</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="serverResponse">服务器响应</param>
        /// <param name="nodeExecutionDetails">节点执行详情</param>
        public static async Task CompleteWorkflowAsync(
            string logId, 
            bool success, 
            string errorMessage = null, 
            object serverResponse = null, 
            object nodeExecutionDetails = null)
        {
            try
            {
                var log = await ORMTables.Instance.ComfyUILogs.FindAsync(logId);
                if (log != null)
                {
                    log.status = success ? 4 : 5; // 4:完成, 5:失败
                    log.statusDescription = success ? "工作流执行完成" : "工作流执行失败";
                    log.endTime = DateTime.Now;
                    log.progress = success ? 100 : log.progress;
                    
                    if (log.startTime.HasValue)
                    {
                        log.totalExecutionTime = (log.endTime.Value - log.startTime.Value).TotalSeconds;
                    }
                    
                    if (!string.IsNullOrEmpty(errorMessage))
                        log.errorMessage = errorMessage;
                    
                    if (serverResponse != null)
                        log.serverResponse = JsonConvert.SerializeObject(serverResponse);
                    
                    if (nodeExecutionDetails != null)
                        log.nodeExecutionDetails = JsonConvert.SerializeObject(nodeExecutionDetails);

                    await ORMTables.Instance.SaveChangesAsync();
                    Console.WriteLine($"📝 工作流完成记录: {logId} - {(success ? "成功" : "失败")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 记录工作流完成失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录文件下载信息
        /// </summary>
        /// <param name="logId">日志ID</param>
        /// <param name="downloadDetails">下载详情</param>
        public static async Task RecordFileDownloadAsync(string logId, List<FileDownloadInfo> downloadDetails)
        {
            try
            {
                var log = await ORMTables.Instance.ComfyUILogs.FindAsync(logId);
                if (log != null)
                {
                    log.totalOutputFiles = downloadDetails.Count;
                    log.successfulDownloads = downloadDetails.Count(d => d.Success);
                    log.failedDownloads = downloadDetails.Count(d => !d.Success);
                    log.downloadSuccessRate = log.totalOutputFiles > 0 ? 
                        (double)log.successfulDownloads / log.totalOutputFiles * 100 : 0;
                    
                    log.totalDownloadSize = downloadDetails.Where(d => d.Success).Sum(d => d.FileSize);
                    log.totalDownloadTime = downloadDetails.Where(d => d.Success).Sum(d => d.DownloadTime);
                    
                    if (log.totalDownloadTime > 0 && log.totalDownloadSize > 0)
                    {
                        log.averageDownloadSpeed = (log.totalDownloadSize / 1024.0 / 1024.0) / log.totalDownloadTime;
                    }
                    
                    log.downloadFileDetails = JsonConvert.SerializeObject(downloadDetails);

                    await ORMTables.Instance.SaveChangesAsync();
                    Console.WriteLine($"📝 记录文件下载: {logId} - {log.successfulDownloads}/{log.totalOutputFiles} 成功");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 记录文件下载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取工作流日志
        /// </summary>
        /// <param name="logId">日志ID</param>
        /// <returns>工作流日志</returns>
        public static async Task<ComfyUILog> GetWorkflowLogAsync(string logId)
        {
            try
            {
                return await ORMTables.Instance.ComfyUILogs.FindAsync(logId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取工作流日志失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取任务的所有日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>日志列表</returns>
        public static async Task<List<ComfyUILog>> GetTaskLogsAsync(string taskId)
        {
            try
            {
                return ORMTables.Instance.ComfyUILogs
                    .Where(log => log.taskId == taskId)
                    .OrderBy(log => log.startTime)
                    .ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取任务日志失败: {ex.Message}");
                return new List<ComfyUILog>();
            }
        }
    }

    /// <summary>
    /// 文件下载信息
    /// </summary>
    public class FileDownloadInfo
    {
        public string FileName { get; set; }
        public string FileUrl { get; set; }
        public string LocalPath { get; set; }
        public long FileSize { get; set; }
        public double DownloadTime { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime DownloadTime_DateTime { get; set; }
    }
}
