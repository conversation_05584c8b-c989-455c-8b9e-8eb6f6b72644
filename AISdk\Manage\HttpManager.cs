﻿using System.Net;
using System.Reflection;
using System.Runtime.InteropServices.JavaScript;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AISDKWebSocket
{
	public class HttpManager
	{
		[System.Serializable]
		public class Message
		{
			public string role { get; set; }
			public string content { get; set; }
		}
		[System.Serializable]
		public class RequestData
		{
			public string model { get; set; }
			public List<Message> messages { get; set; }

			//public Boolean stream { get; set; }

			// 你可以添加一个构造函数来方便地初始化对象  
			public RequestData(string model, Message[] messages)
			{
				this.model = model;
				//this.messages = messages;
			}

			// 或者，如果你想要一个更简单的构造函数，只接受一个消息  
			public RequestData(string model, string role, List<Message> content)
			{
				this.model = model;
				//this.messages = new[] { new Message { role = role, content = content } };
				this.messages = new List<Message>();
				this.messages = content;
				//this.stream = true;
			}
		}

		public class KuaiShuData
		{
			public string model { get; set; }
			public List<Message> messages { get; set; }

			public Boolean stream { get; set; }

			//public Boolean stream { get; set; }

			// 你可以添加一个构造函数来方便地初始化对象  
			//public KuaiShuData(string model, Message[] messages, bool stream)
			//{
			//	this.model = model;
			//	//this.messages = messages;
			//	this.stream = stream;
			//}

			// 或者，如果你想要一个更简单的构造函数，只接受一个消息  
			public KuaiShuData(string model, string role, List<Message> content, Boolean stream)
			{
				this.model = model;
				this.messages = new List<Message>();
				this.messages = content;
				//this.messages.Add(new Message { role = "urser", content = "上一次提问的什么问题" });

				this.stream = stream;
			}
		}
        public class VideoOrImageInfoData
        {
            public string model;
            public List<MessageData> messages;
        }
        public class MessageData
        {
            public string role;
            public List<ContentData> content;
        }
        public class ContentData
        {
            public string type;
            public VideoOrImageBase64Str video_url;
            public string text;
        }
        public class VideoOrImageBase64Str
        {
            public string url;
        }

        private static HttpManager _instance;

		public static HttpManager Instance
		{
			get
			{
				if (_instance == null)
				{
					_instance = new HttpManager();
					// _instance.init();
				}
				return _instance;
			}
		}

		public void SendText(string requrl, string rqeKey, string jsonData, Action<string, Boolean> callback)
		{
			Send(requrl, rqeKey, jsonData, callback);
        }

		private async void Send(string requrl, string rqeKey, string jsonData, Action<string, Boolean> callback)
		{
			var request = WebRequest.Create(requrl);
			request.Method = "POST";
			byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
			request.ContentType = "application/json";
			request.Headers.Add("Authorization", rqeKey);
			byte[] buffer = Encoding.UTF8.GetBytes(jsonData);
			using (Stream requestStream = await request.GetRequestStreamAsync())
			{
				await requestStream.WriteAsync(buffer, 0, buffer.Length);
			}

			string backText = "";
			try
			{
				using (HttpWebResponse response = (HttpWebResponse)await request.GetResponseAsync())
				using (Stream responseStream = response.GetResponseStream())
				using (StreamReader reader = new StreamReader(responseStream))
				{
					string content = await reader.ReadToEndAsync();
					JObject json = JObject.Parse(content);
					backText = json["choices"][0]["message"]["content"].ToString();
					callback(backText, false);
					// Console.WriteLine(backText);
				}
			}
			catch (WebException e)
			{
				statusDetermine(e, callback);
			}
		}
        public async void SendJson(string requrl, string rqeKey, string jsonData, Action<string, Boolean> callback)
        {
            using (var client = new HttpClient())
            {
                // 创建HttpRequestMessage对象
                var request = new HttpRequestMessage(HttpMethod.Post, requrl)
                {
                    // 设置请求头
                    Headers =
                {
                    { "Authorization", $"Bearer {rqeKey}" }
                },
                    // 创建HttpContent对象，并设置Content-Type头部
                    Content = new StringContent(jsonData, Encoding.UTF8, "application/json")
                };

                // 发送POST请求
                HttpResponseMessage response = await client.SendAsync(request);

                // 读取响应内容
                string backText = await response.Content.ReadAsStringAsync();
                JObject json = JObject.Parse(backText);
                string backTextStr = json["choices"]?[0]?["message"]?["content"]?.ToString();
                string safeBackTextStr = backTextStr ?? string.Empty;
                callback(safeBackTextStr, false);

                // 处理响应
                //Console.WriteLine(backText); // 输出结果
            }
        }

        public void FlowmethodSendText(string requrl, string rqeKey, string jsonData, Action<string, Boolean> callback)
		{
			Flowmethod(requrl, rqeKey, jsonData, callback);
		}

		private async void Flowmethod(string requrl, string rqeKey, string jsonData, Action<string, Boolean> callback)
		{

			int TIMEOUT_TIME = 10000;

			HttpWebRequest request = WebRequest.Create(requrl) as HttpWebRequest;
			request.ReadWriteTimeout = TIMEOUT_TIME;
			request.Timeout = TIMEOUT_TIME;
			request.Method = "POST";
			request.ContentType = "application/json";
			request.Headers.Add("Authorization", rqeKey);
			using (Stream requestStream = await request.GetRequestStreamAsync())
			{
				byte[] buffer = Encoding.UTF8.GetBytes(jsonData);
				await requestStream.WriteAsync(buffer, 0, buffer.Length);
			}

			try
			{
				using (HttpWebResponse response = (HttpWebResponse)await request.GetResponseAsync())
				using (Stream responseStream = response.GetResponseStream())
				using (StreamReader reader = new StreamReader(responseStream))
				{
					StringBuilder stringBuilder = new StringBuilder();
					char[] buffer = new char[1024];
					int bytesRead;

					while ((bytesRead = await reader.ReadAsync(buffer, 0, buffer.Length)) > 0)
					{
						stringBuilder.Append(buffer, 0, bytesRead);
						string text = stringBuilder.ToString();
						//Console.WriteLine(text);
						string[] items = text.Split("data: ");
						//UnityEngine.Debug.Log(items[1]);
						string rqData = items[items.Length - 1];

						//下方代码有可能会报错
						try
						{
							JObject json = JObject.Parse(rqData);
							//Console.WriteLine(json["choices"]?[0] +"//这是历史");
							string backText = json["choices"]?[0]?["delta"]?["content"]?.ToString();
							string stop = json?["finish_reason"]?.ToString();
							string base_resp = json?["base_resp"]?["status_msg"]?.ToString();
							if (base_resp == "insufficient balance")
							{
								callback("需要付款", false);
								return;
							}

							if (stop == "stop")
							{
								callback(backText, false);
								return;
							}
							if (backText != null)
							{
								callback(backText, true);
							}
						}
						catch
						{
							callback("", false);
						}
						buffer = new char[1024];
					}
				}
			}
			catch (WebException e)
			{
				statusDetermine(e, callback);
			}

		}


		public async void HttpsWeb(string requrl, string rqeKey, Object data, Action<string, Boolean> callback)
		{
			string jsonData = JsonConvert.SerializeObject(data);
			int TIMEOUT_TIME = 20000;

			HttpWebRequest request = WebRequest.Create(requrl) as HttpWebRequest;
			request.ReadWriteTimeout = TIMEOUT_TIME;
			request.Timeout = TIMEOUT_TIME;
			request.Method = "POST";
			request.ContentType = "application/json";
			request.Headers.Add("Authorization", rqeKey);
			using (Stream requestStream = await request.GetRequestStreamAsync())
			{
				byte[] buffer = Encoding.UTF8.GetBytes(jsonData);
				await requestStream.WriteAsync(buffer, 0, buffer.Length);
			}

			try
			{
				using (HttpWebResponse response = (HttpWebResponse)await request.GetResponseAsync())
				using (Stream responseStream = response.GetResponseStream())
				using (StreamReader reader = new StreamReader(responseStream))
				{
					StringBuilder stringBuilder = new StringBuilder();
					char[] buffer = new char[1024];
					int bytesRead;

					while ((bytesRead = await reader.ReadAsync(buffer, 0, buffer.Length)) > 0)
					{
						stringBuilder.Append(buffer, 0, bytesRead);
						string text = stringBuilder.ToString();
						string[] item = text.Split("data:");
						string returnText = "";
						foreach (string item2 in item)
						{
							string item3 = item2;
							if (item3.StartsWith("data: "))
							{
								item3 = item3.Substring(6);
							}
							if (item3.StartsWith(" "))
							{
								item3 = item3.Substring(1);
							}
							//Console.WriteLine(item3);
							if (item3 != "")
							{
								try
								{
									JObject json = JObject.Parse(item3);
									string backText = json["choices"]?[0]?["delta"]?["content"]?.ToString();
									string stop = json?["choices"]?[0]?["finish_reason"]?.ToString();
									string base_resp = json?["base_resp"]?["status_msg"]?.ToString();
									if (base_resp == "insufficient balance")
									{
										callback("需要付款", false);
										return;
									}
									returnText += backText;
									if (stop == "stop")
									{
										callback(returnText, false);
										return;
									}
								}
								catch (Exception ex)
								{

								}
							}
						}
						//Console.WriteLine(items[0]);
						//string rqData = items[items.Length - 1];

						buffer = new char[1024];
					}
				}
			}
			catch (WebException e)
			{
				statusDetermine(e, callback);
			}
		}

		public void statusDetermine(WebException e, Action<string, bool> callback)
		{
			HttpWebResponse errorResponse = e.Response as HttpWebResponse;
			if (errorResponse != null)
			{
				int status = (int)errorResponse.StatusCode;

				switch (status)
				{
					case 402:
						callback("需要付款", false);
						break;
					case 429:
						callback("需要付款", false);
						break;
					case 404:
						callback("未找到模型", false);
						break;
					case 503:
						callback("服务器不可用", false);
						break;
					default:
						callback("未知错误", false);
						break;
				}
			}
			else
			{
				callback("未知错误", false);
			}
		}

		public async void Sendkk(string requrl, Object jsonData, Action<string, Boolean> callback)
		{
			using (HttpClient client = new HttpClient())
			{
				client.DefaultRequestHeaders.Accept.Clear();
				client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

				string jsonString = JsonConvert.SerializeObject(jsonData);
				HttpContent content = new StringContent(jsonString, Encoding.UTF8, "application/json");
				HttpResponseMessage response = await client.PostAsync(requrl, content);

				string backText = "";
				try
				{
					response.EnsureSuccessStatusCode(); // This will throw an exception if the response is not successful  

					string responseBody = await response.Content.ReadAsStringAsync();
					JObject json = JObject.Parse(responseBody);
					backText = json["message"]["content"].ToString();
					callback(backText, false);
					// Console.WriteLine(backText);  
				}
				catch (WebException e)
				{
					statusDetermine(e, callback);
				}
			}


			//var request = WebRequest.Create(requrl);
			//request.Method = "POST";
			//byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonData);
			//request.ContentType = "application/json";
			//byte[] buffer = Encoding.UTF8.GetBytes(jsonData);
			//using (Stream requestStream = await request.GetRequestStreamAsync())
			//{
			//	await requestStream.WriteAsync(buffer, 0, buffer.Length);
			//}

			//string backText = "";
			//try
			//{
			//	using (HttpWebResponse response = (HttpWebResponse)await request.GetResponseAsync())
			//	using (Stream responseStream = response.GetResponseStream())
			//	using (StreamReader reader = new StreamReader(responseStream))
			//	{
			//		string content = await reader.ReadToEndAsync();
			//		JObject json = JObject.Parse(content);
			//		backText = json["choices"][0]["message"]["content"].ToString();
			//		callback(backText, false);
			//		// Console.WriteLine(backText);
			//	}
			//}
			//catch (WebException e)
			//{
			//	statusDetermine(e, callback);
			//}
		}

	}
}
