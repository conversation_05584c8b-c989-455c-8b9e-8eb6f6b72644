﻿using Newtonsoft.Json;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class XinghuoAISDK : AISDKProvider
	{
		private string url = "https://spark-api-open.xf-yun.com/v1/chat/completions";
		private string key = "Bearer fa8890e0f94f56db7cd4f060df7c5719:ZjNkYmJiMzFkNGJkZDA2N2IxMmM1OWQ4";
		public string Model;
		public string SendText = "";
		public XinghuoAISDK(string userMode)
		{
			this.Model = userMode;
		}

		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "generalv3.5";
			}
			var data = new KuaiShuData(Model, "user", messages, true);

			string jsonData = JsonConvert.SerializeObject(data);

			Instance.FlowmethodSendText(url, key, jsonData, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				if (!bol)
				{
					backText = Model + ": " + SendText;
					callback(backText, bol);
					SendText = "";
				}
				else
				{
					SendText += backText;
				}
			});
		}
        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "generalv3.5";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }

        public void ChatWithAi(string prompt, Action<string, bool> callback)
        {
            List<Message> messages = new List<Message>();
            messages.Add(new Message { role = "user", content = prompt });
            if (string.IsNullOrEmpty(Model))
            {
                Model = "generalv3.5";
            }
            var data = new KuaiShuData(Model, "user", messages, true);
            string jsonData = JsonConvert.SerializeObject(data);
            Instance.FlowmethodSendText(url, key, jsonData, (backText, bol) =>
            {
                // UnityEngine.Debug.Log(backText);
                if (!bol)
                {
					//backText = Model + ": " + SendText;
					callback(SendText, bol);
                    SendText = "";
                }
                else
                {
                    SendText += backText;
                }
            });
        }
        public async Task<AiChatBase> KernelFunAsync()
        {
            return null;
        }
    }
}
