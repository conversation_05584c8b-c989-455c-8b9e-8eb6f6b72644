﻿using ICSharpCode.SharpZipLib.Zip;

namespace GameServer.GameService.Tools
{
    class ZipTools
    {
        /// 压缩成zip
        /// </summary>
        /// <param name="filesPath">d:\</param>
        /// <param name="zipFilePath">d:\a.zip</param>
        public static void CreateZipFile(string folderToZip, string zipedFile)
        {
            bool result = false;
            if (!Directory.Exists(folderToZip))
                return;

            ZipOutputStream zipStream = new ZipOutputStream(File.Create(zipedFile));
            zipStream.SetLevel(6);
            //if (!string.IsNullOrEmpty(password)) zipStream.Password = password;

            result = ZipDirectory(folderToZip, zipStream, "");

            zipStream.Finish();
            zipStream.Close();

            return;

        }

        /// <summary>   
        /// 递归压缩文件夹的内部方法   
        /// </summary>   
        /// <param name="folderToZip">要压缩的文件夹路径</param>   
        /// <param name="zipStream">压缩输出流</param>   
        /// <param name="parentFolderName">此文件夹的上级文件夹</param>   
        /// <returns></returns>   
        private static bool ZipDirectory(string folderToZip, ZipOutputStream zipStream, string parentFolderName)
        {
            bool result = true;
            string[] folders, files;
            ZipEntry ent = null;
            FileStream fs = null;
            //Crc32 crc = new Crc32();

            try
            {
                ent = new ZipEntry(Path.Combine(parentFolderName, Path.GetFileName(folderToZip) + "/"));
                if (parentFolderName != "")
                {

                    zipStream.PutNextEntry(ent);
                }
                zipStream.Flush();

                files = Directory.GetFiles(folderToZip);
                foreach (string file in files)
                {
                    fs = File.OpenRead(file);

                    byte[] buffer = new byte[fs.Length];
                    fs.Read(buffer, 0, buffer.Length);
                    ent = new ZipEntry(Path.Combine(parentFolderName, Path.GetFileName(folderToZip) + "/" + Path.GetFileName(file)));
                    ent.DateTime = DateTime.Now;
                    ent.Size = fs.Length;

                    fs.Close();

                    //crc.Reset();
                    //crc.Update(buffer);

                    //ent.Crc = crc.Value;
                    zipStream.PutNextEntry(ent);
                    zipStream.Write(buffer, 0, buffer.Length);
                }

            }
            catch
            {
                result = false;
            }
            finally
            {
                if (fs != null)
                {
                    fs.Close();
                    fs.Dispose();
                }
                if (ent != null)
                {
                    ent = null;
                }
                GC.Collect();
                GC.Collect(1);
            }

            folders = Directory.GetDirectories(folderToZip);
            foreach (string folder in folders)
            {
                string getPath = Path.GetFileName(folderToZip);
                if (!string.IsNullOrEmpty(parentFolderName))
                {
                    getPath= parentFolderName + "/" + Path.GetFileName(folderToZip);
                }
                if (!ZipDirectory(folder, zipStream, getPath))
                {

                    return false;
                }
            }

            return result;
        }
    }
}
