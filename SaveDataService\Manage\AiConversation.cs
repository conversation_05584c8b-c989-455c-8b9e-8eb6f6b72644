﻿using GameServer.GameService;
using Microsoft.AspNetCore.Http;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TencentCloud.Tke.V20180525.Models;
using System.Threading;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion.Internal;
using NPOI.HPSF;
using ExcelToData;

namespace SaveDataService.Manage
{
    internal class AiConversation
    {

        public AI_Game_Output AI_Game_OutputData;
        public List<AiConversation> aiConversations = new List<AiConversation>();


        //对话的id  服务器随机生成
        public string ConversationId;
        //用户prompt
        public string UserPrompt;
        //系统prompt
        public string SystemPrompt;
        //ai生成的对话内容
        public StringBuilder ConversationContent = new StringBuilder();
        //对话的状态
        public ConversationState State;
        public string TxtContent;
        public string baseId;

        public HttpContext Context;

        public ConcurrentDictionary<HttpContext, bool> HttpcontextDic = new ConcurrentDictionary<HttpContext, bool>();

        // 添加一个取消令牌源，用于取消正在进行的对话生成
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        // 添加一个锁对象，用于同步对ConversationContent的访问
        private readonly object _contentLock = new object();

        public async Task GenerateTextContent(string filename)
        {
            //string outstr = "";
            var generatedContent = new StringBuilder();

            var chatHistory = new ChatHistory();
            if (!string.IsNullOrEmpty(SystemPrompt))
            {
                chatHistory.AddSystemMessage(SystemPrompt);
            }
            chatHistory.AddUserMessage(UserPrompt); // 添加用户输入到历史
            try
            {
                await foreach (var chunk in NewDeepSeekManager.Instance._chatService.GetStreamingChatMessageContentsAsync(chatHistory))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        Console.Write(chunk.Content);
                        generatedContent.Append(chunk.Content);
                    }
                }
                if (!Directory.Exists(NewDeepSeekManager.Instance.baseTemplatePath))
                    Directory.CreateDirectory(NewDeepSeekManager.Instance.baseTemplatePath);

                string outputPath = Path.Combine(NewDeepSeekManager.Instance.baseTemplatePath, $"{filename}.txt");
                await File.WriteAllTextAsync(outputPath, generatedContent.ToString());
                TxtContent = generatedContent.ToString();
                //return generatedContent.ToString();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成内容时出错: {ex.Message}");
                throw;
            }
            finally
            {
                // 确保不再需要时清空 StringBuilder
                generatedContent.Clear();
            }
        }

        public async Task NewStreamDeepSeekResponseToClient()
        {
            try
            {
                // 为新对话创建或获取现有的 StringBuilder
                State = ConversationState.running;

                var chatHistory = new ChatHistory();

                // 添加系统提示（如果有）
                if (!string.IsNullOrEmpty(SystemPrompt))
                {
                    chatHistory.AddSystemMessage(SystemPrompt);
                }
                // 添加用户消息
                chatHistory.AddUserMessage(UserPrompt);
                // 使用取消令牌
                var cancellationToken = _cancellationTokenSource.Token;
                await foreach (var chunk in NewDeepSeekManager.Instance._chatService.GetStreamingChatMessageContentsAsync(chatHistory).WithCancellation(cancellationToken))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        Console.WriteLine(chunk.Content);
                        await AddContent(chunk.Content.ToString());
                    }
                    if (cancellationToken.IsCancellationRequested)
                    {
                        Console.WriteLine("对话生成被取消");
                        break;
                    }
                }
                State = ConversationState.finished;
                await EndConversation();
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("对话生成被取消");
                State = ConversationState.canceled;
                await EndConversation("对话生成被取消");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成对话时出错: {ex.Message}");
                State = ConversationState.finished;
                await EndConversation(ex.Message);
            }
        }

        // 添加一个方法来处理对话内容获取和流式输出
        public async Task GetConversationContent()
        {
            try
            {
                // 如果对话已完成，直接返回完整内容
                if (State == ConversationState.finished || State == ConversationState.canceled)
                {
                    Context.Response.ContentType = "text/event-stream; charset=utf-8";
                    Context.Response.Headers.Add("Cache-Control", "no-cache");
                    Context.Response.Headers.Add("Connection", "keep-alive");
                    Context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

                    // 确保返回完整内容
                    string content = ConversationContent.ToString();
                    Console.WriteLine($"返回已完成对话内容，ID: {ConversationId}, 内容长度: {content.Length}");

                    await Context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = content, conversationId = ConversationId, isCompleted = true, state = State.ToString() })}\n\n");
                    await Context.Response.WriteAsync("data: [DONE]\n\n");
                    await Context.Response.Body.FlushAsync();
                    return;
                }

                // 对话仍在进行中，建立SSE连接
                await EstablishSSEConnection(Context);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取对话内容时出错: {ex.Message}");
                // 尝试返回错误信息
                if (!Context.Response.HasStarted)
                {
                    Context.Response.ContentType = "application/json";
                    Context.Response.StatusCode = 500;
                    await Context.Response.WriteAsync(JsonConvert.SerializeObject(new { error = ex.Message }));
                }
            }
        }
        // 添加一个通用方法来处理SSE连接和内容流式传输
        public async Task EstablishSSEConnection(HttpContext context, bool sendInitialContent = true)
        {
            // 设置SSE响应头
            context.Response.ContentType = "text/event-stream; charset=utf-8";
            context.Response.Headers.Add("Cache-Control", "no-cache");
            context.Response.Headers.Add("Connection", "keep-alive");
            context.Response.Headers.Add("Access-Control-Allow-Origin", "*");

            // 将当前HttpContext添加到对话的HttpContext字典中
            if (!HttpcontextDic.ContainsKey(context))
            {
                HttpcontextDic.TryAdd(context, false);
                Console.WriteLine($"添加新的HttpContext到对话 {ConversationId}");
            }

            // 发送初始内容（如果需要）
            if (sendInitialContent)
            {
                await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = ConversationContent.ToString(), conversationId = ConversationId, isHistory = true, state = State.ToString() })}\n\n");
                await context.Response.Body.FlushAsync();
                HttpcontextDic[context] = true;
            }

            // 等待对话完成或客户端断开连接
            while ((State == ConversationState.running) && !context.RequestAborted.IsCancellationRequested)
            {
                await Task.Delay(100);
            }

            // 如果不是因为客户端断开连接而结束，发送完成标记
            if (!context.RequestAborted.IsCancellationRequested)
            {
                if (State == ConversationState.canceled)
                {
                    await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { canceled = true, conversationId = ConversationId })}\n\n");
                }
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
            }


        }
        //添加生成对话的内容,并发送给前端
        public async Task AddContent(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                Console.WriteLine("收到空内容，跳过");
                return;
            }

            ConversationContent.Append(str);
            List<HttpContext> disconnectedContexts = new List<HttpContext>();
            foreach (var data in HttpcontextDic)
            {
                HttpContext context = data.Key;
                try
                {
                    if (context.RequestAborted.IsCancellationRequested)
                    {
                        Console.WriteLine("客户端已断开连接");
                        disconnectedContexts.Add(context);
                        continue;
                    }

                    if (data.Value == false)
                    {
                        // 第一次发送完整内容
                        Console.WriteLine("首次发送完整内容");
                        await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = ConversationContent.ToString(), conversationId = ConversationId, isHistory = true })}\n\n");
                        await context.Response.Body.FlushAsync();
                        HttpcontextDic[context] = true;
                    }
                    else
                    {
                        // 将流式实时生成的内容发送给客户端
                        //Console.WriteLine("发送增量内容");
                        await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { content = str, conversationId = ConversationId, isHistory = false })}\n\n");
                        await context.Response.Body.FlushAsync();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发送内容时出错: {ex.Message}");
                    disconnectedContexts.Add(context);
                }
            }

            // 移除断开连接的上下文
            foreach (var context in disconnectedContexts)
            {
                HttpcontextDic.TryRemove(context, out _);
                Console.WriteLine("移除断开的客户端连接");
            }
        }
        //结束对话
        public async Task EndConversation(string info = "")
        {
            // 发送结束标记
            foreach (var data in HttpcontextDic)
            {
                HttpContext context = data.Key;
                //if (IsClientConnected(context) == true)
                //{
                if (!string.IsNullOrEmpty(info))
                {
                    await context.Response.WriteAsync($"data: {JsonConvert.SerializeObject(new { error = info })}\n\n");
                }
                await context.Response.WriteAsync("data: [DONE]\n\n");
                await context.Response.Body.FlushAsync();
                //}

            }

        }

        // 添加一个方法来批量创建多个对话
        public static async Task<List<AiConversation>> CreateMultipleConversations(HttpContext context, List<string> prompts, string systemPrompt = null)
        {
            List<AiConversation> conversations = new List<AiConversation>();

            foreach (string prompt in prompts)
            {
                AiConversation conversation = new AiConversation
                {
                    ConversationId = $"multi-{Guid.NewGuid()}",
                    UserPrompt = prompt,
                    SystemPrompt = systemPrompt,
                    State = ConversationState.notStarted,
                    ConversationContent = new StringBuilder()
                };

                conversations.Add(conversation);
            }

            return conversations;
        }
        // 添加一个方法来批量创建多个对话
        public static async Task<List<AiConversation>> NewCreateMultipleConversations(HttpContext context, Dictionary<string, InputPromptData> promptDic)
        {
            List<AiConversation> conversations = new List<AiConversation>();

            foreach (var prompt in promptDic)
            {
                InputPromptData promptData = prompt.Value;
                AiConversation conversation = new AiConversation
                {
                    ConversationId = $"multi-{Guid.NewGuid()}",
                    baseId = prompt.Key,
                    UserPrompt = promptData.userPrompt,
                    SystemPrompt = promptData.systemPrompt,
                    State = ConversationState.notStarted,
                    ConversationContent = new StringBuilder()
                };
                try {
                    AI_Game_OutputData data = new AI_Game_OutputData();
                    data.id = conversation.ConversationId;
                    data.systemPrompt = promptData.systemPrompt;
                    data.userPrompt = promptData.userPrompt;
                    conversations.Add(conversation);
                    
                    using (ORMTables db = new ORMTables())
                    {
                        db.AI_Game_OutputDatas.Add(data);
                        int result = db.SaveChanges();
                        Console.WriteLine($"保存数据结果: {result}条记录受影响, ID: {data.id}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"保存AI_Game_OutputData失败: {ex.Message}");
                    Console.WriteLine(ex.StackTrace);
                }
            }

            return conversations;
        }

        // 添加一个方法来异步启动对话生成
        public async Task StartGenerationAsync()
        {
            // 创建一个新的任务来运行对话生成
            _ = Task.Run(async () =>
            {
                await NewStreamDeepSeekResponseToClient();
            });
        }
        // 添加一个方法来异步启动对话生成
        public async Task StartGenerationTxtAsync(string filename)
        {
            // 创建一个新的任务来运行对话生成
            _ = Task.Run(async () =>
            {
                await GenerateTextContent(filename);
            });
        }

        // 添加取消对话的方法
        public void CancelConversation()
        {
            try
            {
                if (State == ConversationState.running)
                {
                    // 取消操作
                    _cancellationTokenSource.Cancel();
                    State = ConversationState.canceled;
                    Console.WriteLine($"对话 {ConversationId} 已手动取消");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取消对话时出错: {ex.Message}");
            }
        }
        /// <summary>
        /// 去除 ```json  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public string RemoveJSONCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```json");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```json".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }
        /// <summary>
        /// 去除 ```markdown  ```
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public string RemoveMarkdownCodeBlocks(string input)
        {
            // 移除开头的```markdown
            int startIndex = input.IndexOf("```markdown");
            if (startIndex >= 0)
            {
                input = input.Substring(startIndex + "```markdown".Length);
            }

            // 移除结尾的```
            int endIndex = input.LastIndexOf("```");
            if (endIndex >= 0)
            {
                input = input.Substring(0, endIndex);
            }

            return input.Trim();
        }
        // 清空内容并复用对象
        public void ClearConversationContent()
        {
            ConversationContent.Clear(); // 清空内容，但保留底层内存缓冲区
        }
        public static bool IsClientConnected(HttpContext context)
        {
            return context != null
                   && !context.RequestAborted.IsCancellationRequested
                   && (context.Response == null || !context.Response.HasStarted);
        }
        public enum ConversationState
        {
            notStarted,
            running,
            canceled,
            finished
        }
    }
}
