﻿using GameServer.GameService; // 引入游戏服务相关的命名空间
using NetService; // 引入网络服务相关的命名空间
using System; // 引入系统基础类库
using System.Collections.Generic; // 引入泛型集合类库
using System.IO; // 引入输入输出流相关类库
using System.Linq; // 引入LINQ查询扩展方法
using System.Net.WebSockets; // 引入WebSocket网络通信类库
using System.Text; // 引入文本编码相关类库
using System.Threading; // 引入线程相关类库
using System.Threading.Tasks; // 引入异步任务相关类库

namespace SaveDataService.Server.Websocket // 定义SaveDataService.Server.Websocket命名空间
{
    class WebsocketClient : INet // 定义WebsocketClient类，继承自INet接口
    {
        public WebsocketService websocketService { get; set; } // 定义websocket服务的公共属性，用于获取和设置websocket服务实例


        private WebSocket _websocket; // 定义私有的WebSocket字段，用于存储websocket连接实例

        public WebSocket websocket // 定义websocket的公共属性
        {
            get // 属性的getter方法
            {
                return _websocket; // 返回私有的_websocket字段值
            }
            set // 属性的setter方法
            {
                _websocket = value; // 将传入的值赋给私有的_websocket字段
                ///初始化客户端的额很多消息 // 注释：初始化客户端的很多消息
            }
        }



        public void ReceiveAsync(byte[] bytes, CancellationToken cancellationToken) // 定义异步接收消息的公共方法，接收字节数组和取消令牌参数
        {
            // 方法体为空，暂未实现具体逻辑
        }


        private NetEnum state = NetEnum.Init; // 定义私有的网络状态字段，初始值为Init（初始化状态）
        private string host = null; // 定义私有的主机地址字段，初始值为null

        private object userdata; // 定义私有的用户数据字段，用于存储任意类型的用户数据
        private CancellationTokenSource tokenSource = new CancellationTokenSource(); // 定义私有的取消令牌源，用于控制异步操作的取消
        public void SetUserData(object userdata) // 定义设置用户数据的公共方法
        {
            this.userdata = userdata; // 将传入的用户数据赋值给当前实例的userdata字段
        }
        public T GetUserData<T>() where T : class // 定义泛型方法获取用户数据，约束T必须是引用类型
        {
            return userdata as T; // 将userdata转换为指定类型T并返回
        }
        /// <summary>
        /// 发送的消息类型默认值:Text
        /// </summary>
        public static WebSocketMessageType sendType = WebSocketMessageType.Text; // 定义静态字段，设置发送消息的默认类型为文本类型

        public ulong id = 0; // 定义公共的客户端ID字段，类型为无符号长整型，初始值为0


        public override event Action OnClose; // 定义重写的关闭事件，当连接关闭时触发
        public override event Action<byte[]> OnRecv; // 定义重写的接收事件，当接收到数据时触发，参数为字节数组
        internal MemoryStream recvStream; // 定义内部的内存流字段，用于接收数据的缓冲

        public WebsocketClient() // 定义无参构造函数
        {
            new MemoryStream(new byte[ushort.MaxValue]); // 创建一个新的内存流实例，缓冲区大小为ushort的最大值（65535字节）
        }
        public WebsocketClient(MemoryStream recvStream) // 定义带参数的构造函数，接收一个内存流参数
        {
            this.recvStream = recvStream; // 将传入的内存流赋值给当前实例的recvStream字段
        }

        public override async Task<bool> Connect(string ip, int port = 0) // 定义重写的异步连接方法，接收IP地址和端口参数（端口默认为0）
        {
            try // 开始异常处理块
            {
                host = ip; // 将传入的IP地址保存到host字段
                var _sock = new ClientWebSocket(); // 创建一个新的客户端WebSocket实例
                state = NetEnum.Cnnecting; // 设置连接状态为正在连接
                await _sock.ConnectAsync(new Uri(ip), CancellationToken.None); // 异步连接到指定的URI，使用无取消令牌
                state = NetEnum.Connected; // 设置连接状态为已连接
                websocket = _sock; // 将创建的WebSocket实例赋值给websocket属性

                await RecvMessage(); // 异步调用接收消息方法
                _OnClose(); // 调用内部关闭方法
            }
            catch (Exception e) // 捕获异常
            {
                state = NetEnum.Error; // 设置连接状态为错误
                Console.WriteLine("000007   " + e.StackTrace); // 输出异常堆栈信息到控制台
                return false; // 返回false表示连接失败
            }
            return true; // 返回true表示连接成功
        }

        public override async Task<bool> DisConnect() // 定义重写的异步断开连接方法
        {
            if (websocket != null && websocket.State == WebSocketState.Open) // 检查websocket不为空且状态为打开
            {
                await websocket.CloseAsync(WebSocketCloseStatus.Empty, "", CancellationToken.None); // 异步关闭websocket连接，使用空状态码和空消息
                state = NetEnum.Disconnetc; // 设置连接状态为已断开
                return true; // 返回true表示断开成功
            }
            return false; // 返回false表示断开失败
        }

        public override NetEnum NetState() // 定义重写的获取网络状态方法
        {
            return state; // 返回当前的网络连接状态
        }


        public override Task<bool> ReConnect() // 定义重写的重新连接方法
        {
            state = NetEnum.ReConnecting; // 设置连接状态为正在重连
            return Connect(host); // 调用连接方法重新连接到之前保存的主机地址
        }

        public override async Task<bool> SendMessage(byte[] bytes) // 定义重写的异步发送消息方法，接收字节数组参数
        {
            if (websocket != null && websocket.State == WebSocketState.Open) // 检查websocket不为空且状态为打开
            {
                await websocket.SendAsync(bytes, sendType, true, CancellationToken.None); // 异步发送字节数据，使用预设的消息类型，标记为消息结束，无取消令牌
                return true; // 返回true表示发送成功
            }
            return false; // 返回false表示发送失败
        }


        internal void _OnClose() // 定义内部的关闭处理方法
        {
            OnClose(); // 触发OnClose事件
        }

        internal void _OnRecv(byte[] bytes) // 定义内部的接收处理方法，接收字节数组参数
        {
            OnRecv(bytes); // 触发OnRecv事件，传递接收到的字节数据
        }


        /// <summary>
        /// 读取服务器过来的消息
        /// </summary>
        /// <param name="cb"></param>
        /// <returns></returns>
        internal async Task RecvMessage(Action<byte[]> cb = null) // 定义内部异步接收消息方法，可选回调函数参数
        {
            while (websocket.State == WebSocketState.Open) // 当websocket状态为打开时循环执行
            {
                try // 开始异常处理块
                {
                    int receiveCount = 0; // 初始化接收计数器为0
                    ValueWebSocketReceiveResult recv; // 声明WebSocket接收结果变量
                    do // 开始do-while循环
                    {


                        var memory = new Memory<byte>(recvStream.GetBuffer(), receiveCount, recvStream.Capacity - receiveCount); // 创建内存缓冲区，从接收流获取缓冲区，指定起始位置和剩余容量
                        recv = await websocket.ReceiveAsync(memory, tokenSource.Token); // 异步接收数据到内存缓冲区，使用取消令牌
                        receiveCount += recv.Count; // 累加接收到的字节数



                    }


                    while (!recv.EndOfMessage); // 当消息未结束时继续循环
                    if (recv.Count == 0) // 如果接收到的字节数为0
                        continue; // 跳过本次循环，继续下一次
                    recvStream.SetLength(recv.Count); // 设置接收流的长度为实际接收的字节数
                    if (IsAccept) // 如果是接受模式
                    {
                        cb?.Invoke(recvStream.ToArray()); // 如果回调函数不为空，则调用回调函数并传递接收流的字节数组
                    }
                    else // 否则（非接受模式）
                    {
                        try // 开始内部异常处理块
                        {
                            byte[] bytes = recvStream.ToArray(); // 将接收流转换为字节数组
                            //Console.WriteLine($"websocketclient收到客户端{id}的数据[]:{Encoding.UTF8.GetString(bytes)}"); // 注释：输出接收到的客户端数据

                            ClientData data = new ClientData(); // 创建新的客户端数据实例
                            data.websocketBytes = bytes; // 设置客户端数据的字节数组
                            data.websocket = websocket; // 设置客户端数据的websocket连接
                            data.clientID = id; // 设置客户端数据的客户端ID
                            websocketService.handClientMessage(data); // 调用websocket服务处理客户端消息

                        }
                        catch (Exception e) // 捕获内部异常
                        {
                            websocket.Dispose(); // 释放websocket资源
                            Console.WriteLine("000006   " + e.Message); // 输出异常消息到控制台
                            throw; // 重新抛出异常
                        }
                    }
                }
                catch (Exception e) // 捕获外部异常
                {
                    websocket.Dispose(); // 释放websocket资源
                    Console.WriteLine("000005   " + e.Message); // 输出异常消息到控制台
                }
            }
            Console.WriteLine($"收到客户端{id}的信息可能断开链接了，当前状态为：" + websocket.State); // 输出客户端可能断开连接的信息和当前状态
            //既然websocket断开了相关资源要做相应清理 // 注释：既然websocket断开了相关资源要做相应清理
            //websocket.CloseOutputAsync(WebSocketCloseStatus.NormalClosure); // 注释：异步关闭websocket输出流

        } // RecvMessage方法结束


    } // WebsocketClient类结束
} // 命名空间结束
