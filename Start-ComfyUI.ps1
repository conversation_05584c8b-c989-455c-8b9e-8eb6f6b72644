# ComfyUI 启动脚本
# 用于启动ComfyUI服务器以便进行真实测试

param(
    [string]$ComfyUIPath = "C:\ComfyUI",
    [int]$Port = 8888,
    [string]$Host = "127.0.0.1"
)

Write-Host "===================================" -ForegroundColor Cyan
Write-Host "ComfyUI 服务器启动脚本" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# 检查ComfyUI路径
if (-not (Test-Path $ComfyUIPath)) {
    Write-Host "❌ ComfyUI路径不存在: $ComfyUIPath" -ForegroundColor Red
    Write-Host ""
    Write-Host "请指定正确的ComfyUI安装路径:" -ForegroundColor Yellow
    Write-Host "  .\Start-ComfyUI.ps1 -ComfyUIPath 'C:\path\to\ComfyUI'" -ForegroundColor White
    Write-Host ""
    Write-Host "或者下载并安装ComfyUI:" -ForegroundColor Yellow
    Write-Host "  1. 访问 https://github.com/comfyanonymous/ComfyUI" -ForegroundColor White
    Write-Host "  2. 下载并解压到本地目录" -ForegroundColor White
    Write-Host "  3. 安装Python依赖" -ForegroundColor White
    exit 1
}

# 检查Python环境
try {
    $pythonVersion = python --version 2>&1
    Write-Host "检测到Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未找到Python，请先安装Python 3.8+" -ForegroundColor Red
    exit 1
}

# 检查是否已经有ComfyUI在运行
try {
    $response = Invoke-WebRequest -Uri "http://${Host}:${Port}/" -TimeoutSec 3 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ ComfyUI已经在运行: http://${Host}:${Port}" -ForegroundColor Green
        Write-Host ""
        Write-Host "现在可以运行AceStepAll真实测试:" -ForegroundColor Cyan
        Write-Host "  dotnet run" -ForegroundColor White
        Write-Host "  然后选择选项 19" -ForegroundColor White
        exit 0
    }
} catch {
    Write-Host "ComfyUI未运行，准备启动..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "启动参数:" -ForegroundColor Cyan
Write-Host "  ComfyUI路径: $ComfyUIPath" -ForegroundColor White
Write-Host "  监听地址: $Host" -ForegroundColor White
Write-Host "  端口: $Port" -ForegroundColor White
Write-Host ""

# 切换到ComfyUI目录
Push-Location $ComfyUIPath

try {
    Write-Host "正在启动ComfyUI服务器..." -ForegroundColor Yellow
    Write-Host "请等待服务器完全启动后再运行测试" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "启动完成后，您将看到类似以下信息:" -ForegroundColor Cyan
    Write-Host "  'Starting server'" -ForegroundColor Gray
    Write-Host "  'To see the GUI go to: http://127.0.0.1:8888'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "然后可以在另一个终端运行:" -ForegroundColor Green
    Write-Host "  dotnet run" -ForegroundColor White
    Write-Host "  选择选项 19 进行真实ComfyUI测试" -ForegroundColor White
    Write-Host ""
    Write-Host "按 Ctrl+C 停止ComfyUI服务器" -ForegroundColor Yellow
    Write-Host ""
    
    # 启动ComfyUI
    python main.py --listen $Host --port $Port
    
} catch {
    Write-Host "❌ 启动ComfyUI失败: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "可能的解决方案:" -ForegroundColor Yellow
    Write-Host "  1. 检查Python依赖是否安装完整" -ForegroundColor White
    Write-Host "  2. 检查端口 $Port 是否被占用" -ForegroundColor White
    Write-Host "  3. 检查ComfyUI目录是否包含main.py文件" -ForegroundColor White
    Write-Host "  4. 尝试手动运行: python main.py --listen $Host --port $Port" -ForegroundColor White
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "ComfyUI服务器已停止" -ForegroundColor Gray
