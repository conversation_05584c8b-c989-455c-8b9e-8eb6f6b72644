# AceStepAll 简单测试脚本
# PowerShell版本 - 自动运行测试

Write-Host "===================================" -ForegroundColor Cyan
Write-Host "AceStepAll 简单测试脚本" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "正在运行 AceStepAll 音乐生成工作流测试..." -ForegroundColor Yellow
Write-Host "这将自动选择选项 18 并运行测试" -ForegroundColor Yellow
Write-Host ""

# 检查.NET环境
try {
    $dotnetVersion = dotnet --version
    Write-Host "检测到 .NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未找到 .NET 运行时，请先安装 .NET" -ForegroundColor Red
    exit 1
}

# 检查ComfyUI服务器状态
function Test-ComfyUIServer {
    param([string]$Url = "http://127.0.0.1:8888")
    
    Write-Host "检查ComfyUI服务器状态: $Url" -ForegroundColor Yellow
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ ComfyUI服务器在线" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "⚠️ ComfyUI服务器离线或无法访问" -ForegroundColor Yellow
        Write-Host "   请确保ComfyUI在 $Url 运行" -ForegroundColor Yellow
        Write-Host "   测试仍将继续，但可能无法完成实际的音乐生成" -ForegroundColor Yellow
        return $false
    }
}

# 检查服务器状态
Test-ComfyUIServer

Write-Host ""
Write-Host "开始运行测试..." -ForegroundColor Green
Write-Host ""

try {
    # 切换到SaveDataService目录并运行测试
    Push-Location "SaveDataService"
    
    # 使用管道输入选项 18
    "18" | dotnet run
    
    Write-Host ""
    Write-Host "✅ 测试执行完成！" -ForegroundColor Green
    
} catch {
    Write-Host "❌ 测试执行失败: $_" -ForegroundColor Red
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "测试脚本执行完成！" -ForegroundColor Green
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
