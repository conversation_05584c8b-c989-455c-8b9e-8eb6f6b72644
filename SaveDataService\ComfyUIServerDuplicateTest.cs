using System;
using System.Threading.Tasks;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI服务器重复检查测试类
    /// </summary>
    public class ComfyUIServerDuplicateTest
    {
        /// <summary>
        /// 运行重复检查测试
        /// </summary>
        public static async Task RunDuplicateTests()
        {
            Console.WriteLine("=== ComfyUI服务器重复检查测试开始 ===");

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 1. 测试添加第一个服务器（应该成功）
                Console.WriteLine("\n1. 测试添加第一个服务器...");
                var serverId1 = comfyUIManage.AddServer(
                    "测试服务器1",
                    "127.0.0.1",
                    8888,
                    3,
                    "第一个测试服务器"
                );

                if (!string.IsNullOrEmpty(serverId1))
                {
                    Console.WriteLine($"✅ 第一个服务器添加成功: {serverId1}");
                }
                else
                {
                    Console.WriteLine("❌ 第一个服务器添加失败");
                    return;
                }

                // 2. 测试添加相同IP和端口的服务器（应该失败）
                Console.WriteLine("\n2. 测试添加相同IP和端口的服务器（应该失败）...");
                try
                {
                    var serverId2 = comfyUIManage.AddServer(
                        "测试服务器2",
                        "127.0.0.1",
                        8888,
                        5,
                        "重复的测试服务器"
                    );

                    if (string.IsNullOrEmpty(serverId2))
                    {
                        Console.WriteLine("✅ 正确阻止了重复服务器的添加");
                    }
                    else
                    {
                        Console.WriteLine($"❌ 错误：重复服务器被添加了: {serverId2}");
                    }
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"✅ 正确捕获到重复异常: {ex.Message}");
                }

                // 3. 测试添加不同IP和端口的服务器（应该成功）
                Console.WriteLine("\n3. 测试添加不同IP和端口的服务器...");
                var serverId3 = comfyUIManage.AddServer(
                    "测试服务器3",
                    "127.0.0.1",
                    8889,
                    3,
                    "不同端口的测试服务器"
                );

                if (!string.IsNullOrEmpty(serverId3))
                {
                    Console.WriteLine($"✅ 不同端口的服务器添加成功: {serverId3}");
                }
                else
                {
                    Console.WriteLine("❌ 不同端口的服务器添加失败");
                }

                // 4. 测试添加不同IP相同端口的服务器（应该成功）
                Console.WriteLine("\n4. 测试添加不同IP相同端口的服务器...");
                var serverId4 = comfyUIManage.AddServer(
                    "测试服务器4",
                    "*************",
                    8888,
                    3,
                    "不同IP的测试服务器"
                );

                if (!string.IsNullOrEmpty(serverId4))
                {
                    Console.WriteLine($"✅ 不同IP的服务器添加成功: {serverId4}");
                }
                else
                {
                    Console.WriteLine("❌ 不同IP的服务器添加失败");
                }

                // 5. 测试更新服务器为重复的IP和端口（应该失败）
                Console.WriteLine("\n5. 测试更新服务器为重复的IP和端口（应该失败）...");
                try
                {
                    var updateResult = comfyUIManage.UpdateServer(
                        serverId3,
                        "更新的测试服务器3",
                        "127.0.0.1",
                        8888,  // 与serverId1相同的端口
                        5,
                        "尝试更新为重复IP和端口"
                    );

                    if (!updateResult)
                    {
                        Console.WriteLine("✅ 正确阻止了更新为重复的IP和端口");
                    }
                    else
                    {
                        Console.WriteLine("❌ 错误：允许了更新为重复的IP和端口");
                    }
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"✅ 正确捕获到更新重复异常: {ex.Message}");
                }

                // 6. 测试更新服务器为不重复的IP和端口（应该成功）
                Console.WriteLine("\n6. 测试更新服务器为不重复的IP和端口...");
                var updateResult2 = comfyUIManage.UpdateServer(
                    serverId3,
                    "更新的测试服务器3",
                    "*************",
                    9000,
                    5,
                    "更新为不重复的IP和端口"
                );

                if (updateResult2)
                {
                    Console.WriteLine("✅ 成功更新为不重复的IP和端口");
                }
                else
                {
                    Console.WriteLine("❌ 更新为不重复的IP和端口失败");
                }

                // 7. 显示当前所有服务器
                Console.WriteLine("\n7. 当前所有服务器列表:");
                var allServers = comfyUIManage.GetAllServers();
                foreach (var server in allServers)
                {
                    Console.WriteLine($"   - ID: {server.id}, 名称: {server.serverName}, 地址: {server.serverUrl}:{server.port}");
                }

                // 8. 清理测试数据
                Console.WriteLine("\n8. 清理测试数据...");
                if (!string.IsNullOrEmpty(serverId1))
                {
                    comfyUIManage.RemoveServer(serverId1);
                    Console.WriteLine($"   删除服务器: {serverId1}");
                }
                if (!string.IsNullOrEmpty(serverId3))
                {
                    comfyUIManage.RemoveServer(serverId3);
                    Console.WriteLine($"   删除服务器: {serverId3}");
                }
                if (!string.IsNullOrEmpty(serverId4))
                {
                    comfyUIManage.RemoveServer(serverId4);
                    Console.WriteLine($"   删除服务器: {serverId4}");
                }

                Console.WriteLine("\n✅ 重复检查测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试IsServerExists方法
        /// </summary>
        public static void TestIsServerExists()
        {
            Console.WriteLine("\n=== 测试IsServerExists方法 ===");

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 添加一个测试服务器
                var serverId = comfyUIManage.AddServer(
                    "存在性测试服务器",
                    "127.0.0.1",
                    7777,
                    3,
                    "用于测试IsServerExists方法"
                );

                if (!string.IsNullOrEmpty(serverId))
                {
                    Console.WriteLine($"✅ 测试服务器添加成功: {serverId}");

                    // 测试存在的服务器
                    bool exists1 = comfyUIManage.IsServerExists("127.0.0.1", 7777);
                    Console.WriteLine($"测试存在的服务器 (127.0.0.1:7777): {(exists1 ? "✅ 正确检测到存在" : "❌ 错误：未检测到存在")}");

                    // 测试不存在的服务器
                    bool exists2 = comfyUIManage.IsServerExists("127.0.0.1", 7778);
                    Console.WriteLine($"测试不存在的服务器 (127.0.0.1:7778): {(!exists2 ? "✅ 正确检测到不存在" : "❌ 错误：错误检测为存在")}");

                    bool exists3 = comfyUIManage.IsServerExists("192.168.1.1", 7777);
                    Console.WriteLine($"测试不存在的服务器 (192.168.1.1:7777): {(!exists3 ? "✅ 正确检测到不存在" : "❌ 错误：错误检测为存在")}");

                    // 清理测试数据
                    comfyUIManage.RemoveServer(serverId);
                    Console.WriteLine($"✅ 清理测试服务器: {serverId}");
                }
                else
                {
                    Console.WriteLine("❌ 测试服务器添加失败");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ IsServerExists测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试Init方法 - 初始化所有服务器并更新连通性和硬件信息
        /// </summary>
        public static async Task TestInitMethod()
        {
            Console.WriteLine("\n=== 测试Init方法 - 服务器初始化 ===");

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                Console.WriteLine("开始调用Init方法...\n");

                // 调用Init方法
                var result = await comfyUIManage.Init();

                Console.WriteLine("\n=== Init方法执行结果 ===");
                Console.WriteLine($"结果类型: {result.GetType().Name}");

                // 如果结果是动态对象，尝试显示其属性
                if (result != null)
                {
                    var resultType = result.GetType();
                    var properties = resultType.GetProperties();

                    foreach (var prop in properties)
                    {
                        try
                        {
                            var value = prop.GetValue(result);
                            if (prop.Name == "Results" && value is System.Collections.IEnumerable enumerable)
                            {
                                Console.WriteLine($"{prop.Name}: [集合，包含详细结果]");
                            }
                            else
                            {
                                Console.WriteLine($"{prop.Name}: {value}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"{prop.Name}: 获取值失败 - {ex.Message}");
                        }
                    }
                }

                Console.WriteLine("\n✅ Init方法测试完成！");
                Console.WriteLine("请检查数据库中ComfyUIServer表的status和description字段是否已更新。");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Init方法测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }
        }
    }
}
