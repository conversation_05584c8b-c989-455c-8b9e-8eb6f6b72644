﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using MySqlConnector;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;

namespace GameServer.Util
{
    class MysqlUtil
    {
        /// <summary>
        /// 上一次备份的结构 表名称
        /// </summary>
        private const string UPMODELTABLE = "__$upmodel";


        /// <summary>
        /// 检查结构备份表是否存在
        /// </summary>
        /// <param name="dbName"></param>
        /// <returns></returns>
        public static bool CheckOrCreateUpModel(string dbName, string mysqlConnectString)
        {
            var reader = GetReader($"select count(table_name) as count from information_schema.tables where table_schema = '{dbName}' and table_name='{UPMODELTABLE}'", mysqlConnectString);
            reader.Read();
            int count = reader.GetInt32(0);
            Console.WriteLine($"检查模型结构 :{count}");
            if (count > 0)
                return true;
            ExecuteSqlRaw($"CREATE TABLE `{dbName}`.`{UPMODELTABLE}` ( `id` INT NOT NULL AUTO_INCREMENT , `data` LONGTEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL , PRIMARY KEY (`id`)) ENGINE = InnoDB;", mysqlConnectString);
            ExecuteSqlRaw($"INSERT INTO `{dbName}`.`{UPMODELTABLE}` (`id`, `data`) VALUES (NULL, '');", mysqlConnectString);
            return false;
        }



        /// <summary>
        /// 讲ef读取出来的orm model的数据库表格式数据结构序列化成json存起来
        /// </summary>
        /// <param name="model"></param>
        public static void saveDBStructure(IModel model)
        {
            Console.WriteLine("设置模型结构");
            string jsonString = ModelToJsonString(model);
            ExecuteSqlRaw("UPDATE `" + UPMODELTABLE + "` SET `data`={0};", jsonString);

        }

        /// <summary>
        /// 将现有的数据库表的结构数据从数据库中读取出来序列化成json
        /// </summary>
        /// <param name="model"></param>
        public static dynamic readDBStructure(string mysqlConnectString)
        {
            var reader = GetReader($"select data from {UPMODELTABLE}", mysqlConnectString);
            reader.Read();
            var data = reader.GetString(0);
            var modelJson = JsonConvert.SerializeObject(data);
            return modelJson;

        }


        /// <summary>
        /// 表结构序列化
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static string ModelToJsonString(IModel model)
        {
            IDictionary<string, object> cloneObj = new ExpandoObject();
            foreach (var entityType in model.GetEntityTypes())
            {

                dynamic jsonObj = new ExpandoObject();
                cloneObj[entityType.DisplayName().ToLower() + "s"] = jsonObj;
                var properties = new List<dynamic>();
                foreach (var property in entityType.GetDeclaredProperties())
                {
                    dynamic propertyObj = new ExpandoObject();
                    propertyObj.IsNullable = property.IsNullable;
                    propertyObj.IsConcurrencyToken = property.IsConcurrencyToken;
                    propertyObj.ValueGenerated = property.ValueGenerated;
                    propertyObj.BeforeSaveBehavior = property.GetBeforeSaveBehavior();
                    propertyObj.AfterSaveBehavior = property.GetBeforeSaveBehavior();
                    propertyObj.CliType = property.ClrType.FullName;
                    //propertyObj.Annotations = property.GetAnnotations();
                    propertyObj.Name = property.Name;
                    properties.Add(propertyObj);
                }
                jsonObj.properties = properties;

                var declaredKeys = new List<dynamic>();
                foreach (var key in entityType.GetDeclaredKeys())
                {
                    dynamic declaredKey = new ExpandoObject();
                    declaredKey.keys = key.Properties.Select(s => s.Name).ToList();
                    declaredKey.IsPrimaryKey = key.IsPrimaryKey();
                    //declaredKey.Annotations = key.GetAnnotations();
                    declaredKeys.Add(declaredKey);
                }
                jsonObj.declaredKeys = declaredKeys;

                var indexs = new List<dynamic>();
                foreach (var index in entityType.GetDeclaredIndexes())
                {
                    dynamic indexobj = new ExpandoObject();
                    indexobj.keys = index.Properties.Select(p => p.Name).ToList();
                    indexobj.IsUnique = index.IsUnique;
                    //indexobj.Annotations = index.GetAnnotations();
                    indexs.Add(indexobj);
                }
                jsonObj.indexs = indexs;

            }
            var jsonstr = JsonConvert.SerializeObject(cloneObj);
            return jsonstr;
        }






        /// <summary>
        /// 检测数据表是否初始化   注意这里有潜规则，因为我们使用了log4的组件库 很有可能会把所有的日志写入的数据库当中去 ，所以可能会有一种叫logs的表这个表存在 不代表orm被初始化过。
        /// </summary>
        /// <param name="dbName"></param>
        /// <param name="mysqlConnectString"></param>
        /// <returns></returns>
        public static bool TableIsNull(string dbName, string mysqlConnectString)
        {
            Console.WriteLine("检测数据表是否初始化..");
            var reader = GetReader($"select table_name from information_schema.tables where table_schema = '{dbName}'",mysqlConnectString);

            List<string> tableNames = new List<string>();


            while (reader.Read())
            {
                tableNames.Add(reader[0].ToString());
                //Console.WriteLine("表名字为：" + reader[0].ToString());
            }

            if ((tableNames.Count == 1&& tableNames[0] != "logs")|| tableNames.Count > 1)
            {
                Console.WriteLine("数据表已经过初始化..");
                return true;
            }


            Console.WriteLine("数据表未经过初始化..");
            return false;
        }





        /// <summary>
        /// 执行sql命令判断数据库是否存在
        /// </summary>
        /// <param name="dbName"></param>
        /// <param name="mysqlConnectString"></param>
        /// <returns></returns>
        public static bool DBExists(string dbName, string mysqlConnectString)
        {
            Console.WriteLine($"检查数据库:{dbName}...");
            try
            {
                var reader = GetReader($"show databases;", mysqlConnectString);
                    while (reader.Read())
                    {
                        string findName = reader.GetString(0);
                        if (findName == dbName)
                        {
                            Console.WriteLine($"找到数据库:{dbName}");
                            return true;
                        }
                    }
            }
            catch (Exception ex) {
                Console.WriteLine($"检查数据库:{dbName}...发生错误："+ex.Message.ToString());
            }
            Console.WriteLine($"未找到数据库:{dbName}");
            return false;
        }


        /// <summary>
        /// 这段代码主要针对macos下面链接mysql如果没有这个数据库 然后链接回报错的问题  实际就是去掉Database={mysqlDatabaseName};  这个字符串    win下面没有这个问题不需要去掉也可以
        /// </summary>
        /// <param name="mysqlConnectString"></param>
        /// <returns></returns>
        public static string remoteMysqlDababaseStr(string mysqlConnectString)
        {
            if (mysqlConnectString.IndexOf("Database") != -1)
            {
                string[] sArray = mysqlConnectString.Split("Database");
                string[] sArray2 = sArray[1].Split(";");
                return mysqlConnectString.Replace("Database" + sArray2[0] + ";", "");
            }
            else
            {
                return mysqlConnectString;
            }
        }



        /// <summary>
        /// 如果检测数据库不存在则创建一个数据库
        /// </summary>
        /// <param name="dbName"></param>
        /// <param name="mysqlConnectString"></param>
        /// <returns></returns>
        public static bool CheckOrCreateDatabase(string dbName, string mysqlConnectString)
        {
            string mysqlstr = remoteMysqlDababaseStr(mysqlConnectString);

            if (!DBExists(dbName, mysqlstr))
            {
                Console.WriteLine($"创建数据库:{dbName}");
                try
                {

                    ExecuteSqlRaw($"create database {dbName} default character set utf8mb4 collate utf8mb4_unicode_ci;", mysqlstr);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"创建数据库:{dbName}发生错误：" + ex.Message.ToString());
                }

                // var reader = GetReader($"create database {dbName} default character set utf8mb4 collate utf8mb4_unicode_ci;", mysqlstr);

                return true;
            }
            return false;
        }

        public static bool canConnect(string mysqlConnectString)
        {
            //Console.WriteLine("mysqlConnectString:"+mysqlConnectString);
            string mysqlstr = remoteMysqlDababaseStr(mysqlConnectString);
            //Console.WriteLine($"链接数据库：" + mysqlstr);
            MySqlConnection conn = new MySqlConnection(mysqlstr);
            try {
                conn.Open();

            } catch (Exception ex)
            {
                Console.WriteLine($"链接数据库发生错误：" + ex.Message.ToString());
                return false;
            }
            return true;
        }

        public static int ExecuteSqlRaw(string sql, string mysqlConnectString)
        {
            MySqlConnection conn = new MySqlConnection(mysqlConnectString);

            conn.Open();
            var cmd = conn.CreateCommand();
            cmd.CommandText = sql;
            return cmd.ExecuteNonQuery();

        }

        public static IDataReader GetReader(string sql, string mysqlConnectString)
        {
            try
            {
                MySqlConnection conn = new MySqlConnection(mysqlConnectString);
                conn.Open();
                var cmd = conn.CreateCommand();
                cmd.CommandText = sql;
                return cmd.ExecuteReader();
            }
            catch (Exception ex)
            {

                Console.WriteLine("mysql 执行命令失败错误代码：" + ex.ToString());
                Console.WriteLine("mysql 执行命令失败,mysqlconnectString：" + mysqlConnectString+"       sql:"+sql);
                return null;
            }
            
        }
    }
}
