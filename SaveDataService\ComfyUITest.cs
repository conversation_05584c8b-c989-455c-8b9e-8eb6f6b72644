using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using SaveDataService.Manage;
using Newtonsoft.Json;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI系统测试类
    /// </summary>
    public class ComfyUITest
    {
        /// <summary>
        /// 运行测试
        /// </summary>
        public static async Task RunTests()
        {
            Console.WriteLine("=== ComfyUI管理系统测试开始 ===");

            try
            {
                // 1. 测试添加真实的ComfyUI服务器
                Console.WriteLine("\n1. 测试添加真实的ComfyUI服务器...");
                var serverId1 = ComfyUIManage.Instance.AddServer(
                    "本地ComfyUI服务器",
                    "127.0.0.1",
                    8888,
                    3,
                    "本地测试ComfyUI服务器"
                );

                var serverId2 = ComfyUIManage.Instance.AddServer(
                    "备用ComfyUI服务器",
                    "127.0.0.1",
                    8189,
                    2,
                    "备用ComfyUI服务器"
                );

                Console.WriteLine($"添加服务器1: {serverId1}");
                Console.WriteLine($"添加服务器2: {serverId2}");

                // 2. 测试服务器连接
                Console.WriteLine("\n2. 测试服务器连接...");
                var onlineCount = await ComfyUIManage.Instance.TestAllServerConnections();
                Console.WriteLine($"在线服务器数量: {onlineCount}");

                // 3. 测试添加真实的工作流
                Console.WriteLine("\n3. 测试添加真实的工作流...");
                var realWorkflow = LoadRealWorkflow();
                var workflowId = ComfyUIManage.Instance.AddWorkflow(
                    "CafeLabs测试工作流",
                    realWorkflow,
                    "text2img",
                    "从CafeLabs测试文件加载的真实工作流",
                    "系统管理员"
                );
                Console.WriteLine($"添加工作流: {workflowId}");

                // 4. 测试创建任务
                Console.WriteLine("\n4. 测试创建任务...");
                var taskId1 = ComfyUIManage.Instance.CreateTask(
                    workflowId,
                    "生成风景图片",
                    "用户1",
                    5
                );
                
                var taskId2 = ComfyUIManage.Instance.CreateTask(
                    workflowId,
                    "生成人物图片",
                    "用户2",
                    3
                );

                Console.WriteLine($"创建任务1: {taskId1}");
                Console.WriteLine($"创建任务2: {taskId2}");

                // 5. 测试向真实服务器提交工作流
                Console.WriteLine("\n5. 测试向真实服务器提交工作流...");
                if (onlineCount > 0)
                {
                    var onlineServers = ComfyUIManage.Instance.GetOnlineServers();
                    if (onlineServers.Count > 0)
                    {
                        var result = await ComfyUIManage.Instance.SubmitWorkflowToServer(onlineServers[0].id, realWorkflow);
                        Console.WriteLine($"提交结果: {result}");
                    }
                }
                else
                {
                    Console.WriteLine("没有在线的服务器，跳过工作流提交测试");
                }

                // 6. 测试任务执行模拟
                Console.WriteLine("\n6. 测试任务执行模拟...");
                await SimulateRealTaskExecution(taskId1, workflowId);

                // 7. 测试获取各种信息
                Console.WriteLine("\n7. 测试获取信息...");
                
                // 获取服务器列表
                var servers = ComfyUIManage.Instance.GetAllServers();
                Console.WriteLine($"服务器总数: {servers.Count}");

                // 获取工作流列表
                var workflows = ComfyUIManage.Instance.GetAllWorkflows();
                Console.WriteLine($"工作流总数: {workflows.Count}");

                // 获取任务列表
                var tasks = ComfyUIManage.Instance.GetTasks();
                Console.WriteLine($"任务总数: {tasks.Count}");

                // 获取队列任务
                var queueTasks = ComfyUIManage.Instance.GetQueueTasks();
                Console.WriteLine($"队列任务数: {queueTasks.Count}");

                // 获取运行中任务
                var runningTasks = ComfyUIManage.Instance.GetRunningTasks();
                Console.WriteLine($"运行中任务数: {runningTasks.Count}");

                // 7. 测试获取统计信息
                Console.WriteLine("\n7. 测试获取统计信息...");
                var statistics = ComfyUIManage.Instance.GetServerStatistics();
                Console.WriteLine($"统计信息: {JsonConvert.SerializeObject(statistics, Formatting.Indented)}");

                // 8. 测试获取任务执行报告
                Console.WriteLine("\n8. 测试获取任务执行报告...");
                var report = ComfyUIData.Instance.GetTaskExecutionReport(taskId1);
                if (report != null)
                {
                    Console.WriteLine($"任务报告 - 任务名: {report.TaskName}, 状态: {report.Status}, 进度: {report.Progress}%");
                    Console.WriteLine($"执行时长: {report.Duration}, 日志条数: {report.Logs.Count}");
                }

                Console.WriteLine("\n=== ComfyUI管理系统测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 加载真实的工作流JSON
        /// </summary>
        /// <returns>工作流JSON字符串</returns>
        private static string LoadRealWorkflow()
        {
            try
            {
                var workflowPath = Path.Combine("Res", "baseExcel", "comfyui", "comfyui-cafelabs-test.json");
                if (File.Exists(workflowPath))
                {
                    var workflowJson = File.ReadAllText(workflowPath);
                    Console.WriteLine($"成功加载工作流文件: {workflowPath}");
                    return workflowJson;
                }
                else
                {
                    Console.WriteLine($"工作流文件不存在: {workflowPath}，使用默认工作流");
                    return CreateSampleWorkflow();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载工作流文件失败: {ex.Message}，使用默认工作流");
                return CreateSampleWorkflow();
            }
        }

        /// <summary>
        /// 模拟真实任务执行
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="workflowId">工作流ID</param>
        private static async Task SimulateRealTaskExecution(string taskId, string workflowId)
        {
            try
            {
                // 创建任务执行上下文
                var context = ComfyUIData.Instance.CreateTaskContext(taskId, workflowId);
                if (context == null)
                {
                    Console.WriteLine("创建任务上下文失败");
                    return;
                }

                Console.WriteLine($"开始执行任务: {taskId}");

                // 解析真实的工作流节点
                var workflowInfo = ComfyUIData.Instance.ParseWorkflow(context.Workflow.workflowJson ?? "{}");

                Console.WriteLine($"工作流包含 {workflowInfo.Nodes.Count} 个节点");

                // 模拟执行各个节点
                for (int i = 0; i < workflowInfo.Nodes.Count; i++)
                {
                    var node = workflowInfo.Nodes[i];

                    // 更新进度
                    var progress = (int)((double)(i + 1) / workflowInfo.Nodes.Count * 100);
                    ComfyUIData.Instance.UpdateTaskProgress(
                        context,
                        node.NodeId,
                        node.NodeTitle,
                        JsonConvert.SerializeObject(node.Inputs),
                        "",
                        progress
                    );

                    // 模拟处理时间
                    await Task.Delay(1500);

                    // 完成步骤
                    var output = $"节点 {node.NodeTitle} 执行完成";
                    ComfyUIData.Instance.CompleteTaskStep(context, node.NodeId, output, true);

                    Console.WriteLine($"完成节点: {node.NodeTitle} ({progress}%) - {node.NodeType}");
                }

                // 模拟保存输出文件
                var outputData = System.Text.Encoding.UTF8.GetBytes("模拟的图片数据 - 来自真实工作流");
                var outputPath = ComfyUIData.Instance.SaveTaskOutputFile(
                    taskId,
                    "generated_image_real.png",
                    outputData,
                    1,
                    "从真实工作流生成的图片"
                );

                // 完成任务
                ComfyUIData.Instance.CompleteTask(context, true);
                Console.WriteLine($"任务执行完成: {taskId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模拟任务执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟任务执行
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="workflowId">工作流ID</param>
        private static async Task SimulateTaskExecution(string taskId, string workflowId)
        {
            try
            {
                // 创建任务执行上下文
                var context = ComfyUIData.Instance.CreateTaskContext(taskId, workflowId);
                if (context == null)
                {
                    Console.WriteLine("创建任务上下文失败");
                    return;
                }

                Console.WriteLine($"开始执行任务: {taskId}");

                // 模拟执行各个节点
                var nodes = new[]
                {
                    new { Id = "1", Name = "加载检查点", Input = "model.safetensors", Output = "模型已加载" },
                    new { Id = "2", Name = "CLIP文本编码", Input = "a beautiful landscape", Output = "文本编码完成" },
                    new { Id = "3", Name = "KSampler", Input = "采样参数", Output = "采样完成" },
                    new { Id = "4", Name = "VAE解码", Input = "潜在空间", Output = "图像解码完成" },
                    new { Id = "5", Name = "保存图像", Input = "图像数据", Output = "图像已保存" }
                };

                for (int i = 0; i < nodes.Length; i++)
                {
                    var node = nodes[i];
                    
                    // 更新进度
                    ComfyUIData.Instance.UpdateTaskProgress(
                        context, 
                        node.Id, 
                        node.Name, 
                        node.Input, 
                        "", 
                        (i + 1) * 20
                    );

                    // 模拟处理时间
                    await Task.Delay(1000);

                    // 完成步骤
                    ComfyUIData.Instance.CompleteTaskStep(context, node.Id, node.Output, true);

                    Console.WriteLine($"完成节点: {node.Name} ({(i + 1) * 20}%)");
                }

                // 模拟保存输出文件
                var outputData = System.Text.Encoding.UTF8.GetBytes("模拟的图片数据");
                var outputPath = ComfyUIData.Instance.SaveTaskOutputFile(
                    taskId, 
                    "generated_image.png", 
                    outputData, 
                    1, 
                    "生成的风景图片"
                );

                // 完成任务
                ComfyUIData.Instance.CompleteTask(context, true);
                Console.WriteLine($"任务执行完成: {taskId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模拟任务执行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建示例工作流JSON
        /// </summary>
        /// <returns>工作流JSON字符串</returns>
        private static string CreateSampleWorkflow()
        {
            var workflow = new Dictionary<string, object>
            {
                ["1"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CheckpointLoaderSimple",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["ckpt_name"] = "model.safetensors"
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "加载检查点"
                    }
                },
                ["2"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["text"] = "a beautiful landscape, high quality, detailed",
                        ["clip"] = new object[] { "1", 1 }
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "CLIP文本编码(正面)"
                    }
                },
                ["3"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["text"] = "blurry, low quality, bad",
                        ["clip"] = new object[] { "1", 1 }
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "CLIP文本编码(负面)"
                    }
                },
                ["4"] = new Dictionary<string, object>
                {
                    ["class_type"] = "KSampler",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["seed"] = 42,
                        ["steps"] = 20,
                        ["cfg"] = 7.0,
                        ["sampler_name"] = "euler",
                        ["scheduler"] = "normal",
                        ["denoise"] = 1.0,
                        ["model"] = new object[] { "1", 0 },
                        ["positive"] = new object[] { "2", 0 },
                        ["negative"] = new object[] { "3", 0 },
                        ["latent_image"] = new object[] { "5", 0 }
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "KSampler"
                    }
                },
                ["5"] = new Dictionary<string, object>
                {
                    ["class_type"] = "EmptyLatentImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["width"] = 512,
                        ["height"] = 512,
                        ["batch_size"] = 1
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "空潜在图像"
                    }
                },
                ["6"] = new Dictionary<string, object>
                {
                    ["class_type"] = "VAEDecode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["samples"] = new object[] { "4", 0 },
                        ["vae"] = new object[] { "1", 2 }
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "VAE解码"
                    }
                },
                ["7"] = new Dictionary<string, object>
                {
                    ["class_type"] = "SaveImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["filename_prefix"] = "ComfyUI",
                        ["images"] = new object[] { "6", 0 }
                    },
                    ["_meta"] = new Dictionary<string, object>
                    {
                        ["title"] = "保存图像"
                    }
                }
            };

            return JsonConvert.SerializeObject(workflow, Formatting.Indented);
        }
    }
}
