﻿
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace GameServer.GameService.Tools
{
    class BackupsTools
    {
        private static int listLen = 1000;
        public static async void copyMySql()
        {
            await copyMySqlData("定时备份");
        }
        public static string backupFileNameSet = "MysqlBackUp_{ip}_{pn}_{vn}_{t}";
        /// <summary>
        /// 备份数据库数据
        /// </summary>
        public static Task copyMySqlData(string tipLog)
        {
            return Task.CompletedTask;
            //    string dicName = backupFileNameSet;
            //    ExcelDatabase db = new ExcelDatabase();
            //    var properties = db.GetType().GetProperties();
            //    dicName = dicName.ToLower();
            //    dicName = dicName.Replace("{ip}", AppConfig.Instance.mysqlServer).Replace("{pn}", ExcelDll.Tools.Setting.projectName).Replace("{vn}", ExcelDll.Tools.Setting.version).Replace("{t}", System.DateTime.Now.ToString("yyyyMMddHHmm"));
            //    dicName = dicName.Replace(".", "_").Replace("\\", "_").Replace("/", "_").Replace(":", "_").Replace(" ", "_");
            //    //创建文件夹
            //    if (!Directory.Exists(AppConfig.Instance.TableBackUpPath))
            //    {
            //        Directory.CreateDirectory(AppConfig.Instance.TableBackUpPath);
            //    }
            //    if (!Directory.Exists(AppConfig.Instance.TableBackUpPath + "BackZips/"))
            //    {
            //        Directory.CreateDirectory(AppConfig.Instance.TableBackUpPath + "BackZips/");
            //    }
            //    if (!Directory.Exists(AppConfig.Instance.TableBackUpPath + dicName + "/"))
            //    {
            //        Directory.CreateDirectory(AppConfig.Instance.TableBackUpPath + dicName + "/");
            //    }
            //if (!Directory.Exists(AppConfig.Instance.UpBackUpPath))
            //{
            //    Directory.CreateDirectory(AppConfig.Instance.UpBackUpPath);
            //}
            ////通过反射获取所有表
            //var fileList = Directory.GetFiles(AppConfig.Instance.UpBackUpPath, "*.json");
            //for (int i = 0; i < fileList.Length; i++)
            //{
            //    var fileName = fileList[i];
            //    File.Delete(fileName);
            //}
            //List<Task> AllTaskList = new List<Task>();
            ////遍历并备份
            //foreach (var item in properties)
            //{
            //    string itemName = item.Name;
            //    var dataList = item;
            //    //if (dataList.Name != "MapDatas")
            //    //{
            //    //    continue;
            //    //}
            //    //AllTaskList.Add(new Task(() =>
            //    //{
            //    if (dataList.PropertyType.Name == "DbSet`1")
            //    {

            //        dynamic getTable = dataList.GetValue(db);
            //        dynamic aa = Enumerable.ToList(getTable);
            //        int listCount = (int)Math.Ceiling((double)aa.Count / listLen);
            //        int lastListLen = aa.Count - (listCount - 1) * listLen;
            //        string totleText = "[";
            //        List<Task> taskList = new List<Task>();
            //        List<string> textList = new List<string>();
            //        int count = 0;
            //        for (int i = 0; i < listCount; i++)
            //        {
            //            int listIndex = i;
            //            Task t = new Task(() =>
            //            {
            //                var text = "";
            //                int len = listIndex < listCount - 1 ? listLen : lastListLen;
            //                for (int j = 0; j < len; j++)
            //                {
            //                    int index = listIndex * listLen + j;
            //                    text += aa[index].getMySqlString();

            //                    if (index < aa.Count - 1)
            //                    {
            //                        text += ",";
            //                    }
            //                }
            //                count++;
            //                textList.Add(text);

            //            });
            //            taskList.Add(t);

            //        }
            //        var taskArray = taskList.ToArray();
            //        List<Task> taskList2 = new List<Task>();
            //        if (taskArray.Length > 0)
            //        {
            //            for (int i = 0; i < taskArray.Length - 1; i++)
            //            {
            //                var taskItem = taskArray[i];
            //                taskList2.Add(taskItem);
            //            }
            //            int inde = 0;
            //            foreach (var taskItem in taskList2)
            //            {
            //                inde++;
            //                taskItem.Start();
            //            }
            //            await Task.WhenAll(taskList2);
            //            var a = taskArray[taskArray.Length - 1];
            //            a.Start();
            //            await Task.WhenAll(a);
            //            string addText = string.Join("", textList);
            //            totleText += addText;
            //        }
            //        //for (int i = 0;i < aa.Count; i++)
            //        //{
            //        //    text+=aa[i].getMySqlString();
            //        //    if (i < aa.Count - 1)
            //        //    {
            //        //        text += ",";
            //        //    }
            //        //}
            //        totleText += "]";
            //        try
            //        {
            //            File.WriteAllText(AppConfig.Instance.TableBackUpPath + dicName + "/" + itemName + ".json", totleText);
            //            //File.WriteAllText(AppConfig.Instance.UpBackUpPath + itemName + ".json", totleText);

            //        }
            //        catch (Exception)
            //        {

            //            Console.WriteLine("不存在数据表：" + itemName);
            //        }
            //    }
            //    //}));


            //    //));
            //}
            //var allList = AllTaskList.ToArray();
            //foreach (var item in allList)
            //{
            //    item.Start();
            //}
            //Task.WaitAll();
            ////压缩成压缩包
            //ZipTools.CreateZipFile(AppConfig.Instance.TableBackUpPath + dicName, AppConfig.Instance.TableBackUpPath + "BackZips/" + dicName + ".zip");
            //Console.WriteLine(tipLog + "[特殊log 数据库导出已完成]");
        }
        /// <summary>
        /// 恢复所有数据
        /// </summary>
        public static Task uploadAllData(string redisName)
        {
            return Task.CompletedTask;
            //    Interlocked.Exchange(ref ServerTypeManager. canRun, 1);
            //    string upPath = AppConfig.Instance.TableBackUpPath + redisName + "/";
            //    if (!Directory.Exists(upPath))
            //    {
            //        Console.WriteLine("指定的mysql备份文件不存在：" + upPath);
            //        return;
            //    }
            //    var fileList = Directory.GetFiles(upPath, "*.json");
            //    for (int i = 0; i < fileList.Length; i++)
            //    {
            //        var fileName = fileList[i];
            //        var classfileName = fileName.Replace(upPath, "").Replace("s.json", "");
            //        uploadMySqlData(fileName, classfileName);
            //    }
            //    Console.WriteLine("上传mysql完成");
            //    Interlocked.Exchange(ref ServerTypeManager.canRun, 0);
            //}
            ///// <summary>
            ///// 恢复表
            ///// </summary>
            ///// <param name="className"></param>
            //private static void uploadMySqlData(string fileName, string className)
            //{
            //    //if (!Directory.Exists(AppConfig.Instance.UpBackUpPath))
            //    //{
            //    //    Directory.CreateDirectory(AppConfig.Instance.UpBackUpPath);
            //    //}
            //    try
            //    {


            //        var jsonText = File.ReadAllText(fileName);
            //        ExcelDatabase db = new ExcelDatabase();
            //        var propertie = db.GetType().GetProperty(className + "s");
            //        dynamic getTable = propertie.GetValue(db, null);
            //        var jsonData = JsonConvert.DeserializeObject<dynamic[]>(jsonText);
            //        //遍历获取的所有数据
            //        for (int i = 0; i < jsonData.Length; i++)
            //        {
            //            var nnn = jsonData[i];
            //            //new 一个数据对象
            //            dynamic ect = Assembly.Load("ExcelDll").CreateInstance(className);
            //            //复制获取的数据到new出来的数据对象
            //            CopyTo(nnn, ref ect);
            //            //获取这条数据的ID相同的数据，如果表里已经有这个ID的数据，那么先删除原数据
            //            var takeData = getId(ect, getTable);
            //            //添加到数据库
            //            if (takeData == null)
            //            {
            //                getTable.Add(ect);
            //            }
            //            else
            //            {
            //                getTable.Remove(takeData);
            //                getTable.Add(ect);
            //            }
            //        }
            //        db.SaveChanges();
            //        Console.WriteLine("============================上传完成  " + className + "s");
            //    }
            //    catch (Exception e)
            //    {
            //        Console.WriteLine(e.ToString());
            //        ;
            //    }
        }
        public static bool ContainsProperty(dynamic obj, string propertyName)
        {
            Type type = obj.GetType();
            PropertyInfo property = type.GetProperty(propertyName);
            return property != null;
        }
        //获取表里这个ID的数据
        private static dynamic getId(dynamic etc, dynamic getTable)
        {
            if (etc==null||etc.id == null)
            {
                return null;
            }
            try
            {
                foreach (var item in getTable)
                {
                        bool containsProperty = ContainsProperty(etc, "idString") && ContainsProperty(item, "idString");
                    if (containsProperty)
                    {
                        if (etc.idString == item.idString)
                        {
                            return item;
                        }
                    }
                    else
                    {
                        if (JsonConvert.SerializeObject(item.id) == JsonConvert.SerializeObject(etc.id))
                        {
                            return item;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                ;
            }

            return null;
        }
        /// <summary>
        /// 复制数据到创建出来的数据对象
        /// </summary>
        /// <param name="source"></param>
        /// <param name="target"></param>
        private static void CopyTo(object source, ref object target)
        {
            if (source == null)
                return;
            if (target == null)
            {
                return;
            }
            var targetType = target.GetType();
            var targetTypeProList = targetType.GetProperties();
            foreach (var property in targetTypeProList)
            {
                //这里非常沙雕地 要先转化为JSON再转换回来才能正常使用
                if (targetType.Name == property.PropertyType.Name)
                {
                    continue;
                }
                dynamic propertyValue = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(((Newtonsoft.Json.Linq.JObject)source)[property.Name]));
                //var propertyValue = property.GetValue(source);
                //除了tableID以外所有的数据都通过反射填上去
                if (propertyValue != null && property.Name != "tableID")
                {
                    var tags = property.GetCustomAttributes(typeof(NotMappedAttribute), false);
                    string desc = "";
                    if (tags.Length > 0)
                    {
                        continue;
                    }
                    if (propertyValue.GetType().IsClass)
                    {
                    }
                    if (property.PropertyType.Name.IndexOf("`1") == -1)
                    {
                        switch (property.PropertyType.Name)
                        {
                            case "Byte[]":
                                var st = "\""+propertyValue.ToString()+"\"";
                                try
                                {
                                var by = JsonConvert.DeserializeObject<byte[]>(st);

                                property.SetValue(target,by);
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e.ToString());
                                    ;
                                }
                                break;
                            case "Byte":
                                property.SetValue(target, (byte)propertyValue);
                                break;
                            case "Single":
                                property.SetValue(target, (float)propertyValue);
                                break;
                            case "UInt16":
                                property.SetValue(target, (ushort)propertyValue);
                                break;
                            case "Int16":
                                property.SetValue(target, (short)propertyValue);
                                break;
                            case "UInt32":
                                property.SetValue(target, (uint)propertyValue);
                                break;
                            case "Int32":
                                property.SetValue(target, (int)propertyValue);
                                break;
                            case "UInt64":
                                property.SetValue(target, (ulong)propertyValue);
                                break;
                            case "Int64":
                                property.SetValue(target, (long)propertyValue);
                                break;
                            default:
                                property.SetValue(target, propertyValue);
                                break;
                        }
                    }
                }
            }
        }
    }
}
