﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Globalization;

/// <summary>
/// 日期格式化枚举
/// </summary>
public enum DateFormat
{
    ISO8601,            // yyyy-MM-ddTHH:mm:ss.fffZ
    ISO8601DateOnly,    // yyyy-MM-dd
    UnixTimeSeconds,     // 秒数（10位）
    UnixTimeMilliseconds,// 毫秒数（13位）
    DotNetTicks,        // .NET ticks (100纳秒间隔)
    WindowsFileTime,    // Windows文件时间（1601年开始的100纳秒间隔）
    ExcelSerialDate,    // Excel序列日期（1900或1904基准）
    Custom,            // 自定义格式
    RFC1123,           // RFC1123 (ddd, dd MMM yyyy HH:mm:ss GMT)
    RFC3339,           // RFC3339 (类似ISO8601但更严格)
    Sortable,          // 可排序格式 (yyyy-MM-dd HH:mm:ss)
    UniversalSortable, // 通用可排序格式 (yyyy-MM-dd HH:mm:ssZ)
    JulianDate        // 儒略日期
}

/// <summary>
/// 二进制类型辅助类，提供类型与二进制数据的相互转换功能
/// </summary>
public static class BinaryTypeHelper
{
    private static Encoding utf8Encoding = new UTF8Encoding(false);
    private static DateFormat defaultDateFormat = DateFormat.ISO8601;
    private static string customDateFormat = "yyyy-MM-dd HH:mm:ss";

    #region 写入方法

    /// <summary>
    /// 将指定类型的值写入二进制写入器
    /// </summary>
    /// <param name="type">值的类型</param>
    /// <param name="value">要写入的值</param>
    /// <param name="file">二进制写入器</param>
    public static void TypeToValue(string type, string value, BinaryWriter file)
    {
        if (type.EndsWith("[]"))
        {
            WriteArray(type, value, file);
        }
        else
        {
            WriteSingleValue(type, value, file);
        }
    }

    /// <summary>
    /// 将数组写入二进制写入器
    /// </summary>
    /// <param name="type">数组类型</param>
    /// <param name="value">数组值的字符串表示</param>
    /// <param name="file">二进制写入器</param>
    private static void WriteArray(string type, string value, BinaryWriter file)
    {
        string elementType = type.Substring(0, type.Length - 2);
        string[] elements = value.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

        // 写入数组长度
        file.Write((ushort)elements.Length);

        // 写入每个元素
        foreach (var element in elements)
        {
            WriteSingleValue(elementType, element.Trim(), file);
        }
    }

    /// <summary>
    /// 将单个值写入二进制写入器
    /// </summary>
    /// <param name="type">值的类型</param>
    /// <param name="value">值的字符串表示</param>
    /// <param name="file">二进制写入器</param>
    private static void WriteSingleValue(string type, string value, BinaryWriter file)
    {
        switch (type.ToLower())
        {
            case "string":
                if (value == null) value = "";
                byte[] bytes = GetUTFBytes(value);
                if (bytes.Length > ushort.MaxValue)
                    throw new Exception($"字符串长度超出限制 {ushort.MaxValue}");
                file.Write((ushort)bytes.Length);
                file.Write(bytes);
                break;

            case "int":
            case "int32":
                file.Write(int.Parse(value));
                break;

            case "uint":
            case "uint32":
                file.Write(uint.Parse(value));
                break;

            case "short":
            case "int16":
                file.Write(short.Parse(value));
                break;

            case "ushort":
            case "uint16":
                file.Write(ushort.Parse(value));
                break;

            case "long":
            case "int64":
                file.Write(long.Parse(value));
                break;

            case "ulong":
            case "uint64":
                file.Write(ulong.Parse(value));
                break;

            case "byte":
                file.Write(byte.Parse(value));
                break;

            case "sbyte":
                file.Write(sbyte.Parse(value));
                break;

            case "float":
                file.Write(float.Parse(value));
                break;

            case "double":
                file.Write(double.Parse(value));
                break;

            case "decimal":
                file.Write(decimal.Parse(value));
                break;

            case "bool":
            case "boolean":
                file.Write(bool.Parse(value));
                break;

            case "char":
                if (value.Length != 1)
                    throw new Exception("Char类型只能包含一个字符");
                file.Write(char.Parse(value));
                break;

            case "datetime":
                WriteDateTime(value, file);
                break;

            case "timespan":
                file.Write(TimeSpan.Parse(value).Ticks);
                break;

            case "guid":
                file.Write(Guid.Parse(value).ToByteArray());
                break;

            default:
                throw new NotSupportedException($"不支持的类型: {type}");
        }
    }

    

    #endregion

    #region 读取方法

    /// <summary>
    /// 从二进制读取器读取指定类型的值
    /// </summary>
    /// <param name="type">要读取的类型</param>
    /// <param name="file">二进制读取器</param>
    /// <returns>读取的值</returns>
    public static object ReadValue(string type, BinaryReader file)
    {
        if (type.EndsWith("[]"))
        {
            return ReadArray(type, file);
        }

        return ReadSingleValue(type, file);
    }

    /// <summary>
    /// 从二进制读取器读取数组
    /// </summary>
    /// <param name="type">数组类型</param>
    /// <param name="file">二进制读取器</param>
    /// <returns>读取的数组</returns>
    private static Array ReadArray(string type, BinaryReader file)
    {
        string elementType = type.Substring(0, type.Length - 2);
        int length = file.ReadUInt16();

        switch (elementType.ToLower())
        {
            case "int":
            case "int32":
                int[] intArray = new int[length];
                for (int i = 0; i < length; i++) intArray[i] = file.ReadInt32();
                return intArray;

            case "uint":
            case "uint32":
                uint[] uintArray = new uint[length];
                for (int i = 0; i < length; i++) uintArray[i] = file.ReadUInt32();
                return uintArray;

            case "short":
            case "int16":
                short[] shortArray = new short[length];
                for (int i = 0; i < length; i++) shortArray[i] = file.ReadInt16();
                return shortArray;

            case "ushort":
            case "uint16":
                ushort[] ushortArray = new ushort[length];
                for (int i = 0; i < length; i++) ushortArray[i] = file.ReadUInt16();
                return ushortArray;

            case "long":
            case "int64":
                long[] longArray = new long[length];
                for (int i = 0; i < length; i++) longArray[i] = file.ReadInt64();
                return longArray;

            case "ulong":
            case "uint64":
                ulong[] ulongArray = new ulong[length];
                for (int i = 0; i < length; i++) ulongArray[i] = file.ReadUInt64();
                return ulongArray;

            case "byte":
                return file.ReadBytes(length);

            case "sbyte":
                sbyte[] sbyteArray = new sbyte[length];
                for (int i = 0; i < length; i++) sbyteArray[i] = file.ReadSByte();
                return sbyteArray;

            case "float":
                float[] floatArray = new float[length];
                for (int i = 0; i < length; i++) floatArray[i] = file.ReadSingle();
                return floatArray;

            case "double":
                double[] doubleArray = new double[length];
                for (int i = 0; i < length; i++) doubleArray[i] = file.ReadDouble();
                return doubleArray;

            case "decimal":
                decimal[] decimalArray = new decimal[length];
                for (int i = 0; i < length; i++) decimalArray[i] = file.ReadDecimal();
                return decimalArray;

            case "bool":
            case "boolean":
                bool[] boolArray = new bool[length];
                for (int i = 0; i < length; i++) boolArray[i] = file.ReadBoolean();
                return boolArray;

            case "char":
                char[] charArray = new char[length];
                for (int i = 0; i < length; i++) charArray[i] = file.ReadChar();
                return charArray;

            case "string":
                string[] stringArray = new string[length];
                for (int i = 0; i < length; i++)
                    stringArray[i] = ReadString(file);
                return stringArray;

            case "datetime":
                DateTime[] dateTimeArray = new DateTime[length];
                for (int i = 0; i < length; i++)
                    dateTimeArray[i] = ReadDateTime(file);
                return dateTimeArray;

            case "timespan":
                TimeSpan[] timeSpanArray = new TimeSpan[length];
                for (int i = 0; i < length; i++)
                    timeSpanArray[i] = new TimeSpan(file.ReadInt64());
                return timeSpanArray;

            case "guid":
                byte[] guidBytes = file.ReadBytes(16 * length);
                Guid[] guidArray = new Guid[length];
                for (int i = 0; i < length; i++)
                {
                    byte[] temp = new byte[16];
                    Array.Copy(guidBytes, i * 16, temp, 0, 16);
                    guidArray[i] = new Guid(temp);
                }
                return guidArray;

            default:
                throw new NotSupportedException($"不支持的类型: {elementType}");
        }
    }

    /// <summary>
    /// 从二进制读取器读取单个值
    /// </summary>
    /// <param name="type">值的类型</param>
    /// <param name="file">二进制读取器</param>
    /// <returns>读取的值</returns>
    private static object ReadSingleValue(string type, BinaryReader file)
    {
        switch (type.ToLower())
        {
            case "string":
                return ReadString(file);

            case "int":
            case "int32":
                return file.ReadInt32();

            case "uint":
            case "uint32":
                return file.ReadUInt32();

            case "short":
            case "int16":
                return file.ReadInt16();

            case "ushort":
            case "uint16":
                return file.ReadUInt16();

            case "long":
            case "int64":
                return file.ReadInt64();

            case "ulong":
            case "uint64":
                return file.ReadUInt64();

            case "byte":
                return file.ReadByte();

            case "sbyte":
                return file.ReadSByte();

            case "float":
                return file.ReadSingle();

            case "double":
                return file.ReadDouble();

            case "decimal":
                return file.ReadDecimal();

            case "bool":
            case "boolean":
                return file.ReadBoolean();

            case "char":
                return file.ReadChar();

            case "datetime":
                return ReadDateTime(file);

            case "timespan":
                return new TimeSpan(file.ReadInt64());

            case "guid":
                return new Guid(file.ReadBytes(16));

            default:
                throw new NotSupportedException($"不支持的类型: {type}");
        }
    }

    /// <summary>
    /// 从二进制读取器读取字符串
    /// </summary>
    /// <param name="file">二进制读取器</param>
    /// <returns>读取的字符串</returns>
    private static string ReadString(BinaryReader file)
    {
        ushort length = file.ReadUInt16();
        byte[] bytes = file.ReadBytes(length);
        return utf8Encoding.GetString(bytes);
    }




    #endregion

    #region 辅助方法
    /// <summary>
    /// 从二进制读取器读取DateTime，根据设置的日期格式
    /// </summary>
    /// <param name="file">二进制读取器</param>
    public static DateTime ReadDateTime(BinaryReader reader, string customFormat = null)
    {
        // 读取格式枚举
        DateFormat format = (DateFormat)reader.ReadInt32();

        try
        {
            switch (format)
            {
                case DateFormat.ISO8601:
                    return DateTime.ParseExact(
                        reader.ReadString(), "o", CultureInfo.InvariantCulture, DateTimeStyles.RoundtripKind);

                case DateFormat.ISO8601DateOnly:
                    return DateTime.ParseExact(
                        reader.ReadString(), "yyyy-MM-dd", CultureInfo.InvariantCulture);

                case DateFormat.UnixTimeSeconds:
                    return DateTime.UnixEpoch.AddSeconds(reader.ReadInt64());

                case DateFormat.UnixTimeMilliseconds:
                    return DateTime.UnixEpoch.AddMilliseconds(reader.ReadInt64());

                case DateFormat.DotNetTicks:
                    return new DateTime(reader.ReadInt64(), DateTimeKind.Utc);

                case DateFormat.WindowsFileTime:
                    return DateTime.FromFileTimeUtc(reader.ReadInt64());

                case DateFormat.ExcelSerialDate:
                    double excelDate = reader.ReadDouble();
                    return DateTime.FromOADate(excelDate);

                case DateFormat.RFC1123:
                    return DateTime.ParseExact(
                        reader.ReadString(), "r", CultureInfo.InvariantCulture);

                case DateFormat.RFC3339:
                    return DateTime.ParseExact(
                        reader.ReadString(), "yyyy-MM-dd'T'HH:mm:ss.fffK", CultureInfo.InvariantCulture);

                case DateFormat.Sortable:
                    return DateTime.ParseExact(
                        reader.ReadString(), "s", CultureInfo.InvariantCulture);

                case DateFormat.UniversalSortable:
                    return DateTime.ParseExact(
                        reader.ReadString(), "u", CultureInfo.InvariantCulture);

                case DateFormat.JulianDate:
                    int julian = reader.ReadInt32();
                    // 儒略日转换逻辑（简化版）
                    return new DateTime(1858, 11, 17).AddDays(julian - 2400000.5);

                case DateFormat.Custom:
                    return DateTime.ParseExact(
                        reader.ReadString(), customFormat ?? "G", CultureInfo.InvariantCulture);

                default:
                    throw new NotSupportedException($"Unsupported date format: {format}");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidDataException($"Failed to read datetime with format {format}", ex);
        }
    }

    public static void WriteDateTime(string value, BinaryWriter writer, string customFormat = null)
    {
        DateTime dt;
        DateFormat format = DetectDateFormat(value);

        // 先写入格式枚举
        writer.Write((int)format);

        try
        {
            switch (format)
            {
                case DateFormat.ISO8601:
                    dt = DateTime.Parse(value, null, DateTimeStyles.RoundtripKind);
                    writer.Write(dt.ToUniversalTime().ToString("o"));
                    break;

                case DateFormat.ISO8601DateOnly:
                    dt = DateTime.ParseExact(value, "yyyy-MM-dd", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString("yyyy-MM-dd"));
                    break;

                case DateFormat.UnixTimeSeconds:
                    writer.Write(long.Parse(value));
                    break;

                case DateFormat.UnixTimeMilliseconds:
                    writer.Write(long.Parse(value));
                    break;

                case DateFormat.DotNetTicks:
                    writer.Write(long.Parse(value));
                    break;

                case DateFormat.WindowsFileTime:
                    writer.Write(long.Parse(value));
                    break;

                case DateFormat.ExcelSerialDate:
                    writer.Write(double.Parse(value));
                    break;

                case DateFormat.RFC1123:
                    dt = DateTime.ParseExact(value, "r", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString("r"));
                    break;

                case DateFormat.RFC3339:
                    dt = DateTime.ParseExact(value, "yyyy-MM-dd'T'HH:mm:ss.fffK", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString("yyyy-MM-dd'T'HH:mm:ss.fffK"));
                    break;

                case DateFormat.Sortable:
                    dt = DateTime.ParseExact(value, "s", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString("s"));
                    break;

                case DateFormat.UniversalSortable:
                    dt = DateTime.ParseExact(value, "u", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString("u"));
                    break;

                case DateFormat.JulianDate:
                    writer.Write(int.Parse(value));
                    break;

                case DateFormat.Custom:
                    dt = DateTime.ParseExact(value, customFormat ?? "G", CultureInfo.InvariantCulture);
                    writer.Write(dt.ToString(customFormat ?? "G"));
                    break;

                default:
                    throw new NotSupportedException($"Unsupported date format: {format}");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidDataException($"Failed to write datetime value '{value}' as format {format}", ex);
        }
    }
    public static DateFormat DetectDateFormat(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("Value cannot be empty");

        // 检测ISO8601完整格式
        if (Regex.IsMatch(value, @"^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z?$"))
            return DateFormat.ISO8601;

        // 检测ISO8601仅日期
        if (Regex.IsMatch(value, @"^\d{4}-\d{2}-\d{2}$"))
            return DateFormat.ISO8601DateOnly;

        // 检测Unix时间戳（秒或毫秒）
        if (long.TryParse(value, out long timestamp))
        {
            if (value.Length <= 10) return DateFormat.UnixTimeSeconds;
            if (value.Length <= 13) return DateFormat.UnixTimeMilliseconds;
        }

        // 检测.NET Ticks
        if (long.TryParse(value, out long ticks) && ticks > 630822816000000000) // 2000年之后
            return DateFormat.DotNetTicks;

        // 检测Windows文件时间
        if (long.TryParse(value, out long fileTime) && fileTime > 116444736000000000)
            return DateFormat.WindowsFileTime;

        // 检测Excel序列日期
        if (double.TryParse(value, out double excelDate) && excelDate > 0)
            return DateFormat.ExcelSerialDate;

        // 检测RFC1123
        if (DateTime.TryParseExact(value, "r", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
            return DateFormat.RFC1123;

        // 检测RFC3339
        if (DateTime.TryParseExact(value, "yyyy-MM-dd'T'HH:mm:ss.fffK", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
            return DateFormat.RFC3339;

        // 检测可排序格式
        if (DateTime.TryParseExact(value, "s", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
            return DateFormat.Sortable;

        // 检测通用可排序格式
        if (DateTime.TryParseExact(value, "u", CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
            return DateFormat.UniversalSortable;

        // 检测儒略日期
        if (Regex.IsMatch(value, @"^\d{7}$") && int.TryParse(value, out int julian) && julian > 2400000)
            return DateFormat.JulianDate;

        return DateFormat.Custom;
    }
    /// <summary>
    /// 获取字符串的UTF8字节数组
    /// </summary>
    /// <param name="value">要转换的字符串</param>
    /// <returns>UTF8字节数组</returns>
    private static byte[] GetUTFBytes(string value)
    {
        return utf8Encoding.GetBytes(value);
    }

    /// <summary>
    /// 设置日期格式
    /// </summary>
    /// <param name="format">日期格式枚举</param>
    /// <param name="customFormat">自定义格式字符串</param>
    public static void SetDateFormat(DateFormat format, string customFormat = null)
    {
        defaultDateFormat = format;
        if (format == DateFormat.Custom && !string.IsNullOrEmpty(customFormat))
        {
            customDateFormat = customFormat;
        }
    }

    #endregion
}