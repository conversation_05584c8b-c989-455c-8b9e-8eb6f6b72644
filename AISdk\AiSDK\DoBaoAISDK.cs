﻿using Newtonsoft.Json;
using static AISDKWebSocket.HttpManager;

namespace AISdk.Manage
{
	internal class DoBaoAISDK : AISDKProvider
	{
		private string url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
		private string key = "Bearer 5adc7c08-ae37-437d-94fa-f8d6384f26ea";
		public string Model;
		public DoBaoAISDK(string userMode)
		{
			this.Model = userMode;
		}
		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "doubao-embedding-text-240715";
			}
			var data = new KuaiShuData(Model, "user", messages, true);
			//string jsonData = JsonConvert.SerializeObject(data);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				// UnityEngine.Debug.Log(backText);
				callback(backText, bol);
			});
		}
		//根据输入的图像内容、视频内容和自然语言指令完成任务
		public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
		{
			if (string.IsNullOrEmpty(Model))
			{
				Model = "doubao-embedding-text-240715";
			}
			string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
			callback(backText, false);
		}

        public void ChatWithAi(string prompt, Action<string, bool> callback)
        {
            List<Message> messages = new List<Message>();
            messages.Add(new Message { role = "user", content = prompt });
            if (string.IsNullOrEmpty(Model))
            {
                Model = "doubao-embedding-text-240715";
            }
            var data = new KuaiShuData(Model, "user", messages, true);
            Instance.HttpsWeb(url, key, data, (backText, bol) =>
            {
                callback(backText, bol);
            });
        }
        public async Task<AiChatBase> KernelFunAsync()
        {
            return null;
        }
    }
}
