﻿using System;
using System.Collections.Generic;
using System.Text;


    abstract public class  BaseServer
    {
        /// <summary>
        /// 依赖的service会自动初始化并且从appconfig中取所需的初始化参数 如果没有则会初始化失败
        /// 一切功能皆为service为了以后分布式做准备
        /// </summary>
        public List<BaseServer> libService = new List<BaseServer>();


        public string connect="";
        public bool active = false;

        public abstract void init();

        public abstract void ConnectService();
        public abstract void DisConnectService();
    }
