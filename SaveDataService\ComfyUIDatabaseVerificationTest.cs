using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SaveDataService.Manage;

/// <summary>
/// ComfyUI数据库验证测试类
/// 专门用于验证数据库中的数据存储和查询功能
/// </summary>
public static class ComfyUIDatabaseVerificationTest
{
    /// <summary>
    /// 执行完整的数据库验证测试
    /// </summary>
    public static async Task TestDatabaseVerification()
    {
        Console.WriteLine("=== 开始ComfyUI数据库验证测试 ===");
        Console.WriteLine("此测试将验证数据库中的实际数据存储和查询功能");
        Console.WriteLine();

        try
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            // 第一步：清理测试环境
            Console.WriteLine("1. 清理测试环境...");
            await CleanTestEnvironment();
            
            // 第二步：验证初始状态
            Console.WriteLine("2. 验证数据库初始状态...");
            await VerifyInitialState();
            
            // 第三步：创建测试数据
            Console.WriteLine("3. 创建测试数据...");
            var testData = await CreateTestData();
            
            // 第四步：验证数据写入
            Console.WriteLine("4. 验证数据库写入...");
            await VerifyDataWritten(testData);
            
            // 第五步：验证数据查询
            Console.WriteLine("5. 验证数据库查询...");
            await VerifyDataQueries(testData);
            
            // 第六步：验证JSON字段存储
            Console.WriteLine("6. 验证JSON字段存储...");
            await VerifyJsonFieldStorage(testData);
            
            // 第七步：验证数据更新
            Console.WriteLine("7. 验证数据更新功能...");
            await VerifyDataUpdates(testData);
            
            // 第八步：验证数据关联
            Console.WriteLine("8. 验证数据关联查询...");
            await VerifyDataRelations(testData);
            
            // 第九步：验证数据完整性
            Console.WriteLine("9. 验证数据完整性...");
            await VerifyDataIntegrity(testData);
            
            Console.WriteLine();
            Console.WriteLine("=== 数据库验证测试完成 ===");
            Console.WriteLine("✅ 所有数据库验证测试通过！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 数据库验证测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex.StackTrace}");
        }
        
        Console.WriteLine("\n按任意键继续...");
        Console.ReadKey();
    }

    /// <summary>
    /// 清理测试环境
    /// </summary>
    private static async Task CleanTestEnvironment()
    {
        try
        {
            var comfyUIManage = ComfyUIManage.Instance;
            
            // 获取所有测试相关的数据
            var allServers = comfyUIManage.GetAllServers();
            var allWorkflows = comfyUIManage.GetAllWorkflows();
            var allTasks = comfyUIManage.GetTasks();
            
            Console.WriteLine($"   发现现有数据: 服务器 {allServers.Count} 个, 工作流 {allWorkflows.Count} 个, 任务 {allTasks.Count} 个");
            
            // 删除测试相关的任务
            foreach (var task in allTasks)
            {
                if (task.taskName != null && task.taskName.Contains("测试"))
                {
                    // 这里可以添加删除逻辑，但为了保持数据完整性，我们暂时保留
                    Console.WriteLine($"   发现测试任务: {task.taskName}");
                }
            }
            
            Console.WriteLine("   ✅ 测试环境检查完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ⚠️ 清理测试环境时出现警告: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证数据库初始状态
    /// </summary>
    private static async Task VerifyInitialState()
    {
        var comfyUIManage = ComfyUIManage.Instance;
        
        // 检查数据库连接
        var servers = comfyUIManage.GetAllServers();
        var workflows = comfyUIManage.GetAllWorkflows();
        var tasks = comfyUIManage.GetTasks();
        
        Console.WriteLine($"   数据库连接正常");
        Console.WriteLine($"   当前数据统计:");
        Console.WriteLine($"   - 服务器: {servers.Count} 个");
        Console.WriteLine($"   - 工作流: {workflows.Count} 个");
        Console.WriteLine($"   - 任务: {tasks.Count} 个");
        Console.WriteLine("   ✅ 初始状态验证完成");
    }

    /// <summary>
    /// 测试数据结构
    /// </summary>
    public class TestDataSet
    {
        public string ServerId1 { get; set; } = "";
        public string ServerId2 { get; set; } = "";
        public string WorkflowId1 { get; set; } = "";
        public string WorkflowId2 { get; set; } = "";
        public string TaskId1 { get; set; } = "";
        public string TaskId2 { get; set; } = "";
        public DateTime TestStartTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 创建测试数据
    /// </summary>
    private static async Task<TestDataSet> CreateTestData()
    {
        var comfyUIManage = ComfyUIManage.Instance;
        var testData = new TestDataSet();
        
        Console.WriteLine("   创建测试服务器...");
        testData.ServerId1 = comfyUIManage.AddServer(
            "数据库验证测试服务器1", 
            "127.0.0.1", 
            8888, 
            3, 
            "用于数据库验证测试的主服务器"
        );
        
        testData.ServerId2 = comfyUIManage.AddServer(
            "数据库验证测试服务器2", 
            "127.0.0.1", 
            8189, 
            2, 
            "用于数据库验证测试的备用服务器"
        );
        
        Console.WriteLine($"   ✅ 创建服务器: {testData.ServerId1}, {testData.ServerId2}");
        
        Console.WriteLine("   创建测试工作流...");
        var workflowJson1 = JsonConvert.SerializeObject(new
        {
            nodes = new object[]
            {
                new { id = "1", type = "CheckpointLoaderSimple", inputs = new { ckpt_name = "v1-5-pruned-emaonly.ckpt" } },
                new { id = "2", type = "CLIPTextEncode", inputs = new { text = "beautiful landscape", clip = new object[] { "1", 1 } } },
                new { id = "3", type = "KSampler", inputs = new { seed = 123456, steps = 20, cfg = 8 } }
            },
            workflow_info = new { name = "测试工作流1", version = "1.0" }
        });
        
        testData.WorkflowId1 = comfyUIManage.AddWorkflow(
            "数据库验证测试工作流1",
            workflowJson1,
            "text2img",
            "用于数据库验证的文本转图片工作流",
            "测试用户"
        );
        
        var workflowJson2 = JsonConvert.SerializeObject(new
        {
            nodes = new object[]
            {
                new { id = "1", type = "CheckpointLoaderSimple", inputs = new { ckpt_name = "sd_xl_base_1.0.safetensors" } },
                new { id = "2", type = "CLIPTextEncode", inputs = new { text = "portrait of a person", clip = new object[] { "1", 1 } } },
                new { id = "3", type = "KSampler", inputs = new { seed = 654321, steps = 30, cfg = 7 } }
            },
            workflow_info = new { name = "测试工作流2", version = "2.0" }
        });
        
        testData.WorkflowId2 = comfyUIManage.AddWorkflow(
            "数据库验证测试工作流2",
            workflowJson2,
            "portrait",
            "用于数据库验证的人像生成工作流",
            "测试用户"
        );
        
        Console.WriteLine($"   ✅ 创建工作流: {testData.WorkflowId1}, {testData.WorkflowId2}");
        
        Console.WriteLine("   创建测试任务...");
        testData.TaskId1 = comfyUIManage.CreateTask(
            testData.WorkflowId1,
            "数据库验证测试任务1",
            "测试用户",
            5
        );
        
        testData.TaskId2 = comfyUIManage.CreateTask(
            testData.WorkflowId2,
            "数据库验证测试任务2",
            "测试用户",
            3
        );
        
        Console.WriteLine($"   ✅ 创建任务: {testData.TaskId1}, {testData.TaskId2}");
        Console.WriteLine("   ✅ 测试数据创建完成");
        
        return testData;
    }

    /// <summary>
    /// 验证数据写入
    /// </summary>
    private static async Task VerifyDataWritten(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;
        
        Console.WriteLine("   验证服务器数据写入...");
        var servers = comfyUIManage.GetAllServers();
        var server1 = servers.Find(s => s.id == testData.ServerId1);
        var server2 = servers.Find(s => s.id == testData.ServerId2);
        
        if (server1 == null || server2 == null)
        {
            throw new Exception("服务器数据写入验证失败：无法找到创建的服务器");
        }
        
        Console.WriteLine($"   ✅ 服务器1: {server1.serverName} - {server1.serverUrl}:{server1.port}");
        Console.WriteLine($"   ✅ 服务器2: {server2.serverName} - {server2.serverUrl}:{server2.port}");
        
        Console.WriteLine("   验证工作流数据写入...");
        var workflows = comfyUIManage.GetAllWorkflows();
        var workflow1 = workflows.Find(w => w.id == testData.WorkflowId1);
        var workflow2 = workflows.Find(w => w.id == testData.WorkflowId2);
        
        if (workflow1 == null || workflow2 == null)
        {
            throw new Exception("工作流数据写入验证失败：无法找到创建的工作流");
        }
        
        Console.WriteLine($"   ✅ 工作流1: {workflow1.workflowName} - {workflow1.workflowType}");
        Console.WriteLine($"   ✅ 工作流2: {workflow2.workflowName} - {workflow2.workflowType}");
        
        Console.WriteLine("   验证任务数据写入...");
        var tasks = comfyUIManage.GetTasks();
        var task1 = tasks.Find(t => t.id == testData.TaskId1);
        var task2 = tasks.Find(t => t.id == testData.TaskId2);
        
        if (task1 == null || task2 == null)
        {
            throw new Exception("任务数据写入验证失败：无法找到创建的任务");
        }
        
        Console.WriteLine($"   ✅ 任务1: {task1.taskName} - 状态:{task1.status} 进度:{task1.progress}%");
        Console.WriteLine($"   ✅ 任务2: {task2.taskName} - 状态:{task2.status} 进度:{task2.progress}%");
        Console.WriteLine("   ✅ 数据写入验证完成");
    }

    /// <summary>
    /// 验证数据查询功能
    /// </summary>
    private static async Task VerifyDataQueries(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;

        Console.WriteLine("   验证按ID查询功能...");

        // 验证服务器查询
        var server = comfyUIManage.GetServerById(testData.ServerId1);
        if (server == null)
        {
            throw new Exception($"按ID查询服务器失败: {testData.ServerId1}");
        }
        Console.WriteLine($"   ✅ 服务器查询: {server.serverName}");

        // 验证工作流查询
        var workflow = comfyUIManage.GetWorkflowById(testData.WorkflowId1);
        if (workflow == null)
        {
            throw new Exception($"按ID查询工作流失败: {testData.WorkflowId1}");
        }
        Console.WriteLine($"   ✅ 工作流查询: {workflow.workflowName}");

        // 验证任务查询
        var task = comfyUIManage.GetTaskById(testData.TaskId1);
        if (task == null)
        {
            throw new Exception($"按ID查询任务失败: {testData.TaskId1}");
        }
        Console.WriteLine($"   ✅ 任务查询: {task.taskName}");

        Console.WriteLine("   验证条件查询功能...");

        // 验证按状态查询任务
        var pendingTasks = comfyUIManage.GetTasksByStatus(0);
        Console.WriteLine($"   ✅ 按状态查询: 找到 {pendingTasks.Count} 个待处理任务");

        // 验证按类型查询工作流
        var textToImgWorkflows = comfyUIManage.GetWorkflowsByType("text2img");
        Console.WriteLine($"   ✅ 按类型查询: 找到 {textToImgWorkflows.Count} 个文本转图片工作流");

        // 验证在线服务器查询
        var onlineServers = comfyUIManage.GetOnlineServers();
        Console.WriteLine($"   ✅ 在线服务器查询: 找到 {onlineServers.Count} 个在线服务器");

        Console.WriteLine("   ✅ 数据查询验证完成");
    }

    /// <summary>
    /// 验证JSON字段存储
    /// </summary>
    private static async Task VerifyJsonFieldStorage(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;

        Console.WriteLine("   验证任务日志JSON存储...");

        // 添加多条日志
        var logEntries = new object[]
        {
            new { step = 1, node = "CheckpointLoaderSimple", message = "加载模型", timestamp = DateTime.Now },
            new { step = 2, node = "CLIPTextEncode", message = "编码文本", timestamp = DateTime.Now.AddSeconds(1) },
            new { step = 3, node = "KSampler", message = "开始采样", timestamp = DateTime.Now.AddSeconds(2) }
        };

        foreach (dynamic logEntry in logEntries)
        {
            var logJson = JsonConvert.SerializeObject(logEntry);
            var result = comfyUIManage.AddTaskLog(
                testData.TaskId1,
                logEntry.step,
                logEntry.message,
                logEntry.node,
                logEntry.node,
                logJson
            );

            if (!result)
            {
                throw new Exception($"添加任务日志失败: {logEntry.message}");
            }
        }

        Console.WriteLine($"   ✅ 添加了 {logEntries.Length} 条日志");

        Console.WriteLine("   验证任务文件JSON存储...");

        // 添加多个文件
        var fileEntries = new object[]
        {
            new { name = "output_001.png", path = "/outputs/output_001.png", size = 1024000, type = 0 },
            new { name = "output_002.png", path = "/outputs/output_002.png", size = 1536000, type = 0 },
            new { name = "workflow.json", path = "/temp/workflow.json", size = 2048, type = 1 }
        };

        foreach (dynamic fileEntry in fileEntries)
        {
            var fileId = comfyUIManage.AddTaskFile(
                testData.TaskId1,
                fileEntry.type,      // fileType (int)
                fileEntry.name,      // fileName (string)
                fileEntry.path,      // filePath (string)
                fileEntry.size       // fileSize (long)
            );

            if (string.IsNullOrEmpty(fileId))
            {
                throw new Exception($"添加任务文件失败: {fileEntry.name}");
            }
        }

        Console.WriteLine($"   ✅ 添加了 {fileEntries.Length} 个文件");

        // 验证JSON数据读取
        Console.WriteLine("   验证JSON数据读取...");
        var logsJson = comfyUIManage.GetTaskLogs(testData.TaskId1);
        var filesJson = comfyUIManage.GetTaskFiles(testData.TaskId1);

        // 解析JSON字符串为集合
        var logs = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(logsJson) ?? new List<System.Text.Json.JsonElement>();
        var files = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(filesJson) ?? new List<System.Text.Json.JsonElement>();

        Console.WriteLine($"   ✅ 读取到 {logs.Count} 条日志");
        Console.WriteLine($"   ✅ 读取到 {files.Count} 个文件");

        // 验证JSON内容
        if (logs.Count > 0)
        {
            var firstLog = logs[0];
            var message = firstLog.TryGetProperty("message", out var msgProp) ? msgProp.GetString() : "未知";
            var nodeName = firstLog.TryGetProperty("nodeName", out var nodeProp) ? nodeProp.GetString() : "未知";
            Console.WriteLine($"   ✅ 日志示例: {message} (节点: {nodeName})");
        }

        if (files.Count > 0)
        {
            var firstFile = files[0];
            var fileName = firstFile.TryGetProperty("fileName", out var nameProp) ? nameProp.GetString() : "未知";
            var fileSize = firstFile.TryGetProperty("fileSize", out var sizeProp) ? sizeProp.GetInt64() : 0;
            Console.WriteLine($"   ✅ 文件示例: {fileName} (大小: {fileSize} 字节)");
        }

        Console.WriteLine("   ✅ JSON字段存储验证完成");
    }

    /// <summary>
    /// 验证数据更新功能
    /// </summary>
    private static async Task VerifyDataUpdates(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;

        Console.WriteLine("   验证任务状态更新...");

        // 更新任务1状态为进行中
        var updateResult1 = comfyUIManage.UpdateTaskStatus(
            testData.TaskId1,
            1, // 进行中
            50, // 50%进度
            "KSampler",
            "正在采样"
        );

        if (!updateResult1)
        {
            throw new Exception("更新任务1状态失败");
        }

        // 更新任务2状态为已完成
        var updateResult2 = comfyUIManage.UpdateTaskStatus(
            testData.TaskId2,
            2, // 已完成
            100, // 100%进度
            "SaveImage",
            "保存图片完成"
        );

        if (!updateResult2)
        {
            throw new Exception("更新任务2状态失败");
        }

        Console.WriteLine("   ✅ 任务状态更新完成");

        // 验证更新结果
        Console.WriteLine("   验证更新结果...");
        var updatedTask1 = comfyUIManage.GetTaskById(testData.TaskId1);
        var updatedTask2 = comfyUIManage.GetTaskById(testData.TaskId2);

        if (updatedTask1 == null || updatedTask1.status != 1 || updatedTask1.progress != 50)
        {
            throw new Exception("任务1更新验证失败");
        }

        if (updatedTask2 == null || updatedTask2.status != 2 || updatedTask2.progress != 100)
        {
            throw new Exception("任务2更新验证失败");
        }

        Console.WriteLine($"   ✅ 任务1: 状态={updatedTask1.status}, 进度={updatedTask1.progress}%, 当前节点={updatedTask1.currentNode}");
        Console.WriteLine($"   ✅ 任务2: 状态={updatedTask2.status}, 进度={updatedTask2.progress}%, 当前节点={updatedTask2.currentNode}");

        Console.WriteLine("   ✅ 数据更新验证完成");
    }

    /// <summary>
    /// 验证数据关联查询
    /// </summary>
    private static async Task VerifyDataRelations(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;

        Console.WriteLine("   验证任务与工作流关联...");

        // 获取任务并验证其工作流关联
        var task1 = comfyUIManage.GetTaskById(testData.TaskId1);
        var task2 = comfyUIManage.GetTaskById(testData.TaskId2);

        if (task1 == null || task1.workflowId != testData.WorkflowId1)
        {
            throw new Exception("任务1与工作流关联验证失败");
        }

        if (task2 == null || task2.workflowId != testData.WorkflowId2)
        {
            throw new Exception("任务2与工作流关联验证失败");
        }

        Console.WriteLine($"   ✅ 任务1关联工作流: {task1.workflowId}");
        Console.WriteLine($"   ✅ 任务2关联工作流: {task2.workflowId}");

        Console.WriteLine("   验证任务与服务器关联...");

        // 验证任务分配的服务器
        if (string.IsNullOrEmpty(task1.serverId) || string.IsNullOrEmpty(task2.serverId))
        {
            throw new Exception("任务服务器关联验证失败");
        }

        Console.WriteLine($"   ✅ 任务1分配服务器: {task1.serverId}");
        Console.WriteLine($"   ✅ 任务2分配服务器: {task2.serverId}");

        Console.WriteLine("   验证工作流任务查询...");

        // 查询特定工作流的所有任务
        var workflow1Tasks = comfyUIManage.GetTasksByWorkflowId(testData.WorkflowId1);
        var workflow2Tasks = comfyUIManage.GetTasksByWorkflowId(testData.WorkflowId2);

        Console.WriteLine($"   ✅ 工作流1的任务数: {workflow1Tasks.Count}");
        Console.WriteLine($"   ✅ 工作流2的任务数: {workflow2Tasks.Count}");

        Console.WriteLine("   ✅ 数据关联验证完成");
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    private static async Task VerifyDataIntegrity(TestDataSet testData)
    {
        var comfyUIManage = ComfyUIManage.Instance;

        Console.WriteLine("   验证数据完整性...");

        // 获取所有数据进行完整性检查
        var allServers = comfyUIManage.GetAllServers();
        var allWorkflows = comfyUIManage.GetAllWorkflows();
        var allTasks = comfyUIManage.GetTasks();

        Console.WriteLine($"   数据库总计: 服务器 {allServers.Count} 个, 工作流 {allWorkflows.Count} 个, 任务 {allTasks.Count} 个");

        // 验证测试数据是否都存在
        var testServers = allServers.Where(s => s.serverName != null && s.serverName.Contains("数据库验证测试")).ToList();
        var testWorkflows = allWorkflows.Where(w => w.workflowName != null && w.workflowName.Contains("数据库验证测试")).ToList();
        var testTasks = allTasks.Where(t => t.taskName != null && t.taskName.Contains("数据库验证测试")).ToList();

        Console.WriteLine($"   测试数据: 服务器 {testServers.Count} 个, 工作流 {testWorkflows.Count} 个, 任务 {testTasks.Count} 个");

        if (testServers.Count < 2)
        {
            throw new Exception($"测试服务器数量不足: 期望2个, 实际{testServers.Count}个");
        }

        if (testWorkflows.Count < 2)
        {
            throw new Exception($"测试工作流数量不足: 期望2个, 实际{testWorkflows.Count}个");
        }

        if (testTasks.Count < 2)
        {
            throw new Exception($"测试任务数量不足: 期望2个, 实际{testTasks.Count}个");
        }

        Console.WriteLine("   验证时间戳完整性...");

        // 验证时间戳
        foreach (var task in testTasks)
        {
            if (task.CreateTime == null)
            {
                throw new Exception($"任务 {task.taskName} 缺少创建时间");
            }

            if (task.UpdateTime == null)
            {
                throw new Exception($"任务 {task.taskName} 缺少更新时间");
            }

            if (task.CreateTime > DateTime.Now)
            {
                throw new Exception($"任务 {task.taskName} 创建时间异常");
            }

            Console.WriteLine($"   ✅ 任务 {task.taskName}: 创建于 {task.CreateTime:yyyy-MM-dd HH:mm:ss}, 更新于 {task.UpdateTime:yyyy-MM-dd HH:mm:ss}");
        }

        Console.WriteLine("   验证JSON字段完整性...");

        // 验证JSON字段
        foreach (var task in testTasks)
        {
            if (!string.IsNullOrEmpty(task.Logs))
            {
                try
                {
                    var logsJson = comfyUIManage.GetTaskLogs(task.id);
                    var logs = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(logsJson) ?? new List<System.Text.Json.JsonElement>();
                    Console.WriteLine($"   ✅ 任务 {task.taskName} 日志解析正常: {logs.Count} 条");
                }
                catch (Exception ex)
                {
                    throw new Exception($"任务 {task.taskName} 日志JSON解析失败: {ex.Message}");
                }
            }

            if (!string.IsNullOrEmpty(task.Files))
            {
                try
                {
                    var filesJson = comfyUIManage.GetTaskFiles(task.id);
                    var files = System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(filesJson) ?? new List<System.Text.Json.JsonElement>();
                    Console.WriteLine($"   ✅ 任务 {task.taskName} 文件解析正常: {files.Count} 个");
                }
                catch (Exception ex)
                {
                    throw new Exception($"任务 {task.taskName} 文件JSON解析失败: {ex.Message}");
                }
            }
        }

        Console.WriteLine("   验证数据一致性...");

        // 验证数据一致性
        foreach (var task in testTasks)
        {
            // 验证工作流存在
            var workflow = allWorkflows.FirstOrDefault(w => w.id == task.workflowId);
            if (workflow == null)
            {
                throw new Exception($"任务 {task.taskName} 关联的工作流 {task.workflowId} 不存在");
            }

            // 验证服务器存在
            var server = allServers.FirstOrDefault(s => s.id == task.serverId);
            if (server == null)
            {
                throw new Exception($"任务 {task.taskName} 关联的服务器 {task.serverId} 不存在");
            }

            Console.WriteLine($"   ✅ 任务 {task.taskName} 数据一致性正常");
        }

        // 生成完整性报告
        Console.WriteLine("   生成完整性报告...");
        var report = new
        {
            测试时间 = DateTime.Now,
            数据统计 = new
            {
                总服务器数 = allServers.Count,
                总工作流数 = allWorkflows.Count,
                总任务数 = allTasks.Count,
                测试服务器数 = testServers.Count,
                测试工作流数 = testWorkflows.Count,
                测试任务数 = testTasks.Count
            },
            测试任务详情 = testTasks.Select(t => new
            {
                任务名称 = t.taskName,
                任务状态 = t.status,
                任务进度 = t.progress,
                创建时间 = t.CreateTime,
                更新时间 = t.UpdateTime,
                关联工作流 = t.workflowId,
                分配服务器 = t.serverId,
                日志条数 = !string.IsNullOrEmpty(t.Logs) ? (System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(comfyUIManage.GetTaskLogs(t.id)) ?? new List<System.Text.Json.JsonElement>()).Count : 0,
                文件数量 = !string.IsNullOrEmpty(t.Files) ? (System.Text.Json.JsonSerializer.Deserialize<List<System.Text.Json.JsonElement>>(comfyUIManage.GetTaskFiles(t.id)) ?? new List<System.Text.Json.JsonElement>()).Count : 0
            }).ToList()
        };

        var reportJson = JsonConvert.SerializeObject(report, Formatting.Indented);
        Console.WriteLine("   ✅ 完整性报告:");
        Console.WriteLine(reportJson);

        Console.WriteLine("   ✅ 数据完整性验证完成");
    }
}
