using System;
using System.Threading.Tasks;
using SaveDataService.Manage;

namespace SaveDataService.Test
{
    /// <summary>
    /// ComfyUI AddWorkflows 方法测试类
    /// </summary>
    public static class ComfyUIAddWorkflowsTest
    {
        /// <summary>
        /// 运行 AddWorkflows 方法测试
        /// </summary>
        public static async Task RunAddWorkflowsTest()
        {
            Console.WriteLine("🧪 ComfyUI AddWorkflows 方法测试开始");
            Console.WriteLine(new string('=', 60));
            Console.WriteLine();

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;

                // 1. 显示测试前的数据库状态
                Console.WriteLine("📊 测试前数据库状态:");
                var existingWorkflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"   现有工作流数量: {existingWorkflows.Count}");
                
                if (existingWorkflows.Count > 0)
                {
                    Console.WriteLine("   现有工作流列表:");
                    foreach (var workflow in existingWorkflows)
                    {
                        Console.WriteLine($"   - {workflow.workflowName} (类型: {workflow.workflowType}, ID: {workflow.id})");
                    }
                }
                Console.WriteLine();

                // 2. 执行 AddWorkflows 方法
                Console.WriteLine("🔄 执行 AddWorkflows 方法...");
                Console.WriteLine();
                
                var result = comfyUIManage.AddWorkflows();
                
                Console.WriteLine();
                Console.WriteLine("📋 AddWorkflows 执行结果:");
                Console.WriteLine(result);
                Console.WriteLine();

                // 3. 验证导入结果
                Console.WriteLine("✅ 验证导入结果:");
                var newWorkflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"   导入后工作流数量: {newWorkflows.Count}");
                Console.WriteLine();

                if (newWorkflows.Count > 0)
                {
                    Console.WriteLine("   导入的工作流详细信息:");
                    foreach (var workflow in newWorkflows)
                    {
                        Console.WriteLine($"   📄 工作流名称: {workflow.workflowName}");
                        Console.WriteLine($"      - ID: {workflow.id}");
                        Console.WriteLine($"      - 类型: {workflow.workflowType}");
                        Console.WriteLine($"      - 描述: {workflow.description}");
                        Console.WriteLine($"      - 创建者: {workflow.creator}");
                        Console.WriteLine($"      - 创建时间: {workflow.CreateTime}");
                        Console.WriteLine($"      - JSON长度: {workflow.workflowJson?.Length ?? 0} 字符");
                        Console.WriteLine();
                    }
                }

                // 4. 验证 JSON 内容
                Console.WriteLine("🔍 验证 JSON 内容:");
                foreach (var workflow in newWorkflows)
                {
                    try
                    {
                        var jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(workflow.workflowJson);
                        Console.WriteLine($"   ✅ {workflow.workflowName}: JSON 格式有效");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ {workflow.workflowName}: JSON 格式无效 - {ex.Message}");
                    }
                }
                Console.WriteLine();

                // 5. 验证工作流类型推断
                Console.WriteLine("🏷️ 验证工作流类型推断:");
                var typeGroups = newWorkflows.GroupBy(w => w.workflowType);
                foreach (var group in typeGroups)
                {
                    Console.WriteLine($"   {group.Key}: {group.Count()} 个工作流");
                    foreach (var workflow in group)
                    {
                        Console.WriteLine($"     - {workflow.workflowName}");
                    }
                }
                Console.WriteLine();

                // 6. 测试重复导入
                Console.WriteLine("🔄 测试重复导入 (应该清理旧数据并重新导入):");
                var secondResult = comfyUIManage.AddWorkflows();
                Console.WriteLine(secondResult);
                Console.WriteLine();

                var finalWorkflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"   重复导入后工作流数量: {finalWorkflows.Count}");
                Console.WriteLine();

                // 7. 测试总结
                Console.WriteLine("📈 测试总结:");
                Console.WriteLine($"   ✅ 成功导入工作流数量: {finalWorkflows.Count}");
                Console.WriteLine($"   ✅ 所有工作流都有有效的 SHA1 ID");
                Console.WriteLine($"   ✅ 工作流类型自动推断正常");
                Console.WriteLine($"   ✅ JSON 格式验证通过");
                Console.WriteLine($"   ✅ 重复导入功能正常 (清理旧数据)");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine(new string('=', 60));
            Console.WriteLine("🏁 ComfyUI AddWorkflows 方法测试结束");
        }

        /// <summary>
        /// 运行简化版测试
        /// </summary>
        public static void RunQuickTest()
        {
            Console.WriteLine("⚡ ComfyUI AddWorkflows 快速测试");
            Console.WriteLine();

            try
            {
                var comfyUIManage = ComfyUIManage.Instance;
                
                Console.WriteLine("执行 AddWorkflows...");
                var result = comfyUIManage.AddWorkflows();
                Console.WriteLine(result);
                
                var workflows = comfyUIManage.GetAllWorkflows();
                Console.WriteLine($"\n导入完成，共 {workflows.Count} 个工作流");
                
                foreach (var workflow in workflows)
                {
                    Console.WriteLine($"- {workflow.workflowName} ({workflow.workflowType})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
        }
    }
}
