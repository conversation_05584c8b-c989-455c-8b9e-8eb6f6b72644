﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using System.Reflection;
using static AISDKWebSocket.HttpManager;
using static System.Net.Mime.MediaTypeNames;

namespace AISdk.Manage
{
	internal class QwenlongADK : AISDKProvider
	{
		private string url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
		private string key = "Bearer sk-35b4f54ced9a404fa05fa939da455e4a";
		public string Model;
		public QwenlongADK(string userMode)
		{
			this.Model = userMode;
        }
		public void SendTextToAIModel(List<string> text, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			for (int i = 0; i < text.Count; i++)
			{
				if (i % 2 != 0)
				{
					messages.Add(new Message { role = "assistant", content = text[i] });
				}
				else
				{
					messages.Add(new Message { role = "user", content = text[i] });
				}
			}
			if (string.IsNullOrEmpty(Model))
			{
				Model = "qwen-max";
			}

			var data = new KuaiShuData(Model, "user", messages, true);

			//string jsonData = JsonConvert.SerializeObject(data);

			Instance.HttpsWeb(url, key, data, (backText, bol) =>
			{
				// UnityEngine.Debug.Log(backText);
				if (!bol)
				{
					backText = this.Model + ": " + backText;
				}
				callback(backText, bol);
			});
		}

        //根据输入的图像内容、视频内容和自然语言指令完成任务
        public void SendImageOrVideoToAiModle(string videoBase64Str, string requestDescrib, Action<string, bool> callback)
        {
            if (string.IsNullOrEmpty(Model))
            {
                Model = "qwen-max";
            }
            string backText = Model + ": " + "暂不支持分析视频，请切换至智谱大模型";
            callback(backText, false);
        }
		public void ChatWithAi(string prompt, Action<string, bool> callback)
		{
			List<Message> messages = new List<Message>();
			messages.Add(new Message { role = "user", content = prompt });
			if (string.IsNullOrEmpty(Model))
			{
				Model = "qwen-max";
			}
            var data = new KuaiShuData(Model, "user", messages, true);
            Instance.HttpsWeb(url, key, data, (backText, bol) =>
            {
                callback(backText, bol);
            });
        }

        public async Task<AiChatBase> KernelFunAsync()
        {
			var kernel = Kernel.CreateBuilder()
	.AddOpenAIChatCompletion(modelId: "qwen-max",       // 千问模型，如 qwen-turbo、qwen-max 等
		apiKey: "sk-35b4f54ced9a404fa05fa939da455e4a",  // DashScope API Key（需开通服务）
		endpoint: new Uri("https://dashscope.aliyuncs.com/compatible-mode/v1")
		)
	.Build();

            AiChatBase data = new AiChatBase();
            data.ChatService = kernel.GetRequiredService<IChatCompletionService>();
            data.chatHistory = new ChatHistory();
			try
			{
                data.chatHistory.AddUserMessage("你好，介绍一下你自己");
                Console.WriteLine("qwen: 你好，介绍一下你自己\n");
                await foreach (var chunk in data.ChatService.GetStreamingChatMessageContentsAsync(data.chatHistory))
                {
                    Console.Write(chunk.Content);
                }
            }
			catch (Exception ex) {
				Console.WriteLine(ex.ToString());
			}

            return data;
        }
    }
}
