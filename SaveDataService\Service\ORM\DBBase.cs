﻿
using GameServer.ExcelData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using MySqlConnector;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace GameServer.ORM
{
  public  class DBBase : DbContext
    {

        public static string mysqlConnectString = "";
        public static bool ormLogState = false;

        /// <summary>
        /// orm连接到mysql数据库中
        /// </summary>
        /// <param name="optionsBuilder"></param>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseMySql(AppConfig.Instance.mysqlConnectString, MySqlServerVersion.LatestSupportedServerVersion);
            
            if (ormLogState)
            {
                optionsBuilder.LogTo(Console.WriteLine);
                optionsBuilder.EnableSensitiveDataLogging();
            }


        }

        ///// <summary>
        ///// 拦截db数据发生变化的内容 同时把内容同步到redis当中
        ///// </summary>
        //private void HookORMDateChange()
        //{

        //    //添加操作
        //    ChangeTracker.Entries().Where(e => e.State == EntityState.Added && e.Entity is EntityBase).ToList()
        //        .ForEach(e => ((EntityBase)e.Entity).CreateTime = DateTime.Now);

        //    //修改操作
        //    ChangeTracker.Entries().Where(e => e.State == EntityState.Modified && e.Entity is EntityBase).ToList()
        //        .ForEach(e => ((EntityBase)e.Entity).UpdateTime = DateTime.Now);






        //    foreach (var item in ChangeTracker.Entries())
        //    {
        //        var changeName = item.Entity.GetType().Name;
        //        dynamic entity = item.Entity;
        //        //Console.WriteLine($"{item.Entity.GetType().Name} - >{item.State}");
        //        switch (item.State)
        //        {
        //            case EntityState.Modified:
        //                Redis.Instance.Update($"ORM_{changeName}_id_{entity.id}", entity);
        //                //Redis.Instance.Update($"ORM_{changeName}_dbid_{entity.dbID}", entity);
        //                break;
        //            case EntityState.Deleted:
        //                Redis.Instance.Delect($"ORM_{changeName}_id_{entity.id}");
        //                //Redis.Instance.Delect($"ORM_{changeName}_dbid_{entity.dbID}");
        //                break;
        //        }
        //    }
        //}

        //public override int SaveChanges()
        //{

        //    //HookORMDateChange();
        //    return base.SaveChanges();
        //}
        //public override int SaveChanges(bool acceptAllChangesOnSuccess)
        //{
        //    //HookORMDateChange();
        //    return base.SaveChanges(acceptAllChangesOnSuccess);
        //}
        //public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        //{
        //    //HookORMDateChange();
        //    return base.SaveChangesAsync(cancellationToken);
        //}
        //public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
        //{
        //    //HookORMDateChange();
        //    return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        //}


    }
}
