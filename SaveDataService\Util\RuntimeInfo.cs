﻿using System;
using System.Diagnostics;
using static System.Net.Mime.MediaTypeNames;

public class Computer
{
    /// <summary>  
    /// CPU序列号  
    /// </summary>  
    public string cpuID;
    /// <summary>  
    /// 网卡/Mac地址  
    /// </summary>  
    public string macAddress;
    /// <summary>  
    /// 硬盘ID  
    /// </summary>  
    public string diskID;
    /// <summary>  
    /// IP地址  
    /// </summary>  
    public string ipAddress;
    /// <summary>  
    /// 系统登录用户名  
    /// </summary>  
    public string loginUserName;
    /// <summary>  
    /// 系统名称  
    /// </summary>  
    public string computerName;
    /// <summary>  
    /// 系统型号  
    /// </summary>  
    public string systemType;
    /// <summary>  
    /// 物理内存（单位b）  
    /// </summary>  
    public string totalPhysicalMemory;
    /// <summary>
    /// 系统唯一ID
    /// </summary>
    public string sysID;
    /// <summary>
    /// 主板序列号BiosID
    /// </summary>
    public string smBisoUUID;
}