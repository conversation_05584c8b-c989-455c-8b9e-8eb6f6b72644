# ComfyUI 文件自动下载功能

## 功能概述

当ComfyUI工作流执行完成后，系统会自动将输出的文件下载到本地，支持各种文件类型：

- 📸 **图片文件**: PNG, JPG, JPEG, GIF, BMP, TIFF, WebP, SVG, ICO, TGA, EXR, HDR
- 🎬 **视频文件**: MP4, AVI, MOV, WMV, FLV, MKV, WebM, M4V, 3GP, OGV, TS, MTS
- 🎨 **3D模型文件**: FBX, OBJ, DAE, 3DS, Blend, MAX, MA, MB, C4D, LWO, X3D, PLY, STL, GLTF, GLB
- 🎵 **音频文件**: WAV, MP3, FLAC, AAC, OGG, WMA, M4A, Opus, AIFF, AU
- 📄 **文档文件**: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, ODT, ODS, ODP
- 🗜️ **压缩包**: ZIP, RAR, 7Z, TAR, GZ, BZ2, XZ, LZMA, CAB, ISO
- 🎮 **游戏资源**: Unity, UnityPackage, UAsset, UMap
- 🎨 **设计文件**: PSD, AI, EPS, Sketch
- 📝 **文本文件**: TXT, JSON, XML, CSV, YAML, YML, INI, CFG, CONF, LOG, MD, RST
- 🔤 **字体文件**: TTF, OTF, WOFF, WOFF2, EOT
- 💾 **数据库文件**: DB, SQLite, SQL, MDB, ACCDB
- 📜 **脚本文件**: PY, JS, TS, PHP, RB, PL, SH, BAT, PS1

## 配置文件

系统会自动生成 `comfyui_download_config.json` 配置文件，您可以自定义以下设置：

### 基本设置

```json
{
  "DownloadRootPath": "Downloads",              // 下载根目录
  "CreateTaskSubdirectory": true,               // 是否按任务ID创建子目录
  "CreateTypeSubdirectory": false,              // 是否按文件类型创建子目录
  "CreateDateSubdirectory": false,              // 是否按日期创建子目录
  "MaxConcurrentDownloads": 3,                  // 最大并发下载数
  "DownloadTimeoutSeconds": 300                 // 下载超时时间（秒）
}
```

### 重试设置

```json
{
  "AutoRetryFailedDownloads": true,             // 是否自动重试失败的下载
  "MaxRetryAttempts": 3,                        // 最大重试次数
  "RetryDelayMs": 1000                          // 重试间隔（毫秒）
}
```

### 文件名设置

```json
{
  "KeepOriginalFilename": true,                 // 是否保留原始文件名
  "FilenamePrefix": "",                         // 文件名前缀
  "FilenameSuffix": "",                         // 文件名后缀
}
```

### 文件过滤设置

```json
{
  "SupportedFileTypes": [],                     // 支持的文件类型（空表示全部）
  "ExcludedFileTypes": [],                      // 排除的文件类型
  "MaxFileSizeMB": 0,                          // 最大文件大小限制（MB，0表示无限制）
  "ValidateFileIntegrity": false                // 是否验证文件完整性
}
```

## 目录结构示例

### 默认结构（按任务ID分组）
```
Downloads/
└── ComfyUI/
    ├── task-001/
    │   ├── output_image_001.png
    │   ├── output_video_001.mp4
    │   └── model_001.fbx
    └── task-002/
        ├── result_001.png
        └── animation_001.mp4
```

### 按文件类型分组
```
Downloads/
└── ComfyUI/
    ├── task-001/
    │   ├── 图片/
    │   │   └── output_image_001.png
    │   ├── 视频/
    │   │   └── output_video_001.mp4
    │   └── 3D模型/
    │       └── model_001.fbx
```

### 按日期分组
```
Downloads/
├── 2024-01-15/
│   └── ComfyUI/
│       └── task-001/
│           ├── output_image_001.png
│           └── output_video_001.mp4
└── 2024-01-16/
    └── ComfyUI/
        └── task-002/
            └── result_001.png
```

## 日志输出示例

### 文件下载开始
```
📥 开始下载文件 [任务: abc123-def456]
   📄 文件名: ComfyUI_output_00001_.png
   🏷️  文件类型: 图片
   🔗 下载URL: http://127.0.0.1:8888/view?filename=ComfyUI_output_00001_.png&subfolder=&type=output
   ⏰ 开始时间: 2024-01-15 14:30:25.123
```

### 文件下载成功
```
✅ 文件下载成功 [任务: abc123-def456]
   📄 文件名: ComfyUI_output_00001_.png
   📁 本地路径: F:\Downloads\ComfyUI\abc123-def456\ComfyUI_output_00001_.png
   📊 文件大小: 2.45 MB
   ⏱️  下载时间: 1.23秒
   📈 下载速度: 1.99 MB/s
   ⏰ 完成时间: 2024-01-15 14:30:26.356
```

### 批量下载完成
```
📦 批量文件下载完成 [任务: abc123-def456]
   📊 总文件数: 3
   ✅ 成功下载: 3
   ❌ 下载失败: 0
   📈 成功率: 100.0%
   📁 下载的文件:
      📄 ComfyUI_output_00001_.png (2.45 MB)
      📄 animation_output_00001_.mp4 (15.67 MB)
      📄 model_export_00001_.fbx (8.92 MB)
   ⏰ 完成时间: 2024-01-15 14:30:28
```

## 使用方法

1. **自动下载**: 当ComfyUI工作流完成时，系统会自动下载所有输出文件
2. **配置自定义**: 修改 `comfyui_download_config.json` 文件来自定义下载行为
3. **查看日志**: 观察控制台输出了解下载进度和结果
4. **查找文件**: 在配置的下载目录中找到下载的文件

## 特性

- ✅ **自动识别文件类型**: 支持50+种文件格式
- ✅ **智能目录管理**: 可按任务、类型、日期分组
- ✅ **并发下载**: 支持多文件同时下载
- ✅ **断点续传**: 自动重试失败的下载
- ✅ **文件过滤**: 可配置支持/排除的文件类型
- ✅ **大小限制**: 可设置最大文件大小
- ✅ **详细日志**: 完整的下载过程记录
- ✅ **文件验证**: 可选的文件完整性检查
- ✅ **自定义命名**: 支持文件名前缀/后缀

## 注意事项

1. 确保有足够的磁盘空间存储下载的文件
2. 网络连接稳定时下载效果更好
3. 大文件下载可能需要较长时间
4. 配置文件修改后需要重启程序生效
5. 下载失败的文件会自动重试（如果启用）

## 故障排除

### 下载失败
- 检查网络连接
- 确认ComfyUI服务器可访问
- 检查磁盘空间是否充足
- 查看错误日志了解具体原因

### 文件未下载
- 检查文件类型是否在支持列表中
- 确认文件大小未超过限制
- 查看配置文件设置是否正确

### 目录问题
- 确保有写入权限
- 检查路径设置是否正确
- 验证目录结构配置
