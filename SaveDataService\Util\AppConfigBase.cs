﻿using GameServer.Util;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;

namespace ToolsService
{
    public abstract class AppConfigBase
    {
        
        protected static AppConfigBase _instance;
        public AppConfigBase()
        {
            Init();
        }

        public void reLoad()
        {
            Init();
        }

        protected void Init()
        {
           


            JsonSerializerSettings seting = new JsonSerializerSettings();
            string configName = $"{GetType().Assembly.GetName().Name}.appconfig.json";
            string fullPath = Path.GetFullPath($"./{configName}");

            fullPath = PatchUtil.getFilePath()+ configName;

            //Console.WriteLine("取到的配置路径 "+ fullPath);

            if (File.Exists(fullPath))
            {
                string config = File.ReadAllText(fullPath);
                JsonConvert.PopulateObject(config, this, seting);
                return;
            }

            var savetext = JsonConvert.SerializeObject(this, seting);
           // File.WriteAllText(fullPath, savetext);
        }

        public void save()
        {
            return;
            JsonSerializerSettings seting = new JsonSerializerSettings();
            string configName = $"{GetType().Assembly.GetName().Name}.appconfig.json";
            string fullPath = Path.GetFullPath($"./{configName}");
            fullPath = PatchUtil.getFilePath() + configName;
            var savetext = JsonConvert.SerializeObject(this, seting);
            File.WriteAllText(fullPath, savetext);
        }
    }
}
